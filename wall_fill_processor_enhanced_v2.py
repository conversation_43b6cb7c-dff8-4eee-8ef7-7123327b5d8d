#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版墙体填充处理器 V2
实现：
1. 对组内外轮廓使用目前逻辑填充
2. 对内含有其他封闭线条的，扣除此区域的填充
"""

import numpy as np
import math
from shapely.geometry import Polygon, LineString, Point, MultiPolygon
from shapely.ops import unary_union, polygonize
from shapely.geometry import MultiLineString
import matplotlib.patches as patches
try:
    import rtree  # 用于加速空间查询
    RTREE_AVAILABLE = True
except ImportError:
    RTREE_AVAILABLE = False

class EnhancedWallFillProcessorV2:
    """增强版墙体填充处理器 V2"""
    
    def __init__(self):
        # 墙体图层模式
        self.wall_layer_patterns = [
            '墙', '墙体', '结构墙', '承重墙', '隔墙', '内墙', '外墙', '分隔墙', '主墙', '次墙',
            'wall', 'walls', 'partition', 'struct', 'structure', 'bearing', 'load',
            'exterior', 'interior', 'divider', 'separator',
            'A-WALL', 'AWALL', 'A_WALL', 'WALL_', '_WALL', 'STR-WALL', 'ARCH-WALL',
            'PARTITION', 'STRUCT-WALL', 'BEARING-WALL', 'LOAD-WALL',
            'masonry', 'concrete', 'brick', '砖墙', '混凝土', '砌体',
            '01-墙', '02-墙', 'L-墙', 'S-墙', 'A-墙', 'AR-墙'
        ]
        
        # 填充颜色设置
        self.fill_color = '#808080'  # 深灰色
        self.cavity_color = 'white'   # 空腔为白色
        self.fill_alpha = 0.6         # 填充透明度
        
        # 处理参数
        self.connection_threshold = 20  # 连接阈值
        self.min_polygon_area = 10.0   # 最小多边形面积
        self.buffer_distance = 1.0     # 缓冲区距离

    def _safe_get_layer_name(self, entity):
        """安全获取图层名称，处理各种数据类型"""
        try:
            layer = entity.get('layer', '')

            # 处理不同的数据类型
            if isinstance(layer, str):
                return layer.lower()
            elif isinstance(layer, (int, float)):
                return str(layer).lower()
            elif layer is None:
                return ''
            else:
                # 处理其他复杂对象
                return str(layer).lower()

        except Exception as e:
            # 如果所有方法都失败，返回空字符串
            return ''

    def identify_wall_entities(self, entities):
        """识别墙体实体 - 基于图层和几何关系"""
        wall_entities = []

        # 首先按图层识别
        for entity in entities:
            layer = self._safe_get_layer_name(entity)
            if any(pattern.lower() in layer for pattern in self.wall_layer_patterns):
                wall_entities.append(entity)
        
        print(f"按图层识别的墙体实体数量: {len(wall_entities)}")
        
        # 如果按图层识别到的实体太少，尝试基于几何关系识别
        if len(wall_entities) < 5:  # 假设至少需要5个实体才能形成墙体
            print("按图层识别的墙体实体较少，尝试基于几何关系识别...")
            wall_entities = self._identify_walls_by_geometry(entities)
            print(f"基于几何关系识别的墙体实体数量: {len(wall_entities)}")
        
        return wall_entities
    
    def _identify_walls_by_geometry(self, entities):
        """基于几何关系识别墙体实体（增强版V2：支持复杂组合线条和多种识别策略）"""
        wall_entities = []

        # 🔧 收集所有线条类型的实体（扩展支持）
        line_entities = [e for e in entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE', 'ARC', 'CIRCLE']]
        print(f"找到线条类型实体数量: {len(line_entities)} (LINE, LWPOLYLINE, POLYLINE, ARC, CIRCLE)")

        if len(line_entities) < 3:
            return wall_entities

        # 🔧 策略1：使用多种连接阈值进行分析
        connection_thresholds = [2, 5, 10, 20, 50, 100]  # 从严格到宽松，增加更多阈值
        best_groups = []

        for threshold in connection_thresholds:
            print(f"尝试连接阈值: {threshold}")
            connected_groups = self._find_connected_line_groups(line_entities, threshold)
            print(f"  找到连接组数量: {len(connected_groups)}")

            # 评估组的质量
            quality_groups = []
            for i, group in enumerate(connected_groups):
                if len(group) >= 3:  # 至少3个线段才能形成封闭区域
                    is_closed = self._is_closed_line_group(group, threshold)
                    coverage = self._calculate_group_coverage(group)
                    density = self._calculate_group_density(group)
                    regularity = self._calculate_group_regularity(group)

                    # 🔧 增强质量评分算法
                    quality_score = (
                        len(group) * 2 +  # 实体数量权重
                        (50 if is_closed else 0) +  # 封闭性奖励
                        coverage * 30 +  # 覆盖率权重
                        density * 20 +  # 密度权重
                        regularity * 10  # 规律性权重
                    )

                    quality_groups.append({
                        'group': group,
                        'size': len(group),
                        'is_closed': is_closed,
                        'coverage': coverage,
                        'density': density,
                        'regularity': regularity,
                        'quality_score': quality_score,
                        'threshold': threshold
                    })
                    print(f"  组 {i+1}: {len(group)}个实体, 封闭={is_closed}, 覆盖率={coverage:.2f}, 密度={density:.2f}, 规律性={regularity:.2f}, 质量分={quality_score:.2f}")

            if quality_groups:
                # 按质量分排序
                quality_groups.sort(key=lambda x: x['quality_score'], reverse=True)

                # 如果找到高质量组，保存并继续寻找更好的
                if not best_groups or quality_groups[0]['quality_score'] > best_groups[0]['quality_score']:
                    best_groups = quality_groups
                    print(f"  更新最佳组质量分: {best_groups[0]['quality_score']:.2f}")

        # 🔧 策略2：基于图层名称的补充识别
        layer_based_entities = self._identify_walls_by_layer_patterns(entities)
        if layer_based_entities:
            print(f"基于图层模式识别到 {len(layer_based_entities)} 个墙体实体")
            wall_entities.extend(layer_based_entities)

        # 🔧 策略3：选择多个高质量的墙体组（不只是第一个）
        selected_count = 0
        for group_info in best_groups[:5]:  # 最多选择5个最佳组
            group = group_info['group']
            # 🔧 动态质量分阈值
            min_quality = max(15, best_groups[0]['quality_score'] * 0.3) if best_groups else 15

            if group_info['quality_score'] >= min_quality:
                print(f"选择墙体组: {len(group)}个实体, 质量分={group_info['quality_score']:.2f}, 阈值={group_info['threshold']}")

                # 避免重复添加相同的实体
                new_entities = [e for e in group if e not in wall_entities]
                wall_entities.extend(new_entities)
                selected_count += 1

                if selected_count >= 3:  # 限制最多3个组
                    break

        # 🔧 策略4：基于实体长度的补充识别
        length_based_entities = self._identify_walls_by_length(entities)
        if length_based_entities:
            print(f"基于长度特征识别到 {len(length_based_entities)} 个墙体实体")
            new_entities = [e for e in length_based_entities if e not in wall_entities]
            wall_entities.extend(new_entities)

        print(f"几何识别最终结果: {len(wall_entities)} 个墙体实体")
        return wall_entities

    def _calculate_group_density(self, group):
        """计算组的密度（新增方法）"""
        try:
            if len(group) < 2:
                return 0.0

            # 计算组的边界框
            all_points = []
            for entity in group:
                points = self._get_entity_endpoints(entity)
                all_points.extend(points)

            if len(all_points) < 2:
                return 0.0

            # 计算边界框面积
            x_coords = [p[0] for p in all_points]
            y_coords = [p[1] for p in all_points]
            bbox_area = (max(x_coords) - min(x_coords)) * (max(y_coords) - min(y_coords))

            if bbox_area == 0:
                return 0.0

            # 计算实体总长度
            total_length = 0
            for entity in group:
                length = self._calculate_entity_length(entity)
                total_length += length

            # 密度 = 总长度 / 边界框面积
            density = total_length / bbox_area if bbox_area > 0 else 0
            return min(density, 1.0)  # 限制在0-1之间

        except Exception as e:
            print(f"计算组密度失败: {e}")
            return 0.0

    def _calculate_group_regularity(self, group):
        """计算组的规律性（新增方法）"""
        try:
            if len(group) < 3:
                return 0.0

            # 计算角度分布的规律性
            angles = []
            for i in range(len(group)):
                entity1 = group[i]
                entity2 = group[(i + 1) % len(group)]

                angle = self._calculate_angle_between_entities(entity1, entity2)
                if angle is not None:
                    angles.append(angle)

            if len(angles) < 2:
                return 0.0

            # 计算角度的标准差，标准差越小规律性越高
            import statistics
            angle_std = statistics.stdev(angles) if len(angles) > 1 else 0

            # 规律性分数：标准差越小分数越高
            regularity = max(0, 1 - angle_std / 90)  # 90度为最大可能标准差
            return regularity

        except Exception as e:
            print(f"计算组规律性失败: {e}")
            return 0.0

    def _identify_walls_by_layer_patterns(self, entities):
        """基于图层模式识别墙体（新增方法）"""
        try:
            wall_entities = []

            # 扩展的图层模式
            extended_patterns = [
                'wall', 'walls', '墙', '墙体', 'a-wall', 'a_wall',
                'partition', '隔墙', 'structure', '结构',
                'outline', '轮廓', 'boundary', '边界'
            ]

            for entity in entities:
                layer_name = str(entity.get('layer', '')).lower()
                entity_type = entity.get('type', '')

                # 检查图层名称
                if any(pattern in layer_name for pattern in extended_patterns):
                    wall_entities.append(entity)
                # 检查特定类型的实体
                elif entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE'] and self._is_likely_wall_entity(entity):
                    wall_entities.append(entity)

            return wall_entities

        except Exception as e:
            print(f"基于图层模式识别墙体失败: {e}")
            return []

    def _identify_walls_by_length(self, entities):
        """基于长度特征识别墙体（新增方法）"""
        try:
            wall_entities = []

            # 计算所有实体的长度
            entity_lengths = []
            for entity in entities:
                if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    length = self._calculate_entity_length(entity)
                    if length > 0:
                        entity_lengths.append((entity, length))

            if not entity_lengths:
                return wall_entities

            # 按长度排序
            entity_lengths.sort(key=lambda x: x[1], reverse=True)

            # 选择较长的实体作为墙体候选
            total_entities = len(entity_lengths)
            if total_entities > 10:
                # 选择前30%的长实体
                top_count = max(3, total_entities // 3)
                for entity, length in entity_lengths[:top_count]:
                    if length > 50:  # 长度阈值
                        wall_entities.append(entity)

            return wall_entities

        except Exception as e:
            print(f"基于长度特征识别墙体失败: {e}")
            return []

    def _is_likely_wall_entity(self, entity):
        """判断实体是否可能是墙体（新增方法）"""
        try:
            # 检查实体长度
            length = self._calculate_entity_length(entity)
            if length < 20:  # 太短的线段不太可能是墙体
                return False

            # 检查实体是否为水平或垂直线
            if entity.get('type') == 'LINE':
                points = entity.get('points', [])
                if len(points) >= 2:
                    start, end = points[0], points[1]
                    dx = abs(end[0] - start[0])
                    dy = abs(end[1] - start[1])

                    # 水平或垂直线更可能是墙体
                    if dx < 5 or dy < 5:  # 容差5个单位
                        return True

                    # 检查角度是否接近45度的倍数
                    import math
                    angle = math.atan2(dy, dx) * 180 / math.pi
                    angle_mod = angle % 45
                    if angle_mod < 5 or angle_mod > 40:  # 接近45度倍数
                        return True

            return False

        except Exception as e:
            print(f"判断实体是否为墙体失败: {e}")
            return False

    def _calculate_entity_length(self, entity):
        """计算实体长度（新增方法）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE':
                points = entity.get('points', [])
                if len(points) >= 2:
                    start, end = points[0], points[1]
                    import math
                    return math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if len(points) >= 2:
                    total_length = 0
                    for i in range(len(points) - 1):
                        start, end = points[i], points[i + 1]
                        import math
                        length = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
                        total_length += length
                    return total_length

            elif entity_type == 'CIRCLE':
                radius = entity.get('radius', 0)
                return 2 * 3.14159 * radius

            elif entity_type == 'ARC':
                radius = entity.get('radius', 0)
                start_angle = entity.get('start_angle', 0)
                end_angle = entity.get('end_angle', 0)
                angle_diff = abs(end_angle - start_angle)
                return radius * angle_diff * 3.14159 / 180

            return 0

        except Exception as e:
            print(f"计算实体长度失败: {e}")
            return 0

    def _calculate_angle_between_entities(self, entity1, entity2):
        """计算两个实体之间的角度（新增方法）"""
        try:
            # 获取实体的方向向量
            def get_direction_vector(entity):
                points = self._get_entity_endpoints(entity)
                if len(points) >= 2:
                    start, end = points[0], points[1]
                    dx = end[0] - start[0]
                    dy = end[1] - start[1]
                    import math
                    length = math.sqrt(dx*dx + dy*dy)
                    if length > 0:
                        return (dx/length, dy/length)
                return None

            vec1 = get_direction_vector(entity1)
            vec2 = get_direction_vector(entity2)

            if vec1 and vec2:
                import math
                dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
                dot_product = max(-1, min(1, dot_product))  # 限制在[-1, 1]范围内
                angle = math.acos(dot_product) * 180 / math.pi
                return angle

            return None

        except Exception as e:
            print(f"计算实体间角度失败: {e}")
            return None

    def _find_connected_line_groups(self, line_entities, connection_threshold=10):
        """查找连接的线段组（支持可调阈值）"""
        groups = []
        used_entities = set()

        for i, entity in enumerate(line_entities):
            if i in used_entities:
                continue

            # 创建新组
            group = [entity]
            used_entities.add(i)

            # 查找连接的实体
            self._find_connected_lines(line_entities, entity, group, used_entities, connection_threshold)

            if len(group) >= 2:
                groups.append(group)

        # 按组大小排序
        groups.sort(key=len, reverse=True)
        return groups
    
    def _find_connected_lines(self, entities, current_entity, group, used_entities, connection_threshold=10):
        """递归查找连接的线段（支持可调阈值）"""
        current_points = self._get_entity_endpoints(current_entity)

        for i, entity in enumerate(entities):
            if i in used_entities:
                continue

            entity_points = self._get_entity_endpoints(entity)

            # 检查连接性
            if self._are_entities_connected(current_points, entity_points, connection_threshold):
                group.append(entity)
                used_entities.add(i)
                # 递归查找更多连接的实体
                self._find_connected_lines(entities, entity, group, used_entities, connection_threshold)

    def _calculate_group_coverage(self, group):
        """计算组的覆盖率（基于边界框面积）"""
        try:
            if not group:
                return 0.0

            # 收集所有点
            all_points = []
            for entity in group:
                points = self._get_entity_all_points(entity)
                all_points.extend(points)

            if len(all_points) < 3:
                return 0.0

            # 计算边界框
            min_x = min(p[0] for p in all_points)
            max_x = max(p[0] for p in all_points)
            min_y = min(p[1] for p in all_points)
            max_y = max(p[1] for p in all_points)

            bbox_area = (max_x - min_x) * (max_y - min_y)

            # 计算线条总长度
            total_length = 0
            for entity in group:
                points = self._get_entity_all_points(entity)
                for i in range(len(points) - 1):
                    dx = points[i+1][0] - points[i][0]
                    dy = points[i+1][1] - points[i][1]
                    total_length += (dx*dx + dy*dy)**0.5

            # 覆盖率 = 线条密度
            if bbox_area > 0:
                coverage = min(total_length / (bbox_area**0.5), 1.0)
            else:
                coverage = 0.0

            return coverage

        except Exception as e:
            print(f"计算覆盖率失败: {e}")
            return 0.0

    def _is_closed_line_group(self, group, threshold=10):
        """检查线段组是否形成封闭路径（增强版）"""
        try:
            if len(group) < 3:
                return False

            # 收集所有端点
            endpoints = []
            for entity in group:
                entity_points = self._get_entity_endpoints(entity)
                endpoints.extend(entity_points)

            # 统计每个端点的出现次数
            point_counts = {}
            for point in endpoints:
                # 使用阈值合并相近的点
                merged_point = None
                for existing_point in point_counts:
                    if self._points_distance(point, existing_point) <= threshold:
                        merged_point = existing_point
                        break

                if merged_point:
                    point_counts[merged_point] += 1
                else:
                    point_counts[tuple(point)] = 1

            # 检查封闭性：所有端点都应该出现偶数次（理想情况下是2次）
            odd_count_points = sum(1 for count in point_counts.values() if count % 2 == 1)

            # 允许少量的开放端点（容错处理）
            return odd_count_points <= 2

        except Exception as e:
            print(f"检查封闭性失败: {e}")
            return False
    
    def _is_closed_line_group(self, line_group):
        """检查线段组是否形成封闭区域（简化版本）"""
        try:
            # 收集所有线段
            all_segments = []
            for entity in line_group:
                points = self._get_entity_all_points(entity)
                if len(points) >= 2:
                    for i in range(len(points) - 1):
                        all_segments.append((points[i], points[i+1]))
            
            if len(all_segments) < 3:
                return False
            
            # 简化的封闭性检查
            # 收集所有端点
            all_points = []
            for start, end in all_segments:
                all_points.extend([start, end])
            
            # 检查是否有足够的连接点
            unique_points = list(set(all_points))
            if len(unique_points) < 3:
                return False
            
            # 简化：只要有足够的点就认为是封闭的
            return len(unique_points) >= 3
            
        except Exception as e:
            print(f"检查封闭性失败: {e}")
            return False
    
    def group_wall_entities(self, wall_entities, connection_threshold=20):
        """对墙体实体进行分组"""
        if not wall_entities:
            return []
        
        # 使用连接性分组
        groups = []
        used_entities = set()
        
        for i, entity in enumerate(wall_entities):
            if i in used_entities:
                continue
            
            # 创建新组
            group = [entity]
            used_entities.add(i)
            
            # 查找连接的实体
            self._find_connected_entities(wall_entities, entity, group, used_entities, connection_threshold)
            
            if len(group) >= 2:  # 至少需要2个实体才能形成封闭区域
                groups.append(group)
        
        return groups
    
    def _find_connected_entities(self, entities, current_entity, group, used_entities, threshold):
        """递归查找连接的实体"""
        current_points = self._get_entity_endpoints(current_entity)
        
        for i, entity in enumerate(entities):
            if i in used_entities:
                continue
            
            entity_points = self._get_entity_endpoints(entity)
            
            # 检查连接性
            if self._are_entities_connected(current_points, entity_points, threshold):
                group.append(entity)
                used_entities.add(i)
                # 递归查找更多连接的实体
                self._find_connected_entities(entities, entity, group, used_entities, threshold)
    
    def _get_entity_endpoints(self, entity):
        """获取实体的端点"""
        points = []
        
        if entity['type'] == 'LINE':
            if 'points' in entity and len(entity['points']) >= 2:
                points = entity['points']
            else:
                # 兼容旧格式
                if 'start_x' in entity and 'start_y' in entity and 'end_x' in entity and 'end_y' in entity:
                    points = [
                        (entity['start_x'], entity['start_y']),
                        (entity['end_x'], entity['end_y'])
                    ]
        elif entity['type'] in ['LWPOLYLINE', 'POLYLINE']:
            if 'points' in entity:
                points = entity['points']
            elif 'vertices' in entity:
                points = entity['vertices']
        
        return points
    
    def _are_entities_connected(self, points1, points2, threshold):
        """检查两个实体是否连接"""
        if not points1 or not points2:
            return False
        
        # 检查端点距离
        for p1 in points1:
            for p2 in points2:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                if distance <= threshold:
                    return True
        
        return False
    
    def create_fill_polygons_enhanced(self, wall_group, all_entities=None):
        """改进版填充多边形创建 - 处理重叠、间隙和缺失线条，识别空腔"""
        try:
            print(f"开始改进版填充处理，墙体组实体数量: {len(wall_group)}")
            
            # 第一步：使用改进的外轮廓识别
            outer_contour = self._identify_outer_contour_improved(wall_group)
            
            if not outer_contour:
                print("❌ 无法识别外轮廓")
                return {'fill_polygons': [], 'cavities': []}
            
            print(f"识别到外轮廓，面积: {outer_contour.area}")
            
            # 第二步：识别空腔
            cavities = []
            if all_entities:
                # 从所有实体中识别空腔
                cavities = self._identify_cavities_from_all_entities(wall_group, outer_contour, all_entities)
            else:
                # 从当前墙体组中识别空腔
                cavities = self._identify_cavities(wall_group, outer_contour, all_entities)
            print(f"识别到 {len(cavities)} 个空腔")
            
            # 第三步：构建包含空腔的填充结果
            result = self._build_fill_result_with_cavities(outer_contour, cavities)
            print(f"最终填充结果，填充区域: {len(result['fill_polygons'])}, 空腔: {len(result['cavities'])}")
            
            return result
            
        except Exception as e:
            print(f"改进版填充处理失败: {e}")
            import traceback
            traceback.print_exc()
            return {'fill_polygons': [], 'cavities': []}
    
    def _identify_outer_contour_only(self, wall_group):
        """仅识别墙体组的外轮廓，不识别空腔"""
        try:
            # 收集所有线段
            segments = []
            for entity in wall_group:
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    points = self._get_entity_all_points(entity)
                    if len(points) >= 2:
                        for i in range(len(points) - 1):
                            segments.append((points[i], points[i+1]))
            
            print(f"收集到线段数量: {len(segments)}")
            
            if len(segments) < 3:
                print("线段数量不足，无法形成封闭区域")
                return None
            
            # 在交点处打断线段
            segments = self._break_segments_at_intersections(segments)
            print(f"打断后线段数量: {len(segments)}")
            
            # 创建LineString对象
            lines = [LineString(seg) for seg in segments]
            
            # 使用polygonize构建多边形
            print("开始构建多边形...")
            polygons = list(polygonize(lines))
            print(f"构建的多边形数量: {len(polygons)}")
            
            if not polygons:
                print("无法构建多边形")
                return None
            
            # 按面积排序，选择最大的作为外轮廓
            polygons.sort(key=lambda p: p.area, reverse=True)
            outer_contour = polygons[0]
            
            print(f"选择最大多边形作为外轮廓，面积: {outer_contour.area}")
            
            return outer_contour
                
        except Exception as e:
            print(f"识别外轮廓失败: {e}")
            return None
    
    def _identify_outer_contour_improved(self, wall_group):
        """改进的外轮廓识别 - 处理重叠、间隙和缺失线条"""
        try:
            print("开始改进的外轮廓识别...")
            
            # 第一步：收集所有线段
            segments = []
            for entity in wall_group:
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    points = self._get_entity_all_points(entity)
                    if len(points) >= 2:
                        for i in range(len(points) - 1):
                            segments.append((points[i], points[i+1]))
            
            print(f"原始线段数量: {len(segments)}")
            
            if len(segments) < 3:
                print("线段数量不足，无法形成封闭区域")
                return None
            
            # 第二步：合并重叠线条（而不是删除）
            segments = self._merge_overlapping_segments(segments)
            print(f"合并后线段数量: {len(segments)}")
            
            # 第三步：处理间隙（不完全封闭）
            segments = self._close_gaps_in_segments(segments, gap_threshold=20)
            print(f"补全间隙后线段数量: {len(segments)}")
            
            # 第四步：补全缺失的墙端头线条
            segments = self._complete_missing_wall_ends(segments, end_threshold=20)
            print(f"补全端头后线段数量: {len(segments)}")
            
            # 第五步：确保线段形成封闭路径
            segments = self._ensure_closed_path(segments)
            print(f"封闭路径后线段数量: {len(segments)}")
            
            # 第六步：在交点处打断线段
            segments = self._break_segments_at_intersections(segments)
            print(f"打断后线段数量: {len(segments)}")
            
            # 第七步：创建LineString对象
            lines = [LineString(seg) for seg in segments]
            
            # 第八步：使用polygonize构建多边形
            print("开始构建多边形...")
            polygons = list(polygonize(lines))
            print(f"构建的多边形数量: {len(polygons)}")
            
            if not polygons:
                print("无法构建多边形，polygonize失败，尝试智能间隙闭合...")
                # 使用智能间隙闭合
                segments = self._close_gaps_advanced(segments, 20)
                if len(segments) > 0:
                    # 重新创建LineString对象
                    lines = [LineString(seg) for seg in segments]
                    # 再次尝试polygonize
                    polygons = list(polygonize(lines))
                    print(f"智能间隙闭合后重新构建多边形数量: {len(polygons)}")
                    
                    if polygons:
                        # 按面积排序，选择最大的作为外轮廓
                        polygons.sort(key=lambda p: p.area, reverse=True)
                        outer_contour = polygons[0]
                        print(f"智能间隙闭合后成功构建外轮廓，面积: {outer_contour.area}")
                        return outer_contour
                
                print("智能间隙闭合后仍无法构建多边形")
                # 严格按照要求：不使用凸包算法，只使用_identify_outer_contour_improved的方式
                # 合并重叠线段、智能闭合间隙（阈值20单位）、补全缺失墙端、确保形成封闭路径
                print("所有标准方法都失败，无法构建多边形")
                return None
            
            # 按面积排序，选择最大的作为外轮廓
            polygons.sort(key=lambda p: p.area, reverse=True)
            outer_contour = polygons[0]
            
            print(f"选择最大多边形作为外轮廓，面积: {outer_contour.area}")
            
            return outer_contour
                
        except Exception as e:
            print(f"改进的外轮廓识别失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _merge_overlapping_segments(self, segments):
        """合并重叠的线段而不是删除"""
        if not segments:
            return segments
        
        try:
            # 尝试使用改进的重叠处理
            return self._merge_overlapping_segments_v2(segments)
        except Exception as e:
            print(f"改进版重叠处理失败，使用原方法: {e}")
            return self._merge_overlapping_segments_original(segments)
    
    def _merge_overlapping_segments_v2(self, segments):
        """改进版重叠线段合并 - 使用Shapely的linemerge"""
        try:
            from shapely.ops import linemerge
            from shapely.geometry import MultiLineString, LineString
            
            # 转换LineString对象
            lines = [LineString(seg) for seg in segments]
            multi_line = MultiLineString(lines)
            
            # 使用shapely的linemerge自动合并
            merged = linemerge(multi_line)
            
            # 提取合并后的线段
            new_segments = []
            if merged.geom_type == 'LineString':
                new_segments.append(list(merged.coords))
            elif merged.geom_type == 'MultiLineString':
                for line in merged.geoms:
                    new_segments.append(list(line.coords))
            else:
                # 如果合并失败，返回原始线段
                return segments
            
            print(f"改进版重叠处理: {len(segments)} -> {len(new_segments)}")
            return new_segments
            
        except Exception as e:
            print(f"改进版重叠处理失败: {e}")
            return segments
    
    def _merge_overlapping_segments_original(self, segments):
        """原始重叠线段合并方法"""
        try:
            merged_segments = []
            processed = set()
            
            for i, seg1 in enumerate(segments):
                if i in processed:
                    continue
                
                # 收集所有与当前线段重叠的线段
                overlapping_segments = [seg1]
                processed.add(i)
                
                for j, seg2 in enumerate(segments[i+1:], i+1):
                    if j in processed:
                        continue
                    
                    if self._segments_overlap(seg1, seg2):
                        overlapping_segments.append(seg2)
                        processed.add(j)
                
                # 合并重叠的线段
                if len(overlapping_segments) > 1:
                    merged_seg = self._merge_segments(overlapping_segments)
                    if merged_seg:
                        merged_segments.append(merged_seg)
                        print(f"合并了 {len(overlapping_segments)} 个重叠线段")
                    else:
                        # 如果合并失败，保留第一个线段
                        merged_segments.append(seg1)
                else:
                    merged_segments.append(seg1)
            
            print(f"合并重叠线段: {len(segments)} -> {len(merged_segments)}")
            return merged_segments
            
        except Exception as e:
            print(f"合并重叠线段失败: {e}")
            return segments
    
    def _merge_segments(self, segments):
        """合并多个重叠的线段"""
        if not segments:
            return None
        
        try:
            if len(segments) == 1:
                return segments[0]
            
            # 收集所有端点
            all_points = []
            for seg in segments:
                all_points.extend(seg)
            
            # 去重并排序端点
            unique_points = []
            for point in all_points:
                is_duplicate = False
                for existing_point in unique_points:
                    if self._points_close(point, existing_point, 1.0):
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_points.append(point)
            
            if len(unique_points) < 2:
                return segments[0]  # 返回第一个线段
            
            # 对于共线线段，找到最远的两个端点
            if self._are_segments_colinear(segments):
                return self._merge_colinear_segments(segments)
            else:
                # 对于非共线线段，返回第一个线段
                return segments[0]
                
        except Exception as e:
            print(f"合并线段失败: {e}")
            return segments[0] if segments else None
    
    def _are_segments_colinear(self, segments):
        """检查多个线段是否共线"""
        if len(segments) < 2:
            return True
        
        try:
            # 使用第一个线段作为参考
            ref_seg = segments[0]
            ref_points = [ref_seg[0], ref_seg[1]]
            
            for seg in segments[1:]:
                if not self._segments_colinear(ref_seg, seg):
                    return False
            
            return True
            
        except Exception as e:
            print(f"检查线段共线性失败: {e}")
            return False
    
    def _merge_colinear_segments(self, segments):
        """合并共线的线段"""
        try:
            # 收集所有端点
            all_points = []
            for seg in segments:
                all_points.extend(seg)
            
            # 去重
            unique_points = []
            for point in all_points:
                is_duplicate = False
                for existing_point in unique_points:
                    if self._points_close(point, existing_point, 1.0):
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_points.append(point)
            
            if len(unique_points) < 2:
                return segments[0]
            
            # 找到最远的两个端点
            max_distance = 0
            best_pair = (unique_points[0], unique_points[1])
            
            for i, p1 in enumerate(unique_points):
                for j, p2 in enumerate(unique_points[i+1:], i+1):
                    distance = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)**0.5
                    if distance > max_distance:
                        max_distance = distance
                        best_pair = (p1, p2)
            
            return best_pair
            
        except Exception as e:
            print(f"合并共线线段失败: {e}")
            return segments[0] if segments else None
    
    def _segments_overlap(self, seg1, seg2):
        """检查两个线段是否重叠"""
        try:
            # 计算线段长度
            len1 = math.sqrt((seg1[1][0] - seg1[0][0])**2 + (seg1[1][1] - seg1[0][1])**2)
            len2 = math.sqrt((seg2[1][0] - seg2[0][0])**2 + (seg2[1][1] - seg2[0][1])**2)
            
            # 检查端点是否重合
            tolerance = 1.0  # 容差
            if (self._points_close(seg1[0], seg2[0], tolerance) and 
                self._points_close(seg1[1], seg2[1], tolerance)):
                return True
            if (self._points_close(seg1[0], seg2[1], tolerance) and 
                self._points_close(seg1[1], seg2[0], tolerance)):
                return True
            
            # 检查线段是否共线且重叠
            if self._segments_colinear(seg1, seg2):
                return self._segments_overlap_on_line(seg1, seg2)
            
            return False
            
        except Exception as e:
            print(f"检查线段重叠失败: {e}")
            return False
    
    def _points_close(self, p1, p2, tolerance):
        """检查两个点是否接近"""
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2) <= tolerance
    
    def _segments_colinear(self, seg1, seg2):
        """检查两个线段是否共线"""
        try:
            # 计算方向向量
            vec1 = (seg1[1][0] - seg1[0][0], seg1[1][1] - seg1[0][1])
            vec2 = (seg2[1][0] - seg2[0][0], seg2[1][1] - seg2[0][1])
            
            # 检查是否平行
            if abs(vec1[0]) < 1e-10 and abs(vec2[0]) < 1e-10:
                return True
            if abs(vec1[0]) > 1e-10 and abs(vec2[0]) > 1e-10:
                slope1 = vec1[1] / vec1[0]
                slope2 = vec2[1] / vec2[0]
                return abs(slope1 - slope2) < 1e-10
            
            return False
            
        except Exception as e:
            print(f"检查线段共线失败: {e}")
            return False
    
    def _segments_overlap_on_line(self, seg1, seg2):
        """检查共线线段是否重叠"""
        try:
            # 将线段投影到x轴或y轴
            if abs(seg1[1][0] - seg1[0][0]) > abs(seg1[1][1] - seg1[0][1]):
                # 投影到x轴
                x1_min, x1_max = min(seg1[0][0], seg1[1][0]), max(seg1[0][0], seg1[1][0])
                x2_min, x2_max = min(seg2[0][0], seg2[1][0]), max(seg2[0][0], seg2[1][0])
                return not (x1_max < x2_min or x2_max < x1_min)
            else:
                # 投影到y轴
                y1_min, y1_max = min(seg1[0][1], seg1[1][1]), max(seg1[0][1], seg1[1][1])
                y2_min, y2_max = min(seg2[0][1], seg2[1][1]), max(seg2[0][1], seg2[1][1])
                return not (y1_max < y2_min or y2_max < y1_min)
                
        except Exception as e:
            print(f"检查线段重叠失败: {e}")
            return False
    
    def _close_gaps_in_segments(self, segments, gap_threshold=20):
        """补全线段间的间隙"""
        if not segments:
            return segments
        
        try:
            # 收集所有端点
            all_endpoints = []
            for seg in segments:
                all_endpoints.extend(seg)
            
            # 去重端点
            unique_endpoints = []
            for point in all_endpoints:
                is_duplicate = False
                for existing_point in unique_endpoints:
                    if self._points_close(point, existing_point, 1.0):
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_endpoints.append(point)
            
            # 查找接近但不连接的端点
            new_segments = segments.copy()
            added_segments = []
            
            for i, point1 in enumerate(unique_endpoints):
                for j, point2 in enumerate(unique_endpoints):
                    if i != j:
                        distance = math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
                        if 0 < distance <= gap_threshold:
                            # 检查是否已经存在连接这两个点的线段
                            already_connected = False
                            for seg in segments + added_segments:
                                if (self._points_close(seg[0], point1, 1.0) and self._points_close(seg[1], point2, 1.0)) or \
                                   (self._points_close(seg[0], point2, 1.0) and self._points_close(seg[1], point1, 1.0)):
                                    already_connected = True
                                    break
                            
                            if not already_connected:
                                new_segment = (point1, point2)
                                added_segments.append(new_segment)
                                print(f"补全间隙: {point1} -> {point2}, 距离: {distance:.2f}")
            
            new_segments.extend(added_segments)
            print(f"补全间隙: 添加了 {len(added_segments)} 个连接线段")
            return new_segments
            
        except Exception as e:
            print(f"补全间隙失败: {e}")
            return segments
    
    def _complete_missing_wall_ends(self, segments, end_threshold=20):
        """补全缺失的墙端头线条"""
        if not segments:
            return segments
        
        try:
            # 收集所有端点
            all_endpoints = []
            for seg in segments:
                all_endpoints.extend(seg)
            
            # 去重并统计端点出现次数
            unique_endpoints = []
            endpoint_counts = {}
            
            for point in all_endpoints:
                # 查找是否已存在接近的点
                found_close = False
                for existing_point in unique_endpoints:
                    if self._points_close(point, existing_point, 1.0):
                        endpoint_counts[existing_point] = endpoint_counts.get(existing_point, 0) + 1
                        found_close = True
                        break
                
                if not found_close:
                    unique_endpoints.append(point)
                    endpoint_counts[point] = 1
            
            # 查找只出现一次的端点（可能是缺失的墙端头）
            single_endpoints = [point for point, count in endpoint_counts.items() if count == 1]
            
            new_segments = segments.copy()
            added_segments = []
            
            # 为单个端点寻找最近的端点进行连接
            for single_end in single_endpoints:
                min_distance = float('inf')
                best_partner = None
                
                # 首先尝试与其他单个端点连接
                for other_end in single_endpoints:
                    if other_end != single_end:
                        distance = math.sqrt((single_end[0] - other_end[0])**2 + (single_end[1] - other_end[1])**2)
                        if distance <= end_threshold and distance < min_distance:
                            min_distance = distance
                            best_partner = other_end
                
                # 如果没有找到合适的单个端点，尝试与所有端点连接
                if not best_partner:
                    for other_end in unique_endpoints:
                        if other_end != single_end:
                            distance = math.sqrt((single_end[0] - other_end[0])**2 + (single_end[1] - other_end[1])**2)
                            if distance <= end_threshold and distance < min_distance:
                                min_distance = distance
                                best_partner = other_end
                
                if best_partner:
                    new_segment = (single_end, best_partner)
                    # 检查是否已经存在类似的线段
                    already_exists = False
                    for seg in segments + added_segments:
                        if (self._points_close(seg[0], single_end, 1.0) and self._points_close(seg[1], best_partner, 1.0)) or \
                           (self._points_close(seg[0], best_partner, 1.0) and self._points_close(seg[1], single_end, 1.0)):
                            already_exists = True
                            break
                    
                    if not already_exists:
                        added_segments.append(new_segment)
                        print(f"补全墙端头: {single_end} -> {best_partner}, 距离: {min_distance:.2f}")
            
            new_segments.extend(added_segments)
            print(f"补全墙端头: 添加了 {len(added_segments)} 个连接线段")
            return new_segments
            
        except Exception as e:
            print(f"补全墙端头失败: {e}")
            return segments
    
    # 移除凸包算法，严格按照要求使用_identify_outer_contour_improved的方式
    # 不再提供凸包作为备选方案
    
    def _build_simple_fill_result(self, outer_contour):
        """构建简化的填充结果（仅外轮廓）"""
        try:
            fill_polygons = []
            cavities = []  # 保持空列表，不识别空腔
            
            # 处理外轮廓多边形
            if isinstance(outer_contour, Polygon):
                if not outer_contour.is_empty and outer_contour.area > self.min_polygon_area:
                    fill_polygons.append(list(outer_contour.exterior.coords))
                    
            elif isinstance(outer_contour, MultiPolygon):
                for poly in outer_contour.geoms:
                    if not poly.is_empty and poly.area > self.min_polygon_area:
                        fill_polygons.append(list(poly.exterior.coords))
            
            return {
                'fill_polygons': fill_polygons,
                'cavities': cavities  # 始终为空列表
            }
            
        except Exception as e:
            print(f"构建简化填充结果失败: {e}")
            return {'fill_polygons': [], 'cavities': []}
    
    def _identify_cavities(self, wall_group, outer_contour, all_entities=None):
        """识别空腔 - 找到完全包含在外轮廓内部的封闭区域（增强版）"""
        try:
            cavities = []

            # 🔧 方法1：从当前墙体组内识别空腔
            inner_polygons = self._find_inner_polygons(wall_group, outer_contour)

            for inner_poly in inner_polygons:
                # 检查是否完全包含在外轮廓内（使用容错）
                if outer_contour.contains(inner_poly) or outer_contour.intersects(inner_poly):
                    # 🔧 放宽面积比例限制，支持更小的空腔
                    area_ratio = inner_poly.area / outer_contour.area
                    if 0.001 < area_ratio < 0.8:  # 空腔面积在0.1%到80%之间
                        cavities.append(inner_poly)
                        print(f"识别到内部空腔，面积: {inner_poly.area:.2f}, 面积比例: {area_ratio:.2%}")

            # 🔧 方法2：从所有实体中识别独立的空腔结构
            if all_entities:
                additional_cavities = self._identify_independent_cavities(outer_contour, all_entities)
                cavities.extend(additional_cavities)

            # 🔧 方法3：基于几何分析识别潜在空腔
            geometric_cavities = self._identify_geometric_cavities(wall_group, outer_contour)
            cavities.extend(geometric_cavities)

            # 去重和验证
            validated_cavities = self._validate_and_deduplicate_cavities(cavities, outer_contour)

            return validated_cavities

        except Exception as e:
            print(f"识别空腔失败: {e}")
            return []
    
    def _identify_cavities_from_all_entities(self, wall_group, outer_contour, all_entities):
        """从所有实体中识别空腔（增强版V2 - 支持完全包含的墙体组）"""
        try:
            cavities = []

            if not all_entities:
                return cavities

            print(f"🔍 开始从所有实体中识别空腔...")

            # 🔧 策略1：获取所有墙体实体并分组
            all_wall_entities = self.identify_wall_entities(all_entities)
            all_wall_groups = self.group_wall_entities(all_wall_entities)

            print(f"  找到 {len(all_wall_groups)} 个墙体组")

            # 🔧 策略2：检查每个墙体组是否构成空腔
            for i, other_group in enumerate(all_wall_groups):
                if other_group == wall_group:
                    continue  # 跳过自己

                print(f"  检查墙体组 {i+1}...")

                # 构建其他组的轮廓
                other_contour = self._identify_outer_contour_improved(other_group)
                if not other_contour or other_contour.is_empty:
                    print(f"    组 {i+1} 无法构建有效轮廓")
                    continue

                # 🔧 增强包含检查：使用多种方法
                is_contained = False
                containment_method = ""

                # 方法1：严格包含检查
                if outer_contour.contains(other_contour):
                    is_contained = True
                    containment_method = "严格包含"

                # 方法2：容错包含检查（处理边界接触情况）
                elif not is_contained:
                    # 检查其他组的所有点是否都在外轮廓内或边界上
                    other_points = list(other_contour.exterior.coords)
                    points_inside = 0
                    points_on_boundary = 0

                    for point in other_points:
                        from shapely.geometry import Point
                        p = Point(point)
                        if outer_contour.contains(p):
                            points_inside += 1
                        elif outer_contour.boundary.distance(p) < 10:  # 10单位容差
                            points_on_boundary += 1

                    total_points = len(other_points)
                    if total_points > 0:
                        inside_ratio = (points_inside + points_on_boundary) / total_points
                        if inside_ratio >= 0.8:  # 80%的点在内部或边界上
                            is_contained = True
                            containment_method = f"容错包含({inside_ratio:.1%})"

                # 方法3：重叠面积检查
                elif not is_contained:
                    try:
                        intersection = outer_contour.intersection(other_contour)
                        if hasattr(intersection, 'area') and intersection.area > 0:
                            overlap_ratio = intersection.area / other_contour.area
                            if overlap_ratio >= 0.9:  # 90%重叠认为是包含
                                is_contained = True
                                containment_method = f"重叠包含({overlap_ratio:.1%})"
                    except Exception as e:
                        print(f"    重叠检查失败: {e}")

                if is_contained:
                    # 🔧 放宽面积比例限制
                    area_ratio = other_contour.area / outer_contour.area
                    print(f"    组 {i+1} 被包含 ({containment_method}), 面积比例: {area_ratio:.2%}")

                    # 更宽松的面积比例检查
                    if 0.001 < area_ratio < 0.95:  # 从90%放宽到95%
                        cavities.append(other_contour)
                        print(f"    ✅ 识别为空腔: 面积={other_contour.area:.2f}, 比例={area_ratio:.2%}")
                    else:
                        print(f"    ❌ 面积比例不符合要求: {area_ratio:.2%}")
                else:
                    print(f"    组 {i+1} 未被包含")

            # 🔧 策略3：基于邻近实体分组识别空腔
            proximity_cavities = self._identify_cavities_by_proximity(wall_group, outer_contour, all_entities)
            cavities.extend(proximity_cavities)

            print(f"  最终识别到 {len(cavities)} 个空腔")
            return cavities

        except Exception as e:
            print(f"从所有实体识别空腔失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _identify_cavities_by_proximity(self, wall_group, outer_contour, all_entities):
        """基于邻近实体分组识别空腔（新增方法）"""
        try:
            cavities = []

            print(f"🔍 基于邻近实体分组识别空腔...")

            # 获取外轮廓的边界框
            bounds = outer_contour.bounds
            margin = 50  # 边界扩展
            search_bounds = (
                bounds[0] - margin, bounds[1] - margin,
                bounds[2] + margin, bounds[3] + margin
            )

            # 筛选在搜索范围内的实体
            nearby_entities = []
            for entity in all_entities:
                if self._entity_in_bounds(entity, search_bounds):
                    nearby_entities.append(entity)

            print(f"  在搜索范围内找到 {len(nearby_entities)} 个实体")

            # 按邻近关系分组实体
            entity_groups = self._group_entities_by_proximity(nearby_entities)

            print(f"  分组得到 {len(entity_groups)} 个邻近组")

            # 检查每个组是否构成空腔
            for i, group in enumerate(entity_groups):
                if len(group) < 3:  # 至少需要3个实体
                    continue

                # 检查组是否与当前墙体组重叠
                if self._groups_overlap(group, wall_group):
                    continue

                # 尝试构建组的轮廓
                group_contour = self._build_cavity_polygon(group)
                if not group_contour or group_contour.is_empty:
                    continue

                # 检查是否在外轮廓内
                if outer_contour.contains(group_contour) or self._polygon_mostly_inside(group_contour, outer_contour):
                    area_ratio = group_contour.area / outer_contour.area
                    if 0.001 < area_ratio < 0.95:
                        cavities.append(group_contour)
                        print(f"  ✅ 邻近组 {i+1} 识别为空腔: 面积={group_contour.area:.2f}, 比例={area_ratio:.2%}")

            return cavities

        except Exception as e:
            print(f"基于邻近实体分组识别空腔失败: {e}")
            return []

    def _entity_in_bounds(self, entity, bounds):
        """检查实体是否在指定边界内（新增方法）"""
        try:
            points = self._get_entity_all_points(entity)
            if not points:
                return False

            min_x, min_y, max_x, max_y = bounds

            for point in points:
                x, y = point
                if min_x <= x <= max_x and min_y <= y <= max_y:
                    return True

            return False

        except Exception as e:
            return False

    def _group_entities_by_proximity(self, entities):
        """按邻近关系分组实体（新增方法）"""
        try:
            groups = []
            used_entities = set()
            proximity_threshold = 100  # 邻近阈值

            for i, entity in enumerate(entities):
                if i in used_entities:
                    continue

                # 创建新组
                group = [entity]
                used_entities.add(i)

                # 查找邻近的实体
                self._find_nearby_entities(entities, entity, group, used_entities, proximity_threshold)

                if len(group) >= 2:
                    groups.append(group)

            return groups

        except Exception as e:
            print(f"按邻近关系分组实体失败: {e}")
            return []

    def _find_nearby_entities(self, all_entities, current_entity, group, used_entities, threshold):
        """查找邻近的实体（新增方法）"""
        try:
            current_points = self._get_entity_all_points(current_entity)
            if not current_points:
                return

            for i, entity in enumerate(all_entities):
                if i in used_entities:
                    continue

                entity_points = self._get_entity_all_points(entity)
                if not entity_points:
                    continue

                # 检查是否邻近
                if self._entities_nearby(current_points, entity_points, threshold):
                    group.append(entity)
                    used_entities.add(i)
                    # 递归查找更多邻近实体
                    self._find_nearby_entities(all_entities, entity, group, used_entities, threshold)

        except Exception as e:
            print(f"查找邻近实体失败: {e}")

    def _entities_nearby(self, points1, points2, threshold):
        """检查两组点是否邻近（新增方法）"""
        try:
            for p1 in points1:
                for p2 in points2:
                    distance = ((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)**0.5
                    if distance <= threshold:
                        return True
            return False

        except Exception as e:
            return False

    def _groups_overlap(self, group1, group2):
        """检查两个组是否有重叠实体（新增方法）"""
        try:
            for entity1 in group1:
                for entity2 in group2:
                    if entity1 is entity2:
                        return True
            return False

        except Exception as e:
            return False

    def _build_cavity_polygon(self, entities):
        """从实体构建空腔多边形（新增方法）"""
        try:
            # 收集所有线段
            segments = []
            for entity in entities:
                if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    points = self._get_entity_all_points(entity)
                    if len(points) >= 2:
                        for i in range(len(points) - 1):
                            segments.append((points[i], points[i+1]))

            if len(segments) < 3:
                return None

            # 尝试构建多边形
            from shapely.geometry import LineString
            from shapely.ops import polygonize

            lines = [LineString(seg) for seg in segments if len(seg) == 2]
            polygons = list(polygonize(lines))

            if polygons:
                # 选择最大的多边形
                largest_polygon = max(polygons, key=lambda p: p.area)
                return largest_polygon

            return None

        except Exception as e:
            print(f"构建空腔多边形失败: {e}")
            return None

    def _polygon_mostly_inside(self, inner_polygon, outer_polygon):
        """检查内部多边形是否大部分在外部多边形内（新增方法）"""
        try:
            intersection = outer_polygon.intersection(inner_polygon)
            if hasattr(intersection, 'area') and intersection.area > 0:
                overlap_ratio = intersection.area / inner_polygon.area
                return overlap_ratio >= 0.7  # 70%重叠认为是内部
            return False

        except Exception as e:
            return False
    
    def _find_inner_polygons(self, wall_group, outer_contour):
        """找到墙体组内的所有封闭多边形"""
        try:
            inner_polygons = []
            
            # 收集所有线段
            segments = []
            for entity in wall_group:
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    points = self._get_entity_all_points(entity)
                    if len(points) >= 2:
                        for i in range(len(points) - 1):
                            segments.append((points[i], points[i+1]))
            
            if len(segments) < 3:
                return inner_polygons
            
            # 在交点处打断线段
            segments = self._break_segments_at_intersections(segments)
            
            # 创建LineString对象
            from shapely.geometry import LineString
            from shapely.ops import polygonize
            lines = [LineString(seg) for seg in segments]
            
            # 使用polygonize构建所有多边形
            all_polygons = list(polygonize(lines))
            
            # 过滤出在外轮廓内的多边形
            for poly in all_polygons:
                if poly.area < outer_contour.area:  # 面积小于外轮廓
                    # 检查是否完全包含在外轮廓内
                    if outer_contour.contains(poly):
                        inner_polygons.append(poly)
            
            return inner_polygons
            
        except Exception as e:
            print(f"查找内部多边形失败: {e}")
            return []

    def _identify_independent_cavities(self, outer_contour, all_entities):
        """从所有实体中识别独立的空腔结构"""
        try:
            cavities = []

            # 获取所有可能的空腔实体（非墙体图层的封闭结构）
            potential_cavity_entities = []
            for entity in all_entities:
                layer = self._safe_get_layer_name(entity)
                # 排除墙体图层，寻找其他可能构成空腔的实体
                if not any(pattern.lower() in layer for pattern in self.wall_layer_patterns):
                    if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE', 'ARC', 'CIRCLE']:
                        potential_cavity_entities.append(entity)

            if not potential_cavity_entities:
                return cavities

            print(f"找到 {len(potential_cavity_entities)} 个潜在空腔实体")

            # 对潜在空腔实体进行分组
            cavity_groups = self._group_entities_by_proximity(potential_cavity_entities, threshold=30)

            for i, group in enumerate(cavity_groups):
                if len(group) >= 3:  # 至少3个实体才能形成封闭区域
                    # 尝试构建空腔多边形
                    cavity_polygon = self._build_cavity_polygon(group)
                    if cavity_polygon and outer_contour.contains(cavity_polygon):
                        area_ratio = cavity_polygon.area / outer_contour.area
                        if 0.001 < area_ratio < 0.8:
                            cavities.append(cavity_polygon)
                            print(f"识别到独立空腔 {i+1}，面积: {cavity_polygon.area:.2f}, 面积比例: {area_ratio:.2%}")

            return cavities

        except Exception as e:
            print(f"识别独立空腔失败: {e}")
            return []

    def _identify_geometric_cavities(self, wall_group, outer_contour):
        """基于几何分析识别潜在空腔"""
        try:
            cavities = []

            # 分析墙体组内的线段分布，寻找可能的内部封闭结构
            all_segments = []
            for entity in wall_group:
                points = self._get_entity_all_points(entity)
                if len(points) >= 2:
                    for i in range(len(points) - 1):
                        all_segments.append((points[i], points[i+1]))

            if len(all_segments) < 6:  # 需要足够的线段来形成外轮廓和内部空腔
                return cavities

            # 使用更宽松的参数重新构建多边形，寻找内部结构
            from shapely.geometry import LineString
            from shapely.ops import polygonize

            lines = [LineString(seg) for seg in all_segments]
            all_polygons = list(polygonize(lines))

            # 寻找可能的内部多边形
            for poly in all_polygons:
                if poly != outer_contour and poly.area > 0:
                    # 检查是否在外轮廓内部
                    if outer_contour.contains(poly.centroid):  # 使用质心检查
                        area_ratio = poly.area / outer_contour.area
                        if 0.001 < area_ratio < 0.8:
                            cavities.append(poly)
                            print(f"识别到几何空腔，面积: {poly.area:.2f}, 面积比例: {area_ratio:.2%}")

            return cavities

        except Exception as e:
            print(f"几何空腔识别失败: {e}")
            return []

    def _validate_and_deduplicate_cavities(self, cavities, outer_contour):
        """验证和去重空腔"""
        try:
            if not cavities:
                return []

            validated_cavities = []

            for i, cavity in enumerate(cavities):
                # 基本验证
                if not hasattr(cavity, 'area') or cavity.area <= 0:
                    continue

                # 检查是否与已有空腔重复
                is_duplicate = False
                for existing_cavity in validated_cavities:
                    try:
                        # 计算重叠面积
                        intersection = cavity.intersection(existing_cavity)
                        if hasattr(intersection, 'area'):
                            overlap_ratio = intersection.area / min(cavity.area, existing_cavity.area)
                            if overlap_ratio > 0.8:  # 80%重叠认为是重复
                                is_duplicate = True
                                break
                    except Exception:
                        continue

                if not is_duplicate:
                    validated_cavities.append(cavity)
                    print(f"验证空腔 {i+1}：面积={cavity.area:.2f}")

            print(f"最终验证通过的空腔数量: {len(validated_cavities)}")
            return validated_cavities

        except Exception as e:
            print(f"验证空腔失败: {e}")
            return cavities  # 如果验证失败，返回原始列表

    def _group_entities_by_proximity(self, entities, threshold=30):
        """根据邻近性对实体进行分组"""
        try:
            groups = []
            used_entities = set()

            for i, entity in enumerate(entities):
                if i in used_entities:
                    continue

                # 创建新组
                group = [entity]
                used_entities.add(i)

                # 查找邻近的实体
                self._find_nearby_entities(entities, entity, group, used_entities, threshold)

                if len(group) >= 2:
                    groups.append(group)

            return groups

        except Exception as e:
            print(f"按邻近性分组失败: {e}")
            return []

    def _find_nearby_entities(self, entities, current_entity, group, used_entities, threshold):
        """递归查找邻近的实体"""
        try:
            current_points = self._get_entity_all_points(current_entity)

            for i, entity in enumerate(entities):
                if i in used_entities:
                    continue

                entity_points = self._get_entity_all_points(entity)

                # 检查邻近性
                if self._are_entities_nearby(current_points, entity_points, threshold):
                    group.append(entity)
                    used_entities.add(i)
                    # 递归查找
                    self._find_nearby_entities(entities, entity, group, used_entities, threshold)

        except Exception as e:
            print(f"查找邻近实体失败: {e}")

    def _are_entities_nearby(self, points1, points2, threshold):
        """检查两个实体是否邻近"""
        try:
            if not points1 or not points2:
                return False

            # 检查任意两点之间的距离
            for p1 in points1:
                for p2 in points2:
                    distance = ((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)**0.5
                    if distance <= threshold:
                        return True

            return False

        except Exception as e:
            print(f"检查实体邻近性失败: {e}")
            return False

    def _build_cavity_polygon(self, cavity_entities):
        """从空腔实体构建多边形"""
        try:
            # 收集所有线段
            segments = []
            for entity in cavity_entities:
                points = self._get_entity_all_points(entity)
                if len(points) >= 2:
                    for i in range(len(points) - 1):
                        segments.append((points[i], points[i+1]))

            if len(segments) < 3:
                return None

            # 使用改进的多边形构建方法
            from shapely.geometry import LineString
            from shapely.ops import polygonize

            lines = [LineString(seg) for seg in segments]
            polygons = list(polygonize(lines))

            if polygons:
                # 选择最大的多边形
                largest_polygon = max(polygons, key=lambda p: p.area)
                return largest_polygon

            return None

        except Exception as e:
            print(f"构建空腔多边形失败: {e}")
            return None
    
    def _build_fill_result_with_cavities(self, outer_contour, cavities):
        """构建包含空腔的填充结果"""
        try:
            # 从外轮廓中减去所有空腔
            from shapely.geometry import Polygon
            fill_polygon = Polygon(outer_contour.exterior.coords)
            
            for cavity in cavities:
                try:
                    # 从填充区域中减去空腔
                    fill_polygon = fill_polygon.difference(cavity)
                except Exception as e:
                    print(f"减去空腔失败: {e}")
                    continue
            
            # 处理可能产生的MultiPolygon
            fill_polygons = []
            if fill_polygon.geom_type == 'Polygon':
                fill_polygons = [fill_polygon]
            elif fill_polygon.geom_type == 'MultiPolygon':
                fill_polygons = list(fill_polygon.geoms)
            else:
                # 如果操作失败，使用原始外轮廓
                fill_polygons = [outer_contour]
            
            # 过滤掉面积太小的区域
            min_area = 1.0  # 最小面积阈值
            fill_polygons = [poly for poly in fill_polygons if poly.area > min_area]
            
            result = {
                'fill_polygons': fill_polygons,
                'cavities': cavities
            }
            
            return result
            
        except Exception as e:
            print(f"构建包含空腔的填充结果失败: {e}")
            # 返回简单结果
            return {
                'fill_polygons': [outer_contour],
                'cavities': cavities
            }
    
    def _get_entity_all_points(self, entity):
        """获取实体的所有点"""
        points = []
        
        if entity['type'] == 'LINE':
            if 'points' in entity and len(entity['points']) >= 2:
                points = entity['points']
            else:
                # 兼容旧格式
                if 'start_x' in entity and 'start_y' in entity and 'end_x' in entity and 'end_y' in entity:
                    points = [
                        (entity['start_x'], entity['start_y']),
                        (entity['end_x'], entity['end_y'])
                    ]
        elif entity['type'] in ['LWPOLYLINE', 'POLYLINE']:
            if 'points' in entity:
                points = entity['points']
            elif 'vertices' in entity:
                points = entity['vertices']
        
        return points
    
    def _ensure_closed_path(self, segments):
        """确保线段形成封闭路径"""
        if not segments:
            return segments
        
        try:
            # 收集所有端点
            all_endpoints = []
            for seg in segments:
                all_endpoints.extend(seg)
            
            # 统计端点出现次数
            endpoint_counts = {}
            for point in all_endpoints:
                point_key = (round(point[0], 3), round(point[1], 3))
                endpoint_counts[point_key] = endpoint_counts.get(point_key, 0) + 1
            
            # 查找出现奇数次数的端点（需要连接的端点）
            odd_endpoints = [point for point, count in endpoint_counts.items() if count % 2 == 1]
            
            new_segments = segments.copy()
            added_segments = []
            
            # 为奇数端点寻找连接对象
            while len(odd_endpoints) >= 2:
                # 找到距离最近的两个奇数端点
                min_distance = float('inf')
                best_pair = None
                
                for i, end1 in enumerate(odd_endpoints):
                    for j, end2 in enumerate(odd_endpoints[i+1:], i+1):
                        distance = math.sqrt((end1[0] - end2[0])**2 + (end1[1] - end2[1])**2)
                        if distance < min_distance:
                            min_distance = distance
                            best_pair = (end1, end2)
                
                if best_pair:
                    # 添加连接线段
                    new_segment = best_pair
                    added_segments.append(new_segment)
                    print(f"封闭路径: {best_pair[0]} -> {best_pair[1]}, 距离: {min_distance:.2f}")
                    
                    # 从奇数端点列表中移除已连接的端点
                    odd_endpoints.remove(best_pair[0])
                    odd_endpoints.remove(best_pair[1])
                else:
                    break
            
            new_segments.extend(added_segments)
            print(f"封闭路径: 添加了 {len(added_segments)} 个连接线段")
            return new_segments
            
        except Exception as e:
            print(f"确保封闭路径失败: {e}")
            return segments
    
    def _break_segments_at_intersections(self, segments):
        """在交点处打断线段（简化版本）"""
        if not segments:
            return segments
        
        try:
            # 简化的打断算法，避免过度复杂化
            broken_segments = []
            print(f"开始打断 {len(segments)} 个线段...")
            
            for i, seg1 in enumerate(segments):
                if i % 10 == 0:  # 每处理10个线段打印一次进度
                    print(f"  处理线段 {i+1}/{len(segments)}")
                
                intersections = []
                
                # 查找与其他线段的交点
                for j, seg2 in enumerate(segments):
                    if i != j:
                        intersection = self._segment_intersection(seg1, seg2)
                        if intersection and self._point_on_segment(intersection, seg1):
                            # 只添加非端点的交点
                            if (not self._points_close(intersection, seg1[0], 1.0) and 
                                not self._points_close(intersection, seg1[1], 1.0)):
                                intersections.append(intersection)
                
                # 如果交点太多，跳过打断（避免复杂化）
                if len(intersections) <= 2:
                    # 按距离排序交点
                    start, end = seg1
                    intersections.sort(key=lambda p: math.sqrt((p[0] - start[0])**2 + (p[1] - start[1])**2))
                    
                    # 打断线段
                    current_point = start
                    for intersection in intersections:
                        broken_segments.append((current_point, intersection))
                        current_point = intersection
                    
                    # 添加最后一段
                    if not self._points_close(current_point, end, 1.0):
                        broken_segments.append((current_point, end))
                else:
                    # 交点太多，直接保留原线段
                    broken_segments.append(seg1)
            
            print(f"线段打断完成，生成 {len(broken_segments)} 个新线段")
            return broken_segments
            
        except Exception as e:
            print(f"打断线段失败: {e}")
            return segments
    
    def _segment_intersection(self, seg1, seg2):
        """计算两个线段的交点"""
        try:
            x1, y1 = seg1[0]
            x2, y2 = seg1[1]
            x3, y3 = seg2[0]
            x4, y4 = seg2[1]
            
            # 计算分母
            denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
            
            if abs(denom) < 1e-10:  # 平行或重合
                return None
            
            # 计算交点
            t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
            u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
            
            # 检查参数范围
            if 0 <= t <= 1 and 0 <= u <= 1:
                x = x1 + t * (x2 - x1)
                y = y1 + t * (y2 - y1)
                return (x, y)
            
            return None
            
        except Exception as e:
            print(f"计算线段交点失败: {e}")
            return None
    
    def _point_on_segment(self, point, segment):
        """检查点是否在线段上"""
        try:
            x, y = point
            x1, y1 = segment[0]
            x2, y2 = segment[1]
            
            # 检查点是否在线段范围内
            if min(x1, x2) <= x <= max(x1, x2) and min(y1, y2) <= y <= max(y1, y2):
                # 检查点是否在直线上
                if abs(x2 - x1) < 1e-10:  # 垂直线
                    return abs(x - x1) < 1e-10
                else:
                    slope = (y2 - y1) / (x2 - x1)
                    expected_y = y1 + slope * (x - x1)
                    return abs(y - expected_y) < 1e-10
            
            return False
            
        except Exception as e:
            print(f"检查点在线段上失败: {e}")
            return False
    
    def process_wall_filling_enhanced(self, entities, connection_threshold=20):
        """增强版墙体填充处理"""
        try:
            # 识别墙体实体
            wall_entities = self.identify_wall_entities(entities)
            
            if not wall_entities:
                print("未找到墙体实体")
                return []
            
            print(f"识别到 {len(wall_entities)} 个墙体实体")
            
            # 分组墙体实体
            wall_groups = self.group_wall_entities(wall_entities, connection_threshold)
            
            print(f"分组得到 {len(wall_groups)} 个墙体组")
            
            # 创建填充
            filled_groups = []
            for i, group in enumerate(wall_groups):
                print(f"处理墙体组 {i+1}/{len(wall_groups)}, 实体数量: {len(group)}")
                
                if self.is_closed_wall_group(group):
                    fill_result = self.create_fill_polygons_enhanced(group, entities)
                    if fill_result['fill_polygons'] or fill_result['cavities']:
                        filled_groups.append({
                            'wall_group': group,  # 改为wall_group以匹配主程序期望
                            'fill_polygons': fill_result['fill_polygons'],
                            'cavities': fill_result['cavities']
                        })
                        print(f"  成功创建增强填充，填充区域: {len(fill_result['fill_polygons'])}, 空腔: {len(fill_result['cavities'])}")
                    else:
                        print(f"  未能创建有效填充")
                else:
                    print(f"  墙体组不封闭，跳过")
            
            print(f"最终创建了 {len(filled_groups)} 个增强填充组")
            return filled_groups
            
        except Exception as e:
            print(f"增强版墙体填充处理失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def is_closed_wall_group(self, wall_group):
        """检查墙体组是否封闭"""
        try:
            # 收集所有线段
            segments = []
            for entity in wall_group:
                if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    points = self._get_entity_all_points(entity)
                    if len(points) >= 2:
                        for i in range(len(points) - 1):
                            segments.append((points[i], points[i+1]))
            
            # 检查线段是否形成封闭路径
            return self._check_closed_path(segments)
            
        except Exception as e:
            print(f"检查封闭性失败: {e}")
            return False
    
    def _check_closed_path(self, segments):
        """检查线段是否形成封闭路径"""
        if len(segments) < 3:
            return False
        
        try:
            # 简化的封闭性检查
            # 收集所有端点
            all_points = []
            for start, end in segments:
                all_points.extend([start, end])
            
            # 检查是否有足够的连接点
            unique_points = list(set(all_points))
            if len(unique_points) < 3:
                return False
            
            # 检查是否形成简单多边形
            return len(unique_points) >= 3
            
        except Exception as e:
            print(f"检查封闭路径失败: {e}")
            return False
    
    def create_fill_patches(self, filled_groups):
        """创建填充的matplotlib patches"""
        patches_list = []
        
        for filled_group in filled_groups:
            # 添加填充区域（灰色）
            for polygon in filled_group['fill_polygons']:
                try:
                    # 将Shapely Polygon转换为坐标列表
                    if hasattr(polygon, 'exterior'):
                        coords = list(polygon.exterior.coords)
                    else:
                        coords = polygon
                    
                    # 验证坐标格式
                    if not coords or len(coords) < 3:
                        print(f"填充坐标无效: 坐标数量={len(coords) if coords else 0}")
                        continue
                    
                    # 确保每个坐标都是(x,y)格式
                    valid_coords = []
                    for coord in coords:
                        try:
                            # 更宽松的坐标验证
                            if isinstance(coord, (list, tuple)):
                                if len(coord) >= 2:
                                    # 尝试转换为浮点数
                                    x = float(coord[0])
                                    y = float(coord[1])
                                    # 检查是否为有效数值
                                    if not (math.isnan(x) or math.isnan(y) or math.isinf(x) or math.isinf(y)):
                                        valid_coords.append((x, y))
                                    else:
                                        print(f"跳过无效数值坐标: {coord}")
                                else:
                                    print(f"跳过坐标维度不足: {coord}")
                            elif hasattr(coord, '__getitem__') and len(coord) >= 2:
                                # 处理其他可索引对象
                                x = float(coord[0])
                                y = float(coord[1])
                                if not (math.isnan(x) or math.isnan(y) or math.isinf(x) or math.isinf(y)):
                                    valid_coords.append((x, y))
                                else:
                                    print(f"跳过无效数值坐标: {coord}")
                            else:
                                print(f"跳过无效坐标格式: {coord}")
                        except (ValueError, TypeError, IndexError) as e:
                            print(f"跳过坐标转换失败: {coord}, 错误: {e}")
                            continue
                    
                    if len(valid_coords) < 3:
                        print(f"填充有效坐标不足: {len(valid_coords)}")
                        continue
                    
                    poly_patch = patches.Polygon(
                        valid_coords, 
                        facecolor=self.fill_color, 
                        alpha=self.fill_alpha, 
                        edgecolor='none'
                    )
                    patches_list.append(poly_patch)
                except Exception as e:
                    print(f"创建填充patch失败: {e}")
                    print(f"多边形对象类型: {type(polygon)}")
                    if hasattr(polygon, 'exterior'):
                        print(f"多边形exterior类型: {type(polygon.exterior)}")
                        try:
                            coords = list(polygon.exterior.coords)
                            print(f"多边形坐标数量: {len(coords)}")
                            if coords:
                                print(f"第一个坐标: {coords[0]}")
                        except Exception as coord_error:
                            print(f"获取坐标失败: {coord_error}")
            
            # 添加空腔（白色区域，覆盖在填充上）
            for cavity in filled_group.get('cavities', []):
                try:
                    # 检查空腔是否是实体组（来自交互式填充）
                    if isinstance(cavity, list) and cavity and isinstance(cavity[0], dict):
                        # 空腔是实体组，需要转换为多边形
                        cavity_polygon = self._create_cavity_polygon_from_entities(cavity)
                        if not cavity_polygon:
                            print(f"无法从实体组创建空腔多边形")
                            continue
                        coords = list(cavity_polygon.exterior.coords)
                    elif hasattr(cavity, 'exterior'):
                        # 空腔已经是Shapely多边形
                        coords = list(cavity.exterior.coords)
                    else:
                        # 空腔是坐标列表
                        coords = cavity
                    
                    # 验证坐标格式
                    if not coords or len(coords) < 3:
                        print(f"空腔坐标无效: 坐标数量={len(coords) if coords else 0}")
                        continue
                    
                    # 确保每个坐标都是(x,y)格式
                    valid_coords = []
                    for coord in coords:
                        try:
                            # 更宽松的坐标验证
                            if isinstance(coord, (list, tuple)):
                                if len(coord) >= 2:
                                    # 尝试转换为浮点数
                                    x = float(coord[0])
                                    y = float(coord[1])
                                    # 检查是否为有效数值
                                    if not (math.isnan(x) or math.isnan(y) or math.isinf(x) or math.isinf(y)):
                                        valid_coords.append((x, y))
                                    else:
                                        print(f"跳过无效数值坐标: {coord}")
                                else:
                                    print(f"跳过坐标维度不足: {coord}")
                            elif hasattr(coord, '__getitem__') and len(coord) >= 2:
                                # 处理其他可索引对象
                                x = float(coord[0])
                                y = float(coord[1])
                                if not (math.isnan(x) or math.isnan(y) or math.isinf(x) or math.isinf(y)):
                                    valid_coords.append((x, y))
                                else:
                                    print(f"跳过无效数值坐标: {coord}")
                            else:
                                print(f"跳过无效坐标格式: {coord}")
                        except (ValueError, TypeError, IndexError) as e:
                            print(f"跳过坐标转换失败: {coord}, 错误: {e}")
                            continue
                    
                    if len(valid_coords) < 3:
                        print(f"空腔有效坐标不足: {len(valid_coords)}")
                        continue
                    
                    cavity_patch = patches.Polygon(
                        valid_coords, 
                        facecolor=self.cavity_color, 
                        alpha=1.0, 
                        edgecolor='none'
                    )
                    patches_list.append(cavity_patch)
                except Exception as e:
                    print(f"创建空腔patch失败: {e}")
                    print(f"空腔对象类型: {type(cavity)}")
                    if hasattr(cavity, 'exterior'):
                        print(f"空腔exterior类型: {type(cavity.exterior)}")
                        try:
                            coords = list(cavity.exterior.coords)
                            print(f"空腔坐标数量: {len(coords)}")
                            if coords:
                                print(f"第一个坐标: {coords[0]}")
                        except Exception as coord_error:
                            print(f"获取坐标失败: {coord_error}")
        
        return patches_list

    def _create_cavity_polygon_from_entities(self, cavity_entities):
        """从实体组创建空腔多边形"""
        try:
            # 收集空腔组的所有线段
            segments = []
            for entity in cavity_entities:
                if 'points' in entity and len(entity['points']) >= 2:
                    segments.append(entity['points'])

            if not segments:
                return None

            # 尝试创建空腔多边形
            from shapely.geometry import LineString
            from shapely.ops import polygonize

            lines = []
            for seg in segments:
                if len(seg) >= 2:
                    try:
                        line = LineString(seg)
                        lines.append(line)
                    except Exception as e:
                        continue

            if not lines:
                return None

            # 尝试构建多边形
            polygons = list(polygonize(lines))
            if polygons:
                # 选择最大的多边形作为空腔
                largest_poly = max(polygons, key=lambda p: p.area)
                return largest_poly

            return None

        except Exception as e:
            print(f"从实体组创建空腔多边形失败: {e}")
            return None


    
    def _get_group_bbox(self, wall_group):
        """获取墙体组的边界框"""
        if not wall_group:
            return None
        
        try:
            min_x = min_y = float('inf')
            max_x = max_y = float('-inf')
            
            for entity in wall_group:
                points = self._get_entity_all_points(entity)
                for point in points:
                    x, y = point
                    min_x = min(min_x, x)
                    min_y = min(min_y, y)
                    max_x = max(max_x, x)
                    max_y = max(max_y, y)
            
            if min_x == float('inf'):
                return None
            
            return [min_x, min_y, max_x, max_y]
            
        except Exception as e:
            print(f"计算边界框失败: {e}")
            return None
    
    def _find_closed_paths_improved(self, segments):
        """查找封闭路径 - 改进版"""
        try:

            
            # 构建线段图
            segment_graph = self._build_segment_graph(segments)
            
            # 查找所有可能的封闭路径
            all_closed_paths = []
            visited = set()
            
            for start_seg in segments:
                if start_seg in visited:
                    continue
                
                # 尝试从每个线段开始查找封闭路径
                paths = self._find_all_closed_paths_from_segment(segment_graph, start_seg, visited)
                all_closed_paths.extend(paths)
            
            # 过滤和优化路径
            final_paths = self._filter_and_optimize_paths(all_closed_paths)
            
            print(f"找到封闭路径数量: {len(final_paths)}")
            return final_paths
            
        except Exception as e:
            print(f"查找封闭路径失败: {e}")
            return []
    
    def _find_all_closed_paths_from_segment(self, graph, start_seg, visited):
        """从指定线段开始查找所有可能的封闭路径"""
        paths = []
        
        def dfs(current_seg, path, current_visited):
            """深度优先搜索查找封闭路径"""
            if len(path) > len(graph):  # 防止无限循环
                return
            
            # 检查当前路径是否封闭
            if len(path) >= 3 and self._path_is_closed(path):
                # 检查路径是否已经存在（避免重复）
                path_tuple = tuple(sorted(path))
                if path_tuple not in visited_paths:
                    visited_paths.add(path_tuple)
                    paths.append(path[:])  # 复制路径
                return
            
            # 查找下一个连接的线段
            for neighbor in graph.get(current_seg, []):
                if neighbor not in current_visited:
                    current_visited.add(neighbor)
                    path.append(neighbor)
                    dfs(neighbor, path, current_visited)
                    path.pop()
                    current_visited.remove(neighbor)
        
        # 开始搜索
        current_visited = {start_seg}
        visited_paths = set()  # 用于去重
        dfs(start_seg, [start_seg], current_visited)
        
        return paths
    
    def _filter_and_optimize_paths(self, paths):
        """过滤和优化路径"""
        if not paths:
            return []
        
        print(f"原始路径数量: {len(paths)}")
        
        # 按长度排序，优先选择较长的路径
        paths.sort(key=len, reverse=True)
        
        # 过滤重复和包含的路径
        filtered_paths = []
        for path in paths:
            is_unique = True
            for existing_path in filtered_paths:
                # 检查是否被包含
                if self._path_contains(existing_path, path):
                    is_unique = False
                    break
                # 检查是否包含现有路径
                if self._path_contains(path, existing_path):
                    # 替换现有路径
                    filtered_paths.remove(existing_path)
                    break
            
            if is_unique:
                filtered_paths.append(path)
        

        
        # 只保留最长的几个路径
        if len(filtered_paths) > 5:
            filtered_paths = filtered_paths[:5]
            print(f"限制路径数量为: {len(filtered_paths)}")
        
        return filtered_paths
    
    def _path_contains(self, path1, path2):
        """检查path1是否包含path2"""
        if len(path1) < len(path2):
            return False
        
        # 检查path2的所有线段是否都在path1中
        path1_set = set(path1)
        path2_set = set(path2)
        
        return path2_set.issubset(path1_set) 

    def _connect_unconnected_parts(self, segments, threshold=10):
        """连接未连接的部分（使用10单位阈值）"""
        if not segments:
            return segments
        
        try:
            # 收集所有端点
            all_endpoints = []
            for seg in segments:
                all_endpoints.extend(seg)
            
            # 去重端点
            unique_endpoints = []
            for point in all_endpoints:
                is_duplicate = False
                for existing_point in unique_endpoints:
                    if self._points_close(point, existing_point, 1.0):
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_endpoints.append(point)
            
            # 查找需要连接的端点对
            new_segments = segments.copy()
            added_segments = []
            
            for i, point1 in enumerate(unique_endpoints):
                for j, point2 in enumerate(unique_endpoints):
                    if i != j:
                        distance = math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
                        if 0 < distance <= threshold:
                            # 检查是否已经存在连接这两个点的线段
                            already_connected = False
                            for seg in segments + added_segments:
                                if (self._points_close(seg[0], point1, 1.0) and self._points_close(seg[1], point2, 1.0)) or \
                                   (self._points_close(seg[0], point2, 1.0) and self._points_close(seg[1], point1, 1.0)):
                                    already_connected = True
                                    break
                            
                            if not already_connected:
                                new_segment = (point1, point2)
                                added_segments.append(new_segment)
                                print(f"连接未连接部分: {point1} -> {point2}, 距离: {distance:.2f}")
            
            new_segments.extend(added_segments)
            print(f"连接未连接部分: 添加了 {len(added_segments)} 个连接线段")
            return new_segments
            
        except Exception as e:
            print(f"连接未连接部分失败: {e}")
            return segments 

    def _close_gaps_advanced(self, segments, threshold):
        """基于方向预测的智能间隙闭合"""
        try:
            # 检查是否有rtree库
            try:
                import rtree
                RTREE_AVAILABLE = True
            except ImportError:
                RTREE_AVAILABLE = False
                print("警告：未找到rtree库，使用简化版间隙闭合")
            
            if not RTREE_AVAILABLE:
                return self._close_gaps_simple(segments, threshold)
            
            # 构建端点索引（使用R-tree加速）
            index = rtree.index.Index()
            endpoints = []
            for i, seg in enumerate(segments):
                for point in [seg[0], seg[1]]:
                    index.insert(i, (point[0], point[1], point[0], point[1]))
                    endpoints.append((point, i))
            
            # 查找可延伸的端点
            new_segments = segments.copy()
            for (point, seg_idx) in endpoints:
                # 计算线段方向向量
                seg = segments[seg_idx]
                vector = (
                    seg[1][0] - seg[0][0],
                    seg[1][1] - seg[0][1]
                )
                norm = max(1e-5, math.sqrt(vector[0]**2 + vector[1]**2))
                unit_vector = (vector[0]/norm, vector[1]/norm)
                
                # 延伸方向搜索
                search_dist = min(threshold * 2, norm * 0.5)  # 动态搜索距离
                target_point = (
                    point[0] + unit_vector[0] * search_dist,
                    point[1] + unit_vector[1] * search_dist
                )
                
                # 查找可连接点
                nearest = list(index.nearest(
                    (target_point[0], target_point[1], target_point[0], target_point[1]), 3
                ))
                
                for candidate_idx in nearest:
                    candidate_point = None
                    for cp in [segments[candidate_idx][0], segments[candidate_idx][1]]:
                        if math.hypot(cp[0]-target_point[0], cp[1]-target_point[1]) < threshold:
                            candidate_point = cp
                            break
                    
                    if candidate_point and not self._points_close(point, candidate_point, 1.0):
                        # 添加连接线段
                        new_segments.append((point, candidate_point))
                        print(f"智能连接: {point} -> {candidate_point}")
                        break
            
            return new_segments
            
        except Exception as e:
            print(f"智能间隙闭合失败: {e}")
            return segments
    
    def _close_gaps_simple(self, segments, threshold):
        """简化版间隙闭合（无rtree依赖）"""
        try:
            # 收集所有端点
            all_endpoints = []
            for seg in segments:
                all_endpoints.extend(seg)
            
            # 去重端点
            unique_endpoints = []
            for point in all_endpoints:
                is_duplicate = False
                for existing_point in unique_endpoints:
                    if self._points_close(point, existing_point, 1.0):
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_endpoints.append(point)
            
            # 查找需要连接的端点对
            new_segments = segments.copy()
            added_segments = []
            
            for i, point1 in enumerate(unique_endpoints):
                for j, point2 in enumerate(unique_endpoints):
                    if i != j:
                        distance = math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
                        if 0 < distance <= threshold:
                            # 检查是否已经存在连接这两个点的线段
                            already_connected = False
                            for seg in segments + added_segments:
                                if (self._points_close(seg[0], point1, 1.0) and self._points_close(seg[1], point2, 1.0)) or \
                                   (self._points_close(seg[0], point2, 1.0) and self._points_close(seg[1], point1, 1.0)):
                                    already_connected = True
                                    break
                            
                            if not already_connected:
                                new_segment = (point1, point2)
                                added_segments.append(new_segment)
                                print(f"简化版连接: {point1} -> {point2}, 距离: {distance:.2f}")
            
            new_segments.extend(added_segments)
            print(f"简化版间隙闭合: 添加了 {len(added_segments)} 个连接线段")
            return new_segments
            
        except Exception as e:
            print(f"简化版间隙闭合失败: {e}")
            return segments 

    # 移除几何融合方法，严格按照要求使用标准的polygonize方法
    # 不使用缓冲区融合等非标准方法
    
    def process_single_wall_group(self, segments):
        """处理单个墙体组，返回填充结果 - 严格使用_identify_outer_contour_improved的方式"""
        try:
            if not segments:
                return None

            # 严格按照要求：使用_identify_outer_contour_improved的方式
            # 合并重叠线段、智能闭合间隙（阈值20单位）、补全缺失墙端、确保形成封闭路径

            # 第一步：合并重叠线段
            merged_segments = self._merge_overlapping_segments_v2(segments)
            print(f"改进版重叠处理: {len(segments)} -> {len(merged_segments)}")

            # 第二步：智能闭合间隙（阈值20单位）
            gap_closed_segments = self._close_gaps_in_segments(merged_segments, 20)
            print(f"补全间隙后线段数量: {len(gap_closed_segments)}")

            # 第三步：补全缺失墙端
            end_completed_segments = self._complete_missing_wall_ends(gap_closed_segments, 20)
            print(f"补全端头后线段数量: {len(end_completed_segments)}")

            # 第四步：确保形成封闭路径
            closed_segments = self._ensure_closed_path(end_completed_segments)
            print(f"封闭路径后线段数量: {len(closed_segments)}")

            # 第五步：在交点处打断线段
            broken_segments = self._break_segments_at_intersections(closed_segments)
            print(f"打断后线段数量: {len(broken_segments)}")

            # 第六步：构建多边形（使用标准polygonize）
            from shapely.geometry import LineString
            from shapely.ops import polygonize

            lines = []
            for seg in broken_segments:
                if len(seg) >= 2:
                    try:
                        line = LineString(seg)
                        lines.append(line)
                    except Exception as e:
                        continue

            if not lines:
                print("没有有效的线段")
                return None

            # 使用标准polygonize构建多边形
            polygons = list(polygonize(lines))
            print(f"构建的多边形数量: {len(polygons)}")

            if polygons:
                # 选择面积最大的多边形
                polygons.sort(key=lambda p: p.area, reverse=True)
                largest_polygon = polygons[0]
                print(f"选择最大多边形，面积: {largest_polygon.area}")
                return largest_polygon

            print("无法构建多边形")
            return None

        except Exception as e:
            print(f"处理单个墙体组失败: {e}")
            return None
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试概览图颜色显示的调试脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_color_debug():
    """测试颜色调试功能"""
    print("🔍 开始测试概览图颜色显示...")
    
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppWithV2Fill
        
        print("✅ 成功导入主程序")
        
        # 创建应用实例（不启动GUI）
        print("🚀 创建应用实例...")
        
        # 模拟创建应用但不显示窗口
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = EnhancedCADAppWithV2Fill(root)
        
        print("✅ 应用实例创建成功")
        
        # 检查处理器是否有颜色方案方法
        if hasattr(app.processor, '_get_entity_color_from_scheme_enhanced'):
            print("✅ 处理器有颜色方案方法")
            
            # 测试颜色方案方法
            test_entity = {
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': None
            }
            
            try:
                color = app.processor._get_entity_color_from_scheme_enhanced(test_entity)
                print(f"✅ 颜色方案方法测试成功: {color}")
            except Exception as e:
                print(f"❌ 颜色方案方法测试失败: {e}")
        else:
            print("❌ 处理器缺少颜色方案方法")
        
        # 检查可视化器配色方案
        if hasattr(app.processor, 'visualizer') and app.processor.visualizer:
            print("✅ 可视化器存在")
            print(f"🎨 可视化器配色方案: {app.processor.visualizer.color_scheme}")
        else:
            print("❌ 可视化器不存在")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_debug_output():
    """检查调试输出是否正常"""
    print("\n🔍 检查调试输出...")
    
    # 模拟实体数据
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': None},
        {'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window'},
        {'type': 'INSERT', 'layer': '0', 'label': 'furniture'},
    ]
    
    # 模拟处理器
    class MockProcessor:
        def __init__(self):
            self.current_color_scheme = {
                'wall': '#8B4513',
                'door_window': '#FFD700',
                'furniture': '#4DB6AC',
                'other': '#808080'
            }
        
        def _get_entity_color_from_scheme_enhanced(self, entity):
            print(f"    🎨 主程序颜色方案调用: entity={entity.get('type')}, label={entity.get('label')}, layer={entity.get('layer')}")
            
            # 优先使用实体的标签
            if entity.get('label'):
                label = entity['label']
                label_color_map = {
                    'wall': self.current_color_scheme.get('wall', '#8B4513'),
                    'door_window': self.current_color_scheme.get('door_window', '#FFD700'),
                    'furniture': self.current_color_scheme.get('furniture', '#4DB6AC'),
                }
                color = label_color_map.get(label, self.current_color_scheme.get('other', '#808080'))
                print(f"    🎨 标签颜色: {label} -> {color}")
                return color
            
            # 根据图层判断
            layer_name = str(entity.get('layer', '')).lower()
            if 'wall' in layer_name or 'a-wall' in layer_name:
                color = self.current_color_scheme.get('wall', '#8B4513')
                print(f"    🎨 图层颜色(墙体): {layer_name} -> {color}")
                return color
            elif 'door' in layer_name or 'window' in layer_name:
                color = self.current_color_scheme.get('door_window', '#FFD700')
                print(f"    🎨 图层颜色(门窗): {layer_name} -> {color}")
                return color
            else:
                color = self.current_color_scheme.get('other', '#808080')
                print(f"    🎨 默认颜色: {color}")
                return color
    
    processor = MockProcessor()
    
    print("测试实体颜色获取:")
    for i, entity in enumerate(test_entities):
        print(f"\n实体{i+1}: {entity}")
        try:
            color = processor._get_entity_color_from_scheme_enhanced(entity)
            print(f"最终颜色: {color}")
            
            if color == '#808080':
                print("⚠️ 显示为灰色，可能存在问题")
            else:
                print("✅ 颜色正常")
        except Exception as e:
            print(f"❌ 颜色获取失败: {e}")
    
    return True

def main():
    """主函数"""
    print("🚀 概览图颜色显示调试测试")
    print("=" * 50)
    
    tests = [
        ("应用程序测试", test_color_debug),
        ("调试输出检查", check_debug_output),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"结果: {status}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print("\n🔍 问题诊断建议:")
    print("1. 运行程序并查看控制台输出")
    print("2. 检查是否有调试信息输出")
    print("3. 确认处理器的颜色方案方法是否被调用")
    print("4. 检查可视化器的配色方案是否正确更新")
    print("5. 验证实体标签和图层信息是否正确")

if __name__ == "__main__":
    main()

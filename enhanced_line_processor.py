#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强线条处理模块
确保按图层分别处理，避免不同图层数据混合和信息丢失
"""

import copy
import time
from typing import List, Dict, Any, Optional
from collections import defaultdict

from layer_data_container import LayerDataContainer, LayerType


class LayerAwareLineProcessor:
    """
    图层感知的线条处理器
    
    确保：
    1. 按图层分别处理线条
    2. 保持图层类型信息
    3. 避免不同图层数据混合
    4. 处理过程可追溯
    """
    
    def __init__(self):
        """初始化处理器"""
        self.processing_stats = {
            'total_entities_processed': 0,
            'layers_processed': 0,
            'processing_time': 0.0,
            'layer_stats': {}
        }
        
        # 图层特定的处理配置
        self.layer_configs = {
            LayerType.WALL: {
                'merge_threshold': 5.0,      # 墙体线条合并阈值
                'angle_threshold': 2.0,      # 角度阈值
                'enable_merging': True,      # 启用合并
                'connection_precision': 'high'  # 连接精度
            },
            LayerType.DOOR_WINDOW: {
                'merge_threshold': 10.0,     # 门窗线条合并阈值
                'angle_threshold': 5.0,      # 角度阈值
                'enable_merging': True,      # 启用合并
                'connection_precision': 'medium'  # 连接精度
            },
            LayerType.RAILING: {
                'merge_threshold': 8.0,      # 栏杆线条合并阈值
                'angle_threshold': 3.0,      # 角度阈值
                'enable_merging': True,      # 启用合并
                'connection_precision': 'medium'  # 连接精度
            },
            LayerType.TEXT: {
                'enable_merging': False,     # 文字不合并
                'preserve_original': True    # 保持原始状态
            },
            LayerType.DIMENSION: {
                'enable_merging': False,     # 标注不合并
                'preserve_original': True    # 保持原始状态
            },
            LayerType.OTHER: {
                'merge_threshold': 15.0,     # 其他线条合并阈值
                'angle_threshold': 10.0,     # 角度阈值
                'enable_merging': False,     # 默认不合并
                'connection_precision': 'low'  # 连接精度
            }
        }
        
        print("🔧 图层感知线条处理器初始化完成")
    
    def process_containers(self, containers: List[LayerDataContainer]) -> List[LayerDataContainer]:
        """
        处理图层容器列表
        
        Args:
            containers: 图层容器列表
            
        Returns:
            处理后的图层容器列表
        """
        print(f"🚀 开始处理 {len(containers)} 个图层容器的线条")
        
        processed_containers = []
        start_time = time.time()
        
        for i, container in enumerate(containers):
            print(f"\n📦 处理容器 {i+1}/{len(containers)}: {container.layer_name} ({container.layer_type.value})")
            
            try:
                processed_container = self.process_single_container(container)
                processed_containers.append(processed_container)
                
            except Exception as e:
                print(f"  ❌ 处理容器失败: {e}")
                # 返回原始容器，避免数据丢失
                processed_containers.append(container)
        
        # 更新统计信息
        total_time = time.time() - start_time
        self.processing_stats['layers_processed'] = len(containers)
        self.processing_stats['processing_time'] = total_time
        
        print(f"\n🎉 线条处理完成，耗时 {total_time:.2f} 秒")
        
        return processed_containers
    
    def process_single_container(self, container: LayerDataContainer) -> LayerDataContainer:
        """
        处理单个图层容器
        
        Args:
            container: 图层容器
            
        Returns:
            处理后的图层容器
        """
        start_time = time.time()
        
        # 获取图层配置
        config = self.layer_configs.get(container.layer_type, self.layer_configs[LayerType.OTHER])
        
        # 创建处理后的容器
        processed_container = container.create_deep_copy()
        
        # 获取原始实体
        entities = container.get_entities_by_stage("original")
        
        if not entities:
            print(f"  ⚠️ 容器无实体数据")
            return processed_container
        
        # 根据图层类型选择处理方式
        if config.get('preserve_original', False):
            # 保持原始状态（文字、标注等）
            processed_entities = self._preserve_entities(entities, container.layer_type)
        elif config.get('enable_merging', False):
            # 执行线条合并处理
            processed_entities = self._merge_line_entities(entities, config, container.layer_type)
        else:
            # 基本处理（添加类型信息）
            processed_entities = self._basic_process_entities(entities, container.layer_type)
        
        # 更新容器
        processed_container.add_entities(processed_entities, "processed")
        
        # 记录处理统计
        processing_time = time.time() - start_time
        layer_name = container.layer_name
        
        if layer_name not in self.processing_stats['layer_stats']:
            self.processing_stats['layer_stats'][layer_name] = {
                'entities_processed': 0,
                'processing_time': 0.0
            }
        
        self.processing_stats['layer_stats'][layer_name]['entities_processed'] += len(entities)
        self.processing_stats['layer_stats'][layer_name]['processing_time'] += processing_time
        self.processing_stats['total_entities_processed'] += len(entities)
        
        print(f"  ✅ 处理完成: {len(entities)} -> {len(processed_entities)} 个实体")
        
        return processed_container
    
    def _preserve_entities(self, entities: List[Dict[str, Any]], layer_type: LayerType) -> List[Dict[str, Any]]:
        """
        保持实体原始状态（用于文字、标注等）
        
        Args:
            entities: 实体列表
            layer_type: 图层类型
            
        Returns:
            处理后的实体列表
        """
        print(f"  📝 保持原始状态处理 ({layer_type.value})")
        
        processed_entities = []
        
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                
                # 添加处理信息
                entity_copy['line_processed'] = True
                entity_copy['processing_type'] = 'preserve_original'
                entity_copy['layer_type_confirmed'] = layer_type.value
                entity_copy['processing_timestamp'] = time.time()
                
                processed_entities.append(entity_copy)
        
        return processed_entities
    
    def _merge_line_entities(self, entities: List[Dict[str, Any]], config: Dict[str, Any], 
                           layer_type: LayerType) -> List[Dict[str, Any]]:
        """
        合并线条实体
        
        Args:
            entities: 实体列表
            config: 图层配置
            layer_type: 图层类型
            
        Returns:
            处理后的实体列表
        """
        print(f"  🔗 线条合并处理 ({layer_type.value})")
        
        # 分离线条和非线条实体
        line_entities = []
        non_line_entities = []
        
        for entity in entities:
            if self._is_line_entity(entity):
                line_entities.append(entity)
            else:
                non_line_entities.append(entity)
        
        print(f"    线条实体: {len(line_entities)}, 非线条实体: {len(non_line_entities)}")
        
        if not line_entities:
            # 没有线条实体，直接返回基本处理结果
            return self._basic_process_entities(entities, layer_type)
        
        # 执行线条合并（这里可以集成现有的线条合并逻辑）
        merged_lines = self._perform_line_merging(line_entities, config)
        
        # 处理非线条实体
        processed_non_lines = self._basic_process_entities(non_line_entities, layer_type)
        
        # 合并结果
        all_processed = merged_lines + processed_non_lines
        
        print(f"    合并结果: {len(line_entities)} -> {len(merged_lines)} 线条, {len(non_line_entities)} 其他")
        
        return all_processed
    
    def _basic_process_entities(self, entities: List[Dict[str, Any]], layer_type: LayerType) -> List[Dict[str, Any]]:
        """
        基本实体处理（添加类型信息）
        
        Args:
            entities: 实体列表
            layer_type: 图层类型
            
        Returns:
            处理后的实体列表
        """
        processed_entities = []
        
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                
                # 添加处理信息
                entity_copy['line_processed'] = True
                entity_copy['processing_type'] = 'basic_process'
                entity_copy['layer_type_confirmed'] = layer_type.value
                entity_copy['processing_timestamp'] = time.time()
                
                processed_entities.append(entity_copy)
        
        return processed_entities
    
    def _is_line_entity(self, entity: Dict[str, Any]) -> bool:
        """
        判断实体是否为线条实体
        
        Args:
            entity: 实体数据
            
        Returns:
            是否为线条实体
        """
        entity_type = entity.get('type', '').upper()
        line_types = ['LINE', 'LWPOLYLINE', 'POLYLINE', 'ARC', 'CIRCLE', 'ELLIPSE']
        return entity_type in line_types
    
    def _perform_line_merging(self, line_entities: List[Dict[str, Any]], 
                            config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        执行线条合并
        
        Args:
            line_entities: 线条实体列表
            config: 配置参数
            
        Returns:
            合并后的线条实体列表
        """
        # 这里可以集成现有的线条合并算法
        # 暂时返回基本处理结果
        
        merged_entities = []
        
        for entity in line_entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                
                # 添加合并处理信息
                entity_copy['line_processed'] = True
                entity_copy['processing_type'] = 'line_merged'
                entity_copy['merge_threshold'] = config.get('merge_threshold', 0)
                entity_copy['angle_threshold'] = config.get('angle_threshold', 0)
                entity_copy['processing_timestamp'] = time.time()
                
                merged_entities.append(entity_copy)
        
        return merged_entities
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        return {
            'processing_stats': self.processing_stats,
            'layer_configs': {
                layer_type.value: config 
                for layer_type, config in self.layer_configs.items()
            }
        }

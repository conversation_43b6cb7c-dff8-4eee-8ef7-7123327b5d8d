#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图层处理管道模块
确保各图层数据分离处理，避免数据混合和信息丢失
"""

import copy
import time
from typing import List, Dict, Any, Optional, Callable
from abc import ABC, abstractmethod

from layer_data_container import LayerDataContainer, LayerType


class LayerProcessor(ABC):
    """图层处理器基类"""
    
    def __init__(self, processor_name: str):
        self.processor_name = processor_name
        self.processing_stats = {
            'processed_containers': 0,
            'total_entities': 0,
            'processing_time': 0.0
        }
    
    @abstractmethod
    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """
        处理图层数据容器
        
        Args:
            container: 输入的图层数据容器
            
        Returns:
            处理后的图层数据容器
        """
        pass
    
    def _update_stats(self, container: LayerDataContainer, processing_time: float):
        """更新处理统计"""
        self.processing_stats['processed_containers'] += 1
        self.processing_stats['total_entities'] += len(container.get_all_entities())
        self.processing_stats['processing_time'] += processing_time


class WallLayerProcessor(LayerProcessor):
    """墙体图层处理器"""
    
    def __init__(self):
        super().__init__("WallLayerProcessor")
        self.connection_threshold = 10  # 墙体连接阈值
    
    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理墙体图层数据"""
        start_time = time.time()
        
        print(f"🏗️ 开始处理墙体图层: {container.layer_name}")
        
        # 创建处理后的容器
        processed_container = container.create_deep_copy()
        
        # 获取原始实体
        entities = container.get_entities_by_stage("original")
        
        if not entities:
            print(f"  ⚠️ 墙体图层无实体数据")
            return processed_container
        
        # 墙体特殊处理：线条合并和精确连接
        processed_entities = self._process_wall_entities(entities)
        
        # 墙体分组：使用精确端点连接
        wall_groups = self._group_wall_entities(processed_entities)
        
        # 更新容器
        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(wall_groups, self.processor_name)
        processed_container.is_processed = True
        
        # 自动标注墙体实体
        labeled_entities = self._auto_label_wall_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)
        
        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)
        
        print(f"  ✅ 墙体图层处理完成: {len(processed_entities)} 个实体, {len(wall_groups)} 个组")
        
        return processed_container
    
    def _process_wall_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理墙体实体：线条合并等"""
        processed = []
        
        for entity in entities:
            # 确保实体包含墙体类型信息
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'wall'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        
        return processed
    
    def _group_wall_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """墙体实体分组：使用精确端点连接"""
        # 这里可以集成现有的墙体分组逻辑
        # 暂时按图层简单分组
        if entities:
            return [entities]  # 单个组
        return []
    
    def _auto_label_wall_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注墙体实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'wall'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.9
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class DoorWindowLayerProcessor(LayerProcessor):
    """门窗图层处理器"""
    
    def __init__(self):
        super().__init__("DoorWindowLayerProcessor")
        self.proximity_threshold = 80  # 门窗邻近阈值
    
    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理门窗图层数据"""
        start_time = time.time()
        
        print(f"🚪 开始处理门窗图层: {container.layer_name}")
        
        # 创建处理后的容器
        processed_container = container.create_deep_copy()
        
        # 获取原始实体
        entities = container.get_entities_by_stage("original")
        
        if not entities:
            print(f"  ⚠️ 门窗图层无实体数据")
            return processed_container
        
        # 门窗特殊处理：重叠线条处理
        processed_entities = self._process_door_window_entities(entities)
        
        # 门窗分组：使用邻近性分组
        door_window_groups = self._group_door_window_entities(processed_entities)
        
        # 更新容器
        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(door_window_groups, self.processor_name)
        processed_container.is_processed = True
        
        # 自动标注门窗实体
        labeled_entities = self._auto_label_door_window_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)
        
        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)
        
        print(f"  ✅ 门窗图层处理完成: {len(processed_entities)} 个实体, {len(door_window_groups)} 个组")
        
        return processed_container
    
    def _process_door_window_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理门窗实体：重叠线条处理等"""
        processed = []
        
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'door_window'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        
        return processed
    
    def _group_door_window_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """门窗实体分组：使用邻近性分组"""
        if entities:
            return [entities]  # 单个组
        return []
    
    def _auto_label_door_window_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注门窗实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'door_window'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.85
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class RailingLayerProcessor(LayerProcessor):
    """栏杆图层处理器"""
    
    def __init__(self):
        super().__init__("RailingLayerProcessor")
    
    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理栏杆图层数据"""
        start_time = time.time()
        
        print(f"🛡️ 开始处理栏杆图层: {container.layer_name}")
        
        processed_container = container.create_deep_copy()
        entities = container.get_entities_by_stage("original")
        
        if not entities:
            print(f"  ⚠️ 栏杆图层无实体数据")
            return processed_container
        
        # 栏杆处理
        processed_entities = self._process_railing_entities(entities)
        railing_groups = self._group_railing_entities(processed_entities)
        
        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(railing_groups, self.processor_name)
        processed_container.is_processed = True
        
        labeled_entities = self._auto_label_railing_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)
        
        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)
        
        print(f"  ✅ 栏杆图层处理完成: {len(processed_entities)} 个实体, {len(railing_groups)} 个组")
        
        return processed_container
    
    def _process_railing_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理栏杆实体"""
        processed = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'railing'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        return processed
    
    def _group_railing_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """栏杆实体分组"""
        if entities:
            return [entities]
        return []
    
    def _auto_label_railing_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注栏杆实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'railing'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.8
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class TextLayerProcessor(LayerProcessor):
    """文字图层处理器"""

    def __init__(self):
        super().__init__("TextLayerProcessor")

    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理文字图层数据"""
        start_time = time.time()

        print(f"📝 开始处理文字图层: {container.layer_name}")

        processed_container = container.create_deep_copy()
        entities = container.get_entities_by_stage("original")

        if not entities:
            print(f"  ⚠️ 文字图层无实体数据")
            return processed_container

        # 文字处理：按位置和内容分组
        processed_entities = self._process_text_entities(entities)
        text_groups = self._group_text_entities(processed_entities)

        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(text_groups, self.processor_name)
        processed_container.is_processed = True

        labeled_entities = self._auto_label_text_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)

        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)

        print(f"  ✅ 文字图层处理完成: {len(processed_entities)} 个实体, {len(text_groups)} 个组")

        return processed_container

    def _process_text_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理文字实体"""
        processed = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'text'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        return processed

    def _group_text_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """文字实体分组：按图层分组"""
        if entities:
            return [entities]
        return []

    def _auto_label_text_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注文字实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'text'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.95
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class DimensionLayerProcessor(LayerProcessor):
    """标注图层处理器"""

    def __init__(self):
        super().__init__("DimensionLayerProcessor")

    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理标注图层数据"""
        start_time = time.time()

        print(f"📏 开始处理标注图层: {container.layer_name}")

        processed_container = container.create_deep_copy()
        entities = container.get_entities_by_stage("original")

        if not entities:
            print(f"  ⚠️ 标注图层无实体数据")
            return processed_container

        # 标注处理
        processed_entities = self._process_dimension_entities(entities)
        dimension_groups = self._group_dimension_entities(processed_entities)

        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(dimension_groups, self.processor_name)
        processed_container.is_processed = True

        labeled_entities = self._auto_label_dimension_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)

        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)

        print(f"  ✅ 标注图层处理完成: {len(processed_entities)} 个实体, {len(dimension_groups)} 个组")

        return processed_container

    def _process_dimension_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理标注实体"""
        processed = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'dimension'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        return processed

    def _group_dimension_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """标注实体分组"""
        if entities:
            return [entities]
        return []

    def _auto_label_dimension_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注标注实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'dimension'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.9
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class OtherLayerProcessor(LayerProcessor):
    """其他图层处理器"""

    def __init__(self):
        super().__init__("OtherLayerProcessor")

    def process(self, container: LayerDataContainer) -> LayerDataContainer:
        """处理其他图层数据"""
        start_time = time.time()

        print(f"🔧 开始处理其他图层: {container.layer_name}")

        processed_container = container.create_deep_copy()
        entities = container.get_entities_by_stage("original")

        if not entities:
            print(f"  ⚠️ 其他图层无实体数据")
            return processed_container

        # 其他图层的通用处理
        processed_entities = self._process_other_entities(entities)
        other_groups = self._group_other_entities(processed_entities)

        processed_container.add_entities(processed_entities, "processed")
        processed_container.set_groups(other_groups, self.processor_name)
        processed_container.is_processed = True

        labeled_entities = self._auto_label_other_entities(processed_entities)
        processed_container.set_labeled_entities(labeled_entities, self.processor_name)

        processing_time = time.time() - start_time
        self._update_stats(processed_container, processing_time)

        print(f"  ✅ 其他图层处理完成: {len(processed_entities)} 个实体, {len(other_groups)} 个组")

        return processed_container

    def _process_other_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理其他实体"""
        processed = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = self.processor_name
                entity_copy['entity_category'] = 'other'
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
        return processed

    def _group_other_entities(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """其他实体分组"""
        if entities:
            return [entities]
        return []

    def _auto_label_other_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自动标注其他实体"""
        labeled = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = copy.deepcopy(entity)
                entity_copy['label'] = 'other'
                entity_copy['auto_labeled'] = True
                entity_copy['confidence'] = 0.5
                entity_copy['labeled_by'] = self.processor_name
                labeled.append(entity_copy)
        return labeled


class LayerProcessingPipeline:
    """
    图层处理管道

    确保各图层数据分离处理，避免数据混合和信息丢失
    """

    def __init__(self):
        """初始化处理管道"""
        # 初始化各类型处理器
        self.processors = {
            LayerType.WALL: WallLayerProcessor(),
            LayerType.DOOR_WINDOW: DoorWindowLayerProcessor(),
            LayerType.RAILING: RailingLayerProcessor(),
            LayerType.TEXT: TextLayerProcessor(),
            LayerType.DIMENSION: DimensionLayerProcessor(),
            LayerType.OTHER: OtherLayerProcessor()
        }

        # 处理统计
        self.pipeline_stats = {
            'total_containers_processed': 0,
            'total_entities_processed': 0,
            'total_processing_time': 0.0,
            'processor_stats': {}
        }

        print("🔧 图层处理管道初始化完成")

    def process_containers(self, containers: List[LayerDataContainer]) -> List[LayerDataContainer]:
        """
        批量处理图层容器

        Args:
            containers: 图层容器列表

        Returns:
            处理后的图层容器列表
        """
        print(f"🚀 开始批量处理 {len(containers)} 个图层容器")

        processed_containers = []
        start_time = time.time()

        for i, container in enumerate(containers):
            print(f"\n📦 处理容器 {i+1}/{len(containers)}: {container.layer_name} ({container.layer_type.value})")

            try:
                processed_container = self.process_single_container(container)
                processed_containers.append(processed_container)

                # 验证处理结果
                validation_result = processed_container.validate_data_integrity()
                if not validation_result['is_valid']:
                    print(f"  ⚠️ 容器验证失败: {validation_result['errors']}")
                else:
                    print(f"  ✅ 容器验证通过")

            except Exception as e:
                print(f"  ❌ 处理容器失败: {e}")
                # 返回原始容器，避免数据丢失
                processed_containers.append(container)

        # 更新统计信息
        total_time = time.time() - start_time
        self.pipeline_stats['total_containers_processed'] += len(containers)
        self.pipeline_stats['total_processing_time'] += total_time

        print(f"\n🎉 批量处理完成，耗时 {total_time:.2f} 秒")

        return processed_containers

    def process_single_container(self, container: LayerDataContainer) -> LayerDataContainer:
        """
        处理单个图层容器

        Args:
            container: 图层容器

        Returns:
            处理后的图层容器
        """
        # 获取对应的处理器
        processor = self.processors.get(container.layer_type)

        if not processor:
            print(f"  ⚠️ 未找到 {container.layer_type.value} 类型的处理器，使用默认处理器")
            processor = self.processors[LayerType.OTHER]

        # 执行处理
        processed_container = processor.process(container)

        # 更新处理器统计
        processor_name = processor.processor_name
        if processor_name not in self.pipeline_stats['processor_stats']:
            self.pipeline_stats['processor_stats'][processor_name] = processor.processing_stats.copy()
        else:
            stats = self.pipeline_stats['processor_stats'][processor_name]
            stats['processed_containers'] += processor.processing_stats['processed_containers']
            stats['total_entities'] += processor.processing_stats['total_entities']
            stats['processing_time'] += processor.processing_stats['processing_time']

        return processed_container

    def create_containers_from_entities(self, entities: List[Dict[str, Any]],
                                      layer_classifier: Callable = None) -> List[LayerDataContainer]:
        """
        从实体列表创建图层容器

        Args:
            entities: 实体列表
            layer_classifier: 图层分类函数

        Returns:
            图层容器列表
        """
        print(f"📋 从 {len(entities)} 个实体创建图层容器")

        # 按图层分组实体
        layer_groups = {}

        for entity in entities:
            if not isinstance(entity, dict):
                continue

            layer_name = entity.get('layer', 'UNKNOWN')

            # 使用分类器确定图层类型
            if layer_classifier:
                layer_type = layer_classifier(entity)
            else:
                layer_type = self._classify_layer_type(layer_name, entity)

            # 创建图层组键
            layer_key = f"{layer_name}_{layer_type.value}"

            if layer_key not in layer_groups:
                layer_groups[layer_key] = {
                    'layer_name': layer_name,
                    'layer_type': layer_type,
                    'entities': []
                }

            layer_groups[layer_key]['entities'].append(entity)

        # 创建容器
        containers = []
        for layer_key, layer_data in layer_groups.items():
            container = LayerDataContainer(
                layer_name=layer_data['layer_name'],
                layer_type=layer_data['layer_type'],
                original_entities=layer_data['entities']
            )
            containers.append(container)

            print(f"  📦 创建容器: {layer_data['layer_name']} ({layer_data['layer_type'].value}) - {len(layer_data['entities'])} 个实体")

        print(f"✅ 创建了 {len(containers)} 个图层容器")

        return containers

    def _classify_layer_type(self, layer_name: str, entity: Dict[str, Any]) -> LayerType:
        """
        分类图层类型

        Args:
            layer_name: 图层名称
            entity: 实体数据

        Returns:
            图层类型
        """
        layer_name_lower = layer_name.lower()

        # 墙体图层模式
        wall_patterns = ['wall', '墙', 'a-wall', 'arch-wall']
        if any(pattern in layer_name_lower for pattern in wall_patterns):
            return LayerType.WALL

        # 门窗图层模式
        door_window_patterns = ['door', 'window', '门', '窗', 'a-door', 'a-wind']
        if any(pattern in layer_name_lower for pattern in door_window_patterns):
            return LayerType.DOOR_WINDOW

        # 栏杆图层模式
        railing_patterns = ['rail', '栏杆', '护栏', 'a-rail']
        if any(pattern in layer_name_lower for pattern in railing_patterns):
            return LayerType.RAILING

        # 文字图层模式
        text_patterns = ['text', '文字', '标注', 'a-anno', 'anno']
        if any(pattern in layer_name_lower for pattern in text_patterns):
            return LayerType.TEXT

        # 标注图层模式
        dimension_patterns = ['dim', 'dimension', '尺寸', 'a-dims']
        if any(pattern in layer_name_lower for pattern in dimension_patterns):
            return LayerType.DIMENSION

        # 根据实体类型判断
        entity_type = entity.get('type', '').upper()
        if entity_type in ['TEXT', 'MTEXT']:
            return LayerType.TEXT
        elif entity_type in ['DIMENSION', 'ALIGNED_DIMENSION', 'LINEAR_DIMENSION']:
            return LayerType.DIMENSION

        # 默认为其他类型
        return LayerType.OTHER

    def get_pipeline_summary(self) -> Dict[str, Any]:
        """获取管道处理摘要"""
        return {
            'pipeline_stats': self.pipeline_stats,
            'available_processors': list(self.processors.keys()),
            'processor_details': {
                processor_type.value: processor.processing_stats
                for processor_type, processor in self.processors.items()
            }
        }

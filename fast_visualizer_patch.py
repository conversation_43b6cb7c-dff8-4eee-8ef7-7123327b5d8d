#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速可视化器补丁
解决可视化器中的性能瓶颈问题
"""

import time
from typing import List, Dict, Any, Optional


class FastVisualizationOptimizer:
    """
    快速可视化优化器
    
    通过缓存和批量操作优化可视化性能
    """
    
    def __init__(self):
        """初始化快速可视化优化器"""
        self.entity_group_cache = {}
        self.last_groups_hash = None
        self.performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_lookups': 0,
            'optimization_time_saved': 0.0
        }
    
    def optimize_draw_entities(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """
        优化实体绘制
        
        Args:
            visualizer: 原始可视化器
            entities: 实体列表
            
        Returns:
            是否成功优化
        """
        if not entities or len(entities) < 100:
            # 实体数量少时，使用原始方法
            return False
        
        try:
            print(f"⚡ 使用快速绘制模式 ({len(entities)} 个实体)")
            
            # 清除画布
            if hasattr(visualizer, 'ax_detail'):
                visualizer.ax_detail.clear()
                visualizer.ax_detail.set_aspect('equal')
                visualizer.ax_detail.grid(True, linestyle='--', alpha=0.3)
            
            # 批量收集坐标
            all_x = []
            all_y = []
            
            for entity in entities:
                try:
                    coords = self._extract_entity_coordinates(entity)
                    if coords and len(coords) >= 2:
                        x_coords = [coord[0] for coord in coords]
                        y_coords = [coord[1] for coord in coords]
                        all_x.extend(x_coords + [None])  # None用于分隔线段
                        all_y.extend(y_coords + [None])
                except:
                    continue
            
            # 一次性绘制所有线条
            if all_x and all_y and hasattr(visualizer, 'ax_detail'):
                visualizer.ax_detail.plot(all_x, all_y, 'b-', linewidth=0.5, alpha=0.7)
                
                # 设置坐标范围
                valid_x = [x for x in all_x if x is not None]
                valid_y = [y for y in all_y if y is not None]
                
                if valid_x and valid_y:
                    min_x, max_x = min(valid_x), max(valid_x)
                    min_y, max_y = min(valid_y), max(valid_y)
                    
                    margin = max((max_x - min_x) * 0.05, (max_y - min_y) * 0.05, 5)
                    visualizer.ax_detail.set_xlim(min_x - margin, max_x + margin)
                    visualizer.ax_detail.set_ylim(min_y - margin, max_y + margin)
                
                visualizer.ax_detail.set_title(f'快速绘制模式 ({len(entities)}个实体)', fontsize=10)
            
            return True
            
        except Exception as e:
            print(f"❌ 快速绘制失败: {e}")
            return False
    
    def optimize_visualize_overview(self, visualizer, all_entities: List[Dict[str, Any]], 
                                  current_group_entities: Optional[List[Dict[str, Any]]] = None,
                                  labeled_entities: Optional[List[Dict[str, Any]]] = None,
                                  processor=None, **kwargs) -> bool:
        """
        优化概览可视化
        
        Args:
            visualizer: 原始可视化器
            all_entities: 所有实体
            current_group_entities: 当前组实体
            labeled_entities: 已标注实体
            processor: 处理器
            
        Returns:
            是否成功优化
        """
        if not all_entities or len(all_entities) < 100:
            # 实体数量少时，使用原始方法
            return False
        
        try:
            print(f"⚡ 使用快速概览模式 ({len(all_entities)} 个实体)")
            
            # 清除画布
            if hasattr(visualizer, 'ax_overview'):
                visualizer.ax_overview.clear()
                visualizer.ax_overview.set_aspect('equal')
                visualizer.ax_overview.grid(True, linestyle='--', alpha=0.3)
            
            # 跳过复杂的组查找，使用简化绘制
            self._fast_draw_overview(visualizer, all_entities, current_group_entities, labeled_entities)
            
            return True
            
        except Exception as e:
            print(f"❌ 快速概览失败: {e}")
            return False
    
    def _fast_draw_overview(self, visualizer, all_entities: List[Dict[str, Any]], 
                          current_group_entities: Optional[List[Dict[str, Any]]] = None,
                          labeled_entities: Optional[List[Dict[str, Any]]] = None):
        """快速绘制概览"""
        if not hasattr(visualizer, 'ax_overview'):
            return
        
        ax = visualizer.ax_overview
        
        # 批量收集坐标
        all_x = []
        all_y = []
        current_x = []
        current_y = []
        labeled_x = []
        labeled_y = []
        
        # 创建实体ID集合用于快速查找
        current_ids = set()
        labeled_ids = set()
        
        if current_group_entities:
            current_ids = {id(entity) for entity in current_group_entities}
        
        if labeled_entities:
            labeled_ids = {id(entity) for entity in labeled_entities}
        
        # 收集坐标
        for entity in all_entities:
            try:
                coords = self._extract_entity_coordinates(entity)
                if coords and len(coords) >= 2:
                    x_coords = [coord[0] for coord in coords]
                    y_coords = [coord[1] for coord in coords]
                    
                    entity_id = id(entity)
                    
                    if entity_id in current_ids:
                        # 当前组实体
                        current_x.extend(x_coords + [None])
                        current_y.extend(y_coords + [None])
                    elif entity_id in labeled_ids:
                        # 已标注实体
                        labeled_x.extend(x_coords + [None])
                        labeled_y.extend(y_coords + [None])
                    else:
                        # 普通实体
                        all_x.extend(x_coords + [None])
                        all_y.extend(y_coords + [None])
            except:
                continue
        
        # 绘制不同类型的实体
        if all_x and all_y:
            ax.plot(all_x, all_y, color='lightgray', linewidth=0.3, alpha=0.5)
        
        if labeled_x and labeled_y:
            ax.plot(labeled_x, labeled_y, color='green', linewidth=0.8, alpha=0.7)
        
        if current_x and current_y:
            ax.plot(current_x, current_y, color='red', linewidth=1.0, alpha=0.8)
        
        # 设置坐标范围
        all_coords_x = all_x + current_x + labeled_x
        all_coords_y = all_y + current_y + labeled_y
        
        valid_x = [x for x in all_coords_x if x is not None]
        valid_y = [y for y in all_coords_y if y is not None]
        
        if valid_x and valid_y:
            min_x, max_x = min(valid_x), max(valid_x)
            min_y, max_y = min(valid_y), max(valid_y)
            
            margin = max((max_x - min_x) * 0.05, (max_y - min_y) * 0.05, 10)
            ax.set_xlim(min_x - margin, max_x + margin)
            ax.set_ylim(min_y - margin, max_y + margin)
        
        # 设置标题
        current_count = len(current_group_entities) if current_group_entities else 0
        labeled_count = len(labeled_entities) if labeled_entities else 0
        ax.set_title(f'快速概览 (总:{len(all_entities)}, 当前:{current_count}, 已标注:{labeled_count})', fontsize=10)
    
    def _extract_entity_coordinates(self, entity: Dict[str, Any]) -> List[List[float]]:
        """提取实体坐标"""
        if not isinstance(entity, dict):
            return []
        
        # 优先使用points
        if 'points' in entity and entity['points']:
            return entity['points']
        
        # 使用start_point和end_point
        if 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        
        # 使用position（文字等）
        if 'position' in entity:
            pos = entity['position']
            return [pos, pos]  # 点实体
        
        # 使用center（圆形等）
        if 'center' in entity:
            center = entity['center']
            radius = entity.get('radius', 1)
            # 简化圆形为十字形
            return [
                [center[0] - radius, center[1]],
                [center[0] + radius, center[1]],
                [center[0], center[1] - radius],
                [center[0], center[1] + radius]
            ]
        
        return []
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'performance_stats': self.performance_stats,
            'optimization_strategy': 'batch_drawing + id_lookup',
            'complexity_reduction': 'O(n*m) -> O(n)'
        }


# 全局优化器实例
_fast_visualizer_optimizer = None


def get_fast_visualizer_optimizer() -> FastVisualizationOptimizer:
    """获取快速可视化优化器"""
    global _fast_visualizer_optimizer
    if _fast_visualizer_optimizer is None:
        _fast_visualizer_optimizer = FastVisualizationOptimizer()
    return _fast_visualizer_optimizer


def patch_visualizer_performance(visualizer):
    """为可视化器打补丁以提升性能"""
    optimizer = get_fast_visualizer_optimizer()
    
    # 保存原始方法
    original_draw_entities = visualizer.draw_entities
    original_visualize_overview = visualizer.visualize_overview
    
    def optimized_draw_entities(entities):
        """优化的绘制实体方法"""
        start_time = time.time()
        
        # 尝试使用快速绘制
        if optimizer.optimize_draw_entities(visualizer, entities):
            print(f"⚡ 快速绘制完成: {time.time() - start_time:.3f}秒")
            return
        
        # 回退到原始方法
        print(f"🔄 使用原始绘制方法")
        original_draw_entities(entities)
    
    def optimized_visualize_overview(all_entities, current_group_entities=None, 
                                   labeled_entities=None, **kwargs):
        """优化的概览可视化方法"""
        start_time = time.time()
        
        # 尝试使用快速概览
        if optimizer.optimize_visualize_overview(visualizer, all_entities, 
                                               current_group_entities, labeled_entities, **kwargs):
            print(f"⚡ 快速概览完成: {time.time() - start_time:.3f}秒")
            return
        
        # 回退到原始方法
        print(f"🔄 使用原始概览方法")
        original_visualize_overview(all_entities, current_group_entities, labeled_entities, **kwargs)
    
    # 替换方法
    visualizer.draw_entities = optimized_draw_entities
    visualizer.visualize_overview = optimized_visualize_overview
    
    print("⚡ 可视化器性能补丁已应用")

{"test_time": 1753717210.4160683, "processor_test_result": {"input_entities": 12, "output_entities": 6, "processing_time": 0.008984804153442383, "input_layer_stats": {"A-WALL": {"total": 4, "types": "{'LINE'}"}, "A-DOOR": {"total": 3, "types": "{'LINE'}"}, "A-TEXT": {"total": 3, "types": "{'TEXT'}"}, "EQUIPMENT": {"total": 2, "types": "{'CIRCLE'}"}}, "output_layer_stats": {"A-WALL": {"total": 1, "types": "{'LINE'}", "processing_types": "{'iterative_merge'}"}, "A-DOOR": {"total": 2, "types": "{'LINE'}", "processing_types": "{'simple_deduplication'}"}, "A-TEXT": {"total": 2, "types": "{'TEXT'}", "processing_types": "{'simple_deduplication'}"}, "EQUIPMENT": {"total": 1, "types": "{'CIRCLE'}", "processing_types": "{'simple_deduplication'}"}}, "processing_stats": {"processing_stats": {"total_entities_input": 12, "total_entities_output": 6, "wall_layers_processed": 1, "other_layers_processed": 3, "wall_entities_merged": 3, "other_entities_deduplicated": 3, "processing_time": 0.008984804153442383, "layer_details": {"A-WALL": {"input_count": 4, "output_count": 1, "is_wall_layer": true, "processing_type": "iterative_merge"}, "A-DOOR": {"input_count": 3, "output_count": 2, "is_wall_layer": false, "processing_type": "deduplication"}, "A-TEXT": {"input_count": 3, "output_count": 2, "is_wall_layer": false, "processing_type": "deduplication"}, "EQUIPMENT": {"input_count": 2, "output_count": 1, "is_wall_layer": false, "processing_type": "deduplication"}}}, "wall_layer_patterns": ["wall", "墙", "a-wall", "arch-wall", "a-wall-", "wall-"], "processing_strategy": {"wall_layers": "iterative_merge", "other_layers": "deduplication"}}, "validation": {"is_valid": true, "errors": [], "warnings": [], "statistics": {"original_entities": 12, "processed_entities": 6, "original_layers": 4, "processed_layers": 4, "reduction_rate": 0.5, "layer_preservation_rate": 1.0}}}, "manager_test_result": {"success": true, "entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[30.0, 0.0], [0.0, 10.0]], "start_point": [30.0, 0.0], "end_point": [0.0, 10.0], "merged_from_count": 4, "merged_by": "SelectiveLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753717210.4380667, "color": 1}, {"id": "door_1", "type": "LINE", "layer": "A-DOOR", "start_point": [5, 0], "end_point": [8, 0], "color": 2, "points": [[5, 0], [8, 0]], "original_layer": "A-DOOR", "processed_by": "SelectiveLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753717210.4390552}, {"id": "door_2", "type": "LINE", "layer": "A-DOOR", "start_point": [15, 0], "end_point": [18, 0], "color": 2, "points": [[15, 0], [18, 0]], "original_layer": "A-DOOR", "processed_by": "SelectiveLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753717210.4390552}, {"id": "text_1", "type": "TEXT", "layer": "A-TEXT", "text": "房间1", "position": [10, 5], "height": 3, "color": 3, "original_layer": "A-TEXT", "processed_by": "SelectiveLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753717210.440055}, {"id": "text_2", "type": "TEXT", "layer": "A-TEXT", "text": "房间2", "position": [20, 5], "height": 3, "color": 3, "original_layer": "A-TEXT", "processed_by": "SelectiveLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753717210.440055}, {"id": "equipment_1", "type": "CIRCLE", "layer": "EQUIPMENT", "center": [5, 5], "radius": 2, "color": 4, "original_layer": "EQUIPMENT", "processed_by": "SelectiveLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753717210.440055}], "mode_info": "选择性处理: 墙体迭代合并+其他去重", "processing_stats": {"processing_stats": {"total_entities_input": 12, "total_entities_output": 6, "wall_layers_processed": 1, "other_layers_processed": 3, "wall_entities_merged": 3, "other_entities_deduplicated": 3, "processing_time": 0.00899505615234375, "layer_details": {"A-WALL": {"input_count": 4, "output_count": 1, "is_wall_layer": true, "processing_type": "iterative_merge"}, "A-DOOR": {"input_count": 3, "output_count": 2, "is_wall_layer": false, "processing_type": "deduplication"}, "A-TEXT": {"input_count": 3, "output_count": 2, "is_wall_layer": false, "processing_type": "deduplication"}, "EQUIPMENT": {"input_count": 2, "output_count": 1, "is_wall_layer": false, "processing_type": "deduplication"}}}, "wall_layer_patterns": ["wall", "墙", "a-wall", "arch-wall", "a-wall-", "wall-"], "processing_strategy": {"wall_layers": "iterative_merge", "other_layers": "deduplication"}}, "validation": {"is_valid": true, "errors": [], "warnings": [], "statistics": {"original_entities": 12, "processed_entities": 6, "original_layers": 4, "processed_layers": 4, "reduction_rate": 0.5, "layer_preservation_rate": 1.0}}, "processing_time": 0.0099945068359375, "processing_mode": "selective"}, "summary": {"total_test_time": 0.026984453201293945, "processor_test_success": true, "manager_test_success": true, "all_tests_passed": true, "processor_processing_time": 0.008984804153442383, "manager_processing_time": 0.0099945068359375, "performance_acceptable": true}}
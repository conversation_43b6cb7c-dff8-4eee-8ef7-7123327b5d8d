#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量概览模式颜色修复的脚本
"""

def test_batch_overview_color_fix():
    """测试批量概览模式颜色修复"""
    print("🎨 测试批量概览模式颜色修复...")
    
    # 模拟配色方案
    color_scheme = {
        'wall': '#8B4513',
        'door_window': '#FFD700',
        'furniture': '#4DB6AC',
        'column': '#696969',
        'other': '#4169E1',
        'highlight': '#FF0000'
    }
    
    # 模拟实体数据
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'wall', 'points': [[0, 0], [10, 0]]},
        {'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window', 'points': [[10, 0], [15, 0]]},
        {'type': 'LINE', 'layer': '0', 'label': 'furniture', 'points': [[15, 0], [20, 0]]},
        {'type': 'LINE', 'layer': 'A-WALL', 'label': None, 'points': [[20, 0], [30, 0]]},  # 无标签，根据图层判断
        {'type': 'LINE', 'layer': '0', 'label': None, 'points': [[30, 0], [40, 0]]},       # 无标签，使用默认颜色
    ]
    
    # 模拟批量概览颜色获取方法
    def get_entity_color_for_batch(entity, color_scheme):
        """模拟批量概览颜色获取"""
        try:
            # 优先使用实体标签
            entity_label = entity.get('label')
            if entity_label and entity_label not in ['None', 'none', '']:
                label_color_map = {
                    'wall': color_scheme.get('wall', '#8B4513'),
                    'door': color_scheme.get('door_window', '#FFD700'),
                    'window': color_scheme.get('door_window', '#FFD700'),
                    'door_window': color_scheme.get('door_window', '#FFD700'),
                    'furniture': color_scheme.get('furniture', '#4DB6AC'),
                    'column': color_scheme.get('column', '#696969'),
                }
                if entity_label in label_color_map:
                    return label_color_map[entity_label]
            
            # 根据图层名称推断颜色
            layer_name = str(entity.get('layer', '')).lower()
            if any(keyword in layer_name for keyword in ['wall', '墙', 'a-wall']):
                return color_scheme.get('wall', '#8B4513')
            elif any(keyword in layer_name for keyword in ['door', 'window', '门', '窗', 'a-door', 'a-window']):
                return color_scheme.get('door_window', '#FFD700')
            elif any(keyword in layer_name for keyword in ['furniture', '家具', 'a-furniture']):
                return color_scheme.get('furniture', '#4DB6AC')
            
            # 默认颜色
            return color_scheme.get('other', '#4169E1')
            
        except Exception as e:
            return color_scheme.get('other', '#4169E1')
    
    print("批量概览颜色测试结果:")
    print("-" * 50)
    
    color_distribution = {}
    
    for i, entity in enumerate(test_entities):
        color = get_entity_color_for_batch(entity, color_scheme)
        
        # 统计颜色分布
        if color not in color_distribution:
            color_distribution[color] = 0
        color_distribution[color] += 1
        
        print(f"实体{i+1}: {entity['layer']:<10} | 标签: {str(entity['label']):<12} | 颜色: {color}")
        
        # 检查是否为灰色
        if color in ['#808080', '#C0C0C0', 'lightgray']:
            print(f"  ⚠️ 检测到灰色！可能存在问题")
        else:
            print(f"  ✅ 颜色正常")
    
    print("\n" + "=" * 50)
    print("颜色分布统计:")
    for color, count in color_distribution.items():
        color_name = {
            '#8B4513': '棕色(墙体)',
            '#FFD700': '金色(门窗)',
            '#4DB6AC': '青色(家具)',
            '#696969': '灰色(柱子)',
            '#4169E1': '蓝色(其他)',
            '#FF0000': '红色(高亮)'
        }.get(color, f'未知({color})')
        
        print(f"  {color_name}: {count} 个实体")
    
    # 检查是否有灰色实体
    gray_colors = ['#808080', '#C0C0C0', 'lightgray']
    has_gray = any(color in gray_colors for color in color_distribution.keys())
    
    if has_gray:
        print("\n❌ 检测到灰色实体，批量概览模式可能仍有问题")
        return False
    else:
        print("\n✅ 所有实体都有正确的颜色，批量概览模式修复成功")
        return True

def test_batch_vs_normal_mode():
    """测试批量模式与正常模式的颜色对比"""
    print("\n🔄 测试批量模式与正常模式的颜色对比...")
    
    # 模拟正常模式的颜色获取
    def get_normal_mode_color(entity):
        """模拟正常模式颜色获取"""
        # 这里模拟正常模式会使用更复杂的颜色逻辑
        if entity.get('label') == 'wall':
            return '#8B4513'  # 棕色
        elif entity.get('label') == 'door_window':
            return '#FFD700'  # 金色
        elif 'wall' in str(entity.get('layer', '')).lower():
            return '#8B4513'  # 根据图层判断
        else:
            return '#4169E1'  # 蓝色
    
    # 模拟批量模式的颜色获取（修复后）
    def get_batch_mode_color(entity):
        """模拟批量模式颜色获取（修复后）"""
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'other': '#4169E1'
        }
        
        entity_label = entity.get('label')
        if entity_label and entity_label in color_scheme:
            return color_scheme[entity_label]
        
        layer_name = str(entity.get('layer', '')).lower()
        if 'wall' in layer_name:
            return color_scheme['wall']
        
        return color_scheme['other']
    
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'wall'},
        {'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window'},
        {'type': 'LINE', 'layer': 'A-WALL', 'label': None},
    ]
    
    print("模式对比结果:")
    print("-" * 60)
    print(f"{'实体':<15} {'正常模式':<15} {'批量模式':<15} {'一致性':<10}")
    print("-" * 60)
    
    all_consistent = True
    
    for i, entity in enumerate(test_entities):
        normal_color = get_normal_mode_color(entity)
        batch_color = get_batch_mode_color(entity)
        consistent = normal_color == batch_color
        
        if not consistent:
            all_consistent = False
        
        status = "✅ 一致" if consistent else "❌ 不一致"
        entity_desc = f"{entity['layer']}/{entity.get('label', 'None')}"
        
        print(f"{entity_desc:<15} {normal_color:<15} {batch_color:<15} {status:<10}")
    
    if all_consistent:
        print("\n✅ 批量模式与正常模式颜色完全一致")
        return True
    else:
        print("\n⚠️ 批量模式与正常模式存在颜色差异")
        return False

def main():
    """主测试函数"""
    print("🚀 批量概览模式颜色修复测试")
    print("=" * 60)
    
    tests = [
        ("批量概览颜色修复", test_batch_overview_color_fix),
        ("批量vs正常模式对比", test_batch_vs_normal_mode),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 批量概览模式颜色修复成功！")
        print("\n📝 修复要点:")
        print("1. 移除硬编码颜色 (lightgray, green, red)")
        print("2. 使用配色方案中的颜色")
        print("3. 支持根据实体标签和图层智能选择颜色")
        print("4. 保持与正常模式的颜色一致性")
    else:
        print("⚠️ 部分测试失败，需要进一步调整")

if __name__ == "__main__":
    main()

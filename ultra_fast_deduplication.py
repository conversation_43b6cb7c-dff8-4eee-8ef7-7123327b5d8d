#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超快速去重器
专门解决深拷贝和哈希冲突检测导致的性能问题
"""

import time
import hashlib
from typing import List, Dict, Any, Set


class UltraFastDeduplicator:
    """
    超快速去重器
    
    避免深拷贝和复杂的哈希冲突检测，专注于极致性能
    """
    
    def __init__(self):
        """初始化超快速去重器"""
        self.stats = {
            'total_processed': 0,
            'total_removed': 0,
            'processing_time': 0.0
        }
    
    def deduplicate_entities(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """
        超快速去重实体
        
        Args:
            entities: 输入实体列表
            layer_name: 图层名称
            context: 处理上下文
            
        Returns:
            去重后的实体列表
        """
        if not entities:
            return []
        
        print(f"       ⚡ 开始超快速去重处理 ({context})...")
        start_time = time.time()
        
        # 使用超简单哈希去重，避免深拷贝
        seen_hashes = set()
        deduplicated = []
        
        for entity in entities:
            # 计算超简单哈希
            entity_hash = self._calculate_ultra_fast_hash(entity)
            
            if entity_hash not in seen_hashes:
                seen_hashes.add(entity_hash)
                
                # 浅拷贝并添加最少的标记
                entity_copy = entity.copy()  # 浅拷贝，避免深拷贝的性能开销
                entity_copy['processing_type'] = 'ultra_fast_deduplication'
                entity_copy['processed_by'] = 'UltraFastDeduplicator'
                
                deduplicated.append(entity_copy)
        
        processing_time = time.time() - start_time
        removed_count = len(entities) - len(deduplicated)
        
        # 更新统计
        self.stats['total_processed'] += len(entities)
        self.stats['total_removed'] += removed_count
        self.stats['processing_time'] += processing_time
        
        if removed_count > 0:
            print(f"         🗑️ 超快速去重移除实体: {removed_count} 个")
        print(f"         ⚡ 去重时间: {processing_time:.3f} 秒")
        
        return deduplicated
    
    def _calculate_ultra_fast_hash(self, entity: Dict[str, Any]) -> str:
        """
        计算超快速哈希
        
        只使用最关键的特征，避免复杂计算
        """
        # 只使用最基本的特征
        features = []
        
        # 1. 基本属性
        features.append(entity.get('type', ''))
        features.append(entity.get('layer', ''))
        
        # 2. 简化的坐标处理
        if 'points' in entity:
            points = entity['points']
            if isinstance(points, list) and points:
                # 只使用第一个和最后一个点，并且简化精度
                if len(points) >= 1:
                    p = points[0]
                    if isinstance(p, (list, tuple)) and len(p) >= 2:
                        features.append(f"f:{int(p[0])},{int(p[1])}")
                if len(points) >= 2:
                    p = points[-1]
                    if isinstance(p, (list, tuple)) and len(p) >= 2:
                        features.append(f"l:{int(p[0])},{int(p[1])}")
                features.append(f"c:{len(points)}")
        
        # 3. 其他关键坐标
        if 'start_point' in entity:
            p = entity['start_point']
            if isinstance(p, (list, tuple)) and len(p) >= 2:
                features.append(f"s:{int(p[0])},{int(p[1])}")
        
        if 'end_point' in entity:
            p = entity['end_point']
            if isinstance(p, (list, tuple)) and len(p) >= 2:
                features.append(f"e:{int(p[0])},{int(p[1])}")
        
        # 4. 文字和颜色
        if 'text' in entity:
            features.append(f"t:{entity['text']}")
        
        if 'color' in entity:
            features.append(f"col:{entity['color']}")
        
        # 生成超简单哈希
        feature_string = '|'.join(features)
        
        # 使用更快的哈希方法
        return str(hash(feature_string) % 1000000)  # 使用Python内置hash，取模避免负数
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'stats': self.stats,
            'algorithm': 'ultra_fast_deduplication',
            'complexity': 'O(n)',
            'optimizations': ['shallow_copy', 'simple_hash', 'no_collision_check']
        }


class HybridUltraFastDeduplicator:
    """
    混合超快速去重器
    
    根据数据规模自动选择最优策略
    """
    
    def __init__(self, threshold=30):
        """
        初始化混合超快速去重器
        
        Args:
            threshold: 切换算法的阈值
        """
        self.threshold = threshold
        self.ultra_fast_deduplicator = UltraFastDeduplicator()
    
    def deduplicate_entities(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """
        混合超快速去重实体
        
        根据实体数量自动选择最优算法
        """
        if not entities:
            return []
        
        entity_count = len(entities)
        
        if entity_count < self.threshold:
            print(f"       🎯 使用精确去重 (实体数: {entity_count})")
            return self._precise_deduplicate(entities, layer_name, context)
        else:
            print(f"       ⚡ 使用超快速去重 (实体数: {entity_count})")
            return self.ultra_fast_deduplicator.deduplicate_entities(entities, layer_name, context)
    
    def _precise_deduplicate(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """精确去重（用于小数据集）"""
        print(f"       🎯 开始精确去重处理 ({context})...")
        start_time = time.time()
        
        deduplicated = []
        seen_entities = []
        
        for entity in entities:
            is_duplicate = False
            
            # 精确比较（限制比较数量）
            for seen_entity in seen_entities[-10:]:  # 只与最近10个比较
                if self._is_precise_duplicate(entity, seen_entity):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                entity_copy = entity.copy()  # 浅拷贝
                entity_copy['processing_type'] = 'precise_deduplication'
                entity_copy['processed_by'] = 'HybridUltraFastDeduplicator'
                
                deduplicated.append(entity_copy)
                seen_entities.append(entity)
        
        processing_time = time.time() - start_time
        removed_count = len(entities) - len(deduplicated)
        
        if removed_count > 0:
            print(f"         🗑️ 精确去重移除实体: {removed_count} 个")
        print(f"         ⚡ 去重时间: {processing_time:.3f} 秒")
        
        return deduplicated
    
    def _is_precise_duplicate(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> bool:
        """精确的重复判断（简化版）"""
        # 基本属性检查
        if entity1.get('type') != entity2.get('type'):
            return False
        if entity1.get('layer') != entity2.get('layer'):
            return False
        
        # 简化的坐标比较
        if 'points' in entity1 and 'points' in entity2:
            points1, points2 = entity1['points'], entity2['points']
            if len(points1) != len(points2):
                return False
            # 只比较第一个和最后一个点
            if points1 and points2:
                if not self._points_equal_fast(points1[0], points2[0]):
                    return False
                if len(points1) > 1 and not self._points_equal_fast(points1[-1], points2[-1]):
                    return False
        
        # 其他关键属性
        for attr in ['start_point', 'end_point', 'position', 'text', 'color']:
            if attr in entity1 and attr in entity2:
                if attr in ['start_point', 'end_point', 'position']:
                    if not self._points_equal_fast(entity1[attr], entity2[attr]):
                        return False
                else:
                    if entity1[attr] != entity2[attr]:
                        return False
        
        return True
    
    def _points_equal_fast(self, p1, p2, tolerance=0.01) -> bool:
        """快速比较两个点是否相等"""
        if isinstance(p1, (list, tuple)) and isinstance(p2, (list, tuple)):
            if len(p1) >= 2 and len(p2) >= 2:
                return (abs(p1[0] - p2[0]) <= tolerance and 
                       abs(p1[1] - p2[1]) <= tolerance)
        return p1 == p2
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'algorithm': 'hybrid_ultra_fast_deduplication',
            'threshold': self.threshold,
            'ultra_fast_stats': self.ultra_fast_deduplicator.get_stats()
        }


# 工厂函数
def create_ultra_fast_deduplicator() -> UltraFastDeduplicator:
    """创建超快速去重器"""
    return UltraFastDeduplicator()


def create_hybrid_ultra_fast_deduplicator(threshold=30) -> HybridUltraFastDeduplicator:
    """创建混合超快速去重器"""
    return HybridUltraFastDeduplicator(threshold)

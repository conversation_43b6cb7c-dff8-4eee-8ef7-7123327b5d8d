{"test_time": 1753712840.5065894, "test_entities": [{"id": "wall_1", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]]}, {"id": "wall_2", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]]}, {"id": "wall_3", "type": "LINE", "layer": "A-WALL", "start_point": [100, 100], "end_point": [0, 100], "color": 1, "points": [[100, 100], [0, 100]]}, {"id": "door_1", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]]}, {"id": "door_2", "type": "ARC", "layer": "A-DOOR", "center": [40, 0], "radius": 10, "color": 2, "start_angle": 0, "end_angle": 180}, {"id": "window_1", "type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "points": [[100, 30], [100, 50]]}, {"id": "rail_1", "type": "LINE", "layer": "A-RAIL", "start_point": [0, 100], "end_point": [0, 120], "color": 4, "points": [[0, 100], [0, 120]]}, {"id": "rail_2", "type": "LINE", "layer": "RAILING", "start_point": [0, 120], "end_point": [20, 120], "color": 4, "points": [[0, 120], [20, 120]]}, {"id": "other_1", "type": "LINE", "layer": "FURNITURE", "start_point": [10, 5], "end_point": [30, 5], "color": 7, "points": [[10, 5], [30, 5]]}, {"id": "other_2", "type": "CIRCLE", "layer": "EQUIPMENT", "center": [50, 50], "radius": 15, "color": 8}, {"id": "text_1", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5}, {"id": "dim_1", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6}, {"id": "overlap_1", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]]}], "traditional_result": {"method": "traditional", "input_entities": 13, "output_groups": 6, "processing_time": 0.0009992122650146484, "groups": [{"entities": [{"id": "wall_1", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]]}, {"id": "wall_2", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]]}, {"id": "wall_3", "type": "LINE", "layer": "A-WALL", "start_point": [100, 100], "end_point": [0, 100], "color": 1, "points": [[100, 100], [0, 100]]}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "overlap_1", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]]}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "door_1", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]]}, {"id": "door_2", "type": "ARC", "layer": "A-DOOR", "center": [40, 0], "radius": 10, "color": 2, "start_angle": 0, "end_angle": 180}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "rail_1", "type": "LINE", "layer": "A-RAIL", "start_point": [0, 100], "end_point": [0, 120], "color": 4, "points": [[0, 100], [0, 120]]}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "rail_2", "type": "LINE", "layer": "RAILING", "start_point": [0, 120], "end_point": [20, 120], "color": 4, "points": [[0, 120], [20, 120]]}], "label": "railing_RAILING_0", "group_type": "railing", "layer": "RAILING", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "other_2", "type": "CIRCLE", "layer": "EQUIPMENT", "center": [50, 50], "radius": 15, "color": 8}], "label": "group_5", "group_type": "general", "status": "pending", "confidence": 0.5}], "cross_layer_issues": {"cross_layer_groups": [], "layer_mixing_count": 0, "pure_layer_groups": 6, "problematic_combinations": [], "layer_statistics": {}}}, "enhanced_result": {"method": "enhanced", "input_entities": 13, "output_groups": 10, "processing_time": 0.047972679138183594, "groups": [[{"id": "wall_1", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712840.5365725, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712840.542569, "processing_stage": "processed", "group_index": 0}, {"id": "wall_2", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712840.5365725, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712840.542569, "processing_stage": "processed", "group_index": 0}, {"id": "wall_3", "type": "LINE", "layer": "A-WALL", "start_point": [100, 100], "end_point": [0, 100], "color": 1, "points": [[100, 100], [0, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712840.5365725, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712840.542569, "processing_stage": "processed", "group_index": 0}], [{"id": "door_1", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712840.537572, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712840.5435684, "processing_stage": "processed", "group_index": 0}, {"id": "door_2", "type": "ARC", "layer": "A-DOOR", "center": [40, 0], "radius": 10, "color": 2, "start_angle": 0, "end_angle": 180, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712840.537572, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712840.5435684, "processing_stage": "processed", "group_index": 0}], [{"id": "window_1", "type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "points": [[100, 30], [100, 50]], "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712840.537572, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712840.5445683, "processing_stage": "processed", "group_index": 0}], [{"id": "rail_1", "type": "LINE", "layer": "A-RAIL", "start_point": [0, 100], "end_point": [0, 120], "color": 4, "points": [[0, 100], [0, 120]], "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712840.5385714, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712840.546567, "processing_stage": "processed", "group_index": 0}], [{"id": "rail_2", "type": "LINE", "layer": "RAILING", "start_point": [0, 120], "end_point": [20, 120], "color": 4, "points": [[0, 120], [20, 120]], "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712840.5385714, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712840.5475667, "processing_stage": "processed", "group_index": 0}], [{"id": "other_1", "type": "LINE", "layer": "FURNITURE", "start_point": [10, 5], "end_point": [30, 5], "color": 7, "points": [[10, 5], [30, 5]], "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712840.539571, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712840.5485659, "processing_stage": "processed", "group_index": 0}], [{"id": "other_2", "type": "CIRCLE", "layer": "EQUIPMENT", "center": [50, 50], "radius": 15, "color": 8, "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753712840.539571, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712840.5485659, "processing_stage": "processed", "group_index": 0}], [{"id": "text_1", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712840.54057, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712840.5495648, "processing_stage": "processed", "group_index": 0}], [{"id": "dim_1", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712840.54057, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712840.5505645, "processing_stage": "processed", "group_index": 0}], [{"id": "overlap_1", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753712840.54057, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712840.5515637, "processing_stage": "processed", "group_index": 0}]], "containers": ["LayerDataContainer(name='A-WALL', type='wall', entities=9, groups=1, stage='labeled')", "LayerDataContainer(name='A-DOOR', type='door_window', entities=6, groups=1, stage='labeled')", "LayerDataContainer(name='A-WIND', type='door_window', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-RAIL', type='railing', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='RAILING', type='railing', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='FURNITURE', type='other', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='EQUIPMENT', type='other', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-ANNO-TEXT', type='text', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-DIMS', type='dimension', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='CONSTRUCTION', type='other', entities=3, groups=1, stage='labeled')"], "cross_layer_issues": {"cross_layer_groups": [], "layer_mixing_count": 0, "pure_layer_groups": 10, "problematic_combinations": [], "layer_statistics": {"A-WALL": {"groups": 1, "entities": 3, "mixed_groups": 0}, "A-DOOR": {"groups": 1, "entities": 2, "mixed_groups": 0}, "A-WIND": {"groups": 1, "entities": 1, "mixed_groups": 0}, "A-RAIL": {"groups": 1, "entities": 1, "mixed_groups": 0}, "RAILING": {"groups": 1, "entities": 1, "mixed_groups": 0}, "FURNITURE": {"groups": 1, "entities": 1, "mixed_groups": 0}, "EQUIPMENT": {"groups": 1, "entities": 1, "mixed_groups": 0}, "A-ANNO-TEXT": {"groups": 1, "entities": 1, "mixed_groups": 0}, "A-DIMS": {"groups": 1, "entities": 1, "mixed_groups": 0}, "CONSTRUCTION": {"groups": 1, "entities": 1, "mixed_groups": 0}}}, "integrity_check": {"is_valid": true, "errors": [], "warnings": [], "statistics": {"total_entities": 39, "total_groups": 10, "total_labeled": 13, "group_entity_count": 13, "entities_without_type": 0, "entities_without_layer": 0}}}, "line_merger_result": {"original_lines": 6, "merged_lines": 1, "processing_time": 0.008994817733764648, "merger_stats": {"original_lines": 6, "merged_lines": 5, "final_lines": 1, "processing_time": 0.008994817733764648, "iterations_performed": 2, "iteration_details": [{"iteration": 1, "input_count": 6, "output_count": 1, "merged_count": 5}, {"iteration": 2, "input_count": 1, "output_count": 1, "merged_count": 0}]}}, "comparison": {"cross_layer_issues_comparison": {"traditional": {"cross_layer_groups": 0, "problematic_combinations": 0, "pure_layer_groups": 6}, "enhanced": {"cross_layer_groups": 0, "problematic_combinations": 0, "pure_layer_groups": 10}}, "performance_comparison": {"traditional_time": 0.0009992122650146484, "enhanced_time": 0.047972679138183594, "time_ratio": 48.01049868766404}, "quality_assessment": {"traditional_cross_layer_rate": 0.0, "enhanced_cross_layer_rate": 0.0, "improvement_ratio": 0}}}
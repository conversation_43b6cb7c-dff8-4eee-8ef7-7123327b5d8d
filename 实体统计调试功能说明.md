# CAD分类标注工具 - 实体统计调试功能

## 🎯 功能概述

新增的实体统计调试功能可以在每次实体属性更改后自动显示各类型实体的数量统计，帮助用户实时跟踪标注进度和实体分类情况。

## 🔍 功能特性

### 1. 自动触发统计
- **程序启动时**：显示初始实体统计
- **文件加载后**：显示加载文件的实体统计
- **手动标注后**：显示标注操作后的实体变化
- **重新标注后**：显示重新标注操作后的实体变化
- **实体数据变化时**：显示数据变化后的统计

### 2. 多维度统计
- **总体统计**：所有实体的总数
- **已手动标注**：用户手动标注的实体数量
- **自动标注**：系统自动标注的实体数量
- **未标注**：尚未标注的实体数量

### 3. 按类型分类统计
- **按标注类型**：wall（墙体）、door_window（门窗）、furniture（家具）等
- **按实体类型**：LINE、CIRCLE、ARC等CAD实体类型
- **按状态分类**：pending（待处理）、unlabeled（未标注）等

### 4. 变化检测
- **新增类型检测**：发现新的实体类型时提示
- **数量变化追踪**：显示每种类型实体数量的增减变化
- **变化对比**：与上次统计结果进行对比

## 📊 输出示例

### 程序启动时的统计
```
📊 实体统计调试 - 触发事件: 程序启动
============================================================
📈 总体统计:
  总实体数: 0
  已手动标注: 0
  自动标注: 0
  未标注: 0
============================================================
```

### 文件加载后的统计
```
📊 实体统计调试 - 触发事件: 文件加载完成: example.dxf
============================================================
📈 总体统计:
  总实体数: 1250
  已手动标注: 0
  自动标注: 0
  未标注: 1250

🏷️ 按类型统计 (总计):
  unlabeled: 1250 个

❓ 未标注统计 (按实体类型):
  LINE: 800 个
  CIRCLE: 150 个
  ARC: 200 个
  POLYLINE: 100 个
============================================================
```

### 手动标注后的统计
```
📊 实体统计调试 - 触发事件: 手动标注组为wall
============================================================
📈 总体统计:
  总实体数: 1250
  已手动标注: 45
  自动标注: 0
  未标注: 1205

🏷️ 按类型统计 (总计):
  unlabeled: 1205 个
  wall: 45 个

✅ 手动标注统计:
  wall: 45 个

❓ 未标注统计 (按实体类型):
  LINE: 755 个
  CIRCLE: 150 个
  ARC: 200 个
  POLYLINE: 100 个

🔄 total wall: 0 → 45 (+45)
🔄 labeled wall: 0 → 45 (+45)
🔄 unlabeled LINE: 800 → 755 (-45)
============================================================
```

### 重新标注后的统计
```
📊 实体统计调试 - 触发事件: 重新标注组3为door_window
============================================================
📈 总体统计:
  总实体数: 1250
  已手动标注: 45
  自动标注: 0
  未标注: 1205

🏷️ 按类型统计 (总计):
  unlabeled: 1175 个
  wall: 45 个
  door_window: 30 个

✅ 手动标注统计:
  wall: 45 个
  door_window: 30 个

🔄 total door_window: 0 → 30 (+30)
🔄 labeled door_window: 0 → 30 (+30)
🔄 total unlabeled: 1205 → 1175 (-30)
============================================================
```

## 🔧 技术实现

### 核心方法
- `_debug_entity_stats(trigger_event)`: 主要的统计调试方法
- `_get_total_entity_stats()`: 获取总体实体统计
- `_get_labeled_entity_stats()`: 获取已标注实体统计
- `_get_auto_labeled_entity_stats()`: 获取自动标注实体统计
- `_get_unlabeled_entity_stats()`: 获取未标注实体统计
- `_display_entity_stats()`: 显示统计结果
- `_check_entity_stats_changes()`: 检查统计变化

### 触发点
1. **程序初始化**: `_init_entity_stats_debug()`
2. **手动标注**: `label_current_group()` 方法中
3. **重新标注**: `relabel_group()` 方法中
4. **数据变化**: `_on_entities_changed()` 回调中

### 配置选项
```python
# 启用/禁用实体统计调试
self.entity_stats_debug = True  # True=启用, False=禁用

# 上次统计结果缓存
self.last_entity_stats = {}     # 用于变化检测
```

## 🎯 使用场景

### 1. 标注进度跟踪
- 实时查看已标注和未标注的实体数量
- 了解标注工作的完成进度
- 识别需要重点关注的实体类型

### 2. 质量控制
- 检查标注结果的一致性
- 发现异常的实体分类情况
- 验证自动标注的准确性

### 3. 性能监控
- 监控大文件处理时的实体处理情况
- 跟踪内存使用和处理效率
- 识别处理瓶颈

### 4. 调试辅助
- 快速定位标注问题
- 验证算法改进效果
- 分析实体分布特征

## 🔄 开启/关闭调试

### 开启调试
```python
self.entity_stats_debug = True
```

### 关闭调试
```python
self.entity_stats_debug = False
```

### 手动触发统计
```python
app._debug_entity_stats("手动触发测试")
```

## 📝 注意事项

1. **性能影响**：调试功能会增加一定的计算开销，建议在调试时开启，生产环境可关闭
2. **输出量**：大文件处理时会产生较多调试输出，注意控制台日志管理
3. **数据准确性**：统计基于当前内存中的实体数据，确保数据同步
4. **兼容性**：功能与现有标注流程完全兼容，不影响正常使用

## 🎉 总结

实体统计调试功能为CAD分类标注工具提供了强大的实时监控能力，帮助用户更好地理解和控制标注过程，提高工作效率和标注质量。

**功能状态**：✅ 已完成并测试通过
**版本**：v2.0
**更新时间**：2025-07-29

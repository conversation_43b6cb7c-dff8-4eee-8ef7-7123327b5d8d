#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据传递机制模块
建立可靠的数据传递机制，确保图层类型信息在处理流程中不丢失
"""

import copy
import time
import hashlib
import json
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum

from layer_data_container import LayerDataContainer, LayerType


class TransferStage(Enum):
    """数据传递阶段"""
    FILE_LOADING = "file_loading"           # 文件加载
    LINE_PROCESSING = "line_processing"     # 线条处理
    ENTITY_GROUPING = "entity_grouping"     # 实体分组
    AUTO_LABELING = "auto_labeling"         # 自动标注
    MANUAL_LABELING = "manual_labeling"     # 手动标注
    VISUALIZATION = "visualization"         # 可视化
    EXPORT = "export"                       # 导出


@dataclass
class TransferRecord:
    """数据传递记录"""
    timestamp: float
    source_stage: TransferStage
    target_stage: TransferStage
    container_id: str
    data_hash: str
    entity_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    validation_passed: bool = True
    errors: List[str] = field(default_factory=list)


class DataTransferMechanism:
    """
    数据传递机制
    
    确保图层类型信息在处理流程中不丢失：
    1. 数据完整性验证
    2. 传递过程追踪
    3. 错误检测和恢复
    4. 类型信息保持
    """
    
    def __init__(self):
        """初始化数据传递机制"""
        self.transfer_records: List[TransferRecord] = []
        self.container_registry: Dict[str, LayerDataContainer] = {}
        self.stage_validators: Dict[TransferStage, Callable] = {}
        self.error_handlers: Dict[str, Callable] = {}
        
        # 统计信息
        self.transfer_stats = {
            'total_transfers': 0,
            'successful_transfers': 0,
            'failed_transfers': 0,
            'data_integrity_errors': 0,
            'type_information_losses': 0
        }
        
        # 初始化默认验证器
        self._init_default_validators()
        
        print("🔄 数据传递机制初始化完成")
    
    def _init_default_validators(self):
        """初始化默认验证器"""
        self.stage_validators = {
            TransferStage.FILE_LOADING: self._validate_file_loading,
            TransferStage.LINE_PROCESSING: self._validate_line_processing,
            TransferStage.ENTITY_GROUPING: self._validate_entity_grouping,
            TransferStage.AUTO_LABELING: self._validate_auto_labeling,
            TransferStage.MANUAL_LABELING: self._validate_manual_labeling,
            TransferStage.VISUALIZATION: self._validate_visualization,
            TransferStage.EXPORT: self._validate_export
        }
    
    def transfer_containers(self, containers: List[LayerDataContainer], 
                          source_stage: TransferStage, target_stage: TransferStage,
                          metadata: Dict[str, Any] = None) -> List[LayerDataContainer]:
        """
        安全传递图层容器列表
        
        Args:
            containers: 图层容器列表
            source_stage: 源阶段
            target_stage: 目标阶段
            metadata: 传递元数据
            
        Returns:
            传递后的图层容器列表
        """
        print(f"🔄 开始数据传递: {source_stage.value} -> {target_stage.value}")
        print(f"   传递容器数量: {len(containers)}")
        
        transferred_containers = []
        metadata = metadata or {}
        
        for i, container in enumerate(containers):
            print(f"   传递容器 {i+1}/{len(containers)}: {container.layer_name}")
            
            try:
                transferred_container = self.transfer_single_container(
                    container, source_stage, target_stage, metadata
                )
                transferred_containers.append(transferred_container)
                
            except Exception as e:
                print(f"   ❌ 容器传递失败: {e}")
                # 记录错误并返回原始容器
                self._record_transfer_error(container, source_stage, target_stage, str(e))
                transferred_containers.append(container)
        
        print(f"✅ 数据传递完成: {len(transferred_containers)} 个容器")
        
        return transferred_containers
    
    def transfer_single_container(self, container: LayerDataContainer,
                                source_stage: TransferStage, target_stage: TransferStage,
                                metadata: Dict[str, Any] = None) -> LayerDataContainer:
        """
        安全传递单个图层容器
        
        Args:
            container: 图层容器
            source_stage: 源阶段
            target_stage: 目标阶段
            metadata: 传递元数据
            
        Returns:
            传递后的图层容器
        """
        start_time = time.time()
        metadata = metadata or {}
        
        # 生成容器ID
        container_id = self._generate_container_id(container)
        
        # 创建深拷贝以确保数据安全
        transferred_container = container.create_deep_copy()
        
        # 验证源数据
        source_validation = self._validate_container_at_stage(container, source_stage)
        if not source_validation['is_valid']:
            raise ValueError(f"源数据验证失败: {source_validation['errors']}")
        
        # 添加传递信息
        transfer_info = {
            'transfer_timestamp': start_time,
            'source_stage': source_stage.value,
            'target_stage': target_stage.value,
            'transfer_id': f"{container_id}_{int(start_time)}",
            'metadata': metadata
        }
        
        transferred_container.metadata.update(transfer_info)
        
        # 确保类型信息完整
        self._ensure_type_information_integrity(transferred_container)
        
        # 验证目标数据
        target_validation = self._validate_container_at_stage(transferred_container, target_stage)
        
        # 计算数据哈希
        data_hash = self._calculate_container_hash(transferred_container)
        
        # 记录传递
        record = TransferRecord(
            timestamp=start_time,
            source_stage=source_stage,
            target_stage=target_stage,
            container_id=container_id,
            data_hash=data_hash,
            entity_count=len(transferred_container.get_all_entities()),
            metadata=metadata,
            validation_passed=target_validation['is_valid'],
            errors=target_validation.get('errors', [])
        )
        
        self.transfer_records.append(record)
        
        # 更新注册表
        self.container_registry[container_id] = transferred_container
        
        # 更新统计
        self.transfer_stats['total_transfers'] += 1
        if target_validation['is_valid']:
            self.transfer_stats['successful_transfers'] += 1
        else:
            self.transfer_stats['failed_transfers'] += 1
            if 'type_information' in str(target_validation.get('errors', [])):
                self.transfer_stats['type_information_losses'] += 1
        
        return transferred_container
    
    def _ensure_type_information_integrity(self, container: LayerDataContainer):
        """
        确保类型信息完整性
        
        Args:
            container: 图层容器
        """
        # 确保容器本身的类型信息
        if not container.layer_type:
            print(f"  ⚠️ 容器缺少图层类型信息")
            container.layer_type = LayerType.OTHER
        
        # 确保所有实体都包含类型信息
        all_entities = container.get_all_entities()
        
        for entity in all_entities:
            if isinstance(entity, dict):
                # 确保实体包含容器类型信息
                if 'container_layer_type' not in entity:
                    entity['container_layer_type'] = container.layer_type.value
                
                if 'container_layer_name' not in entity:
                    entity['container_layer_name'] = container.layer_name
                
                # 确保实体包含原始图层信息
                if 'original_layer' not in entity and 'layer' in entity:
                    entity['original_layer'] = entity['layer']
                
                # 添加类型确认标记
                entity['type_information_verified'] = True
                entity['type_verification_timestamp'] = time.time()
    
    def _validate_container_at_stage(self, container: LayerDataContainer, 
                                   stage: TransferStage) -> Dict[str, Any]:
        """
        在特定阶段验证容器
        
        Args:
            container: 图层容器
            stage: 处理阶段
            
        Returns:
            验证结果
        """
        # 基本验证
        validation_result = container.validate_data_integrity()
        
        # 阶段特定验证
        validator = self.stage_validators.get(stage)
        if validator:
            stage_validation = validator(container)
            
            # 合并验证结果
            validation_result['stage_validation'] = stage_validation
            if not stage_validation.get('is_valid', True):
                validation_result['is_valid'] = False
                validation_result['errors'].extend(stage_validation.get('errors', []))
        
        return validation_result
    
    def _validate_file_loading(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证文件加载阶段"""
        errors = []
        
        if not container.original_entities:
            errors.append("缺少原始实体数据")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'stage': 'file_loading'
        }
    
    def _validate_line_processing(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证线条处理阶段"""
        errors = []
        
        # 检查是否有处理后的实体
        if not container.processed_entities and container.original_entities:
            errors.append("线条处理后缺少处理实体")
        
        # 检查类型信息
        for entity in container.get_all_entities():
            if isinstance(entity, dict) and 'container_layer_type' not in entity:
                errors.append("实体缺少容器类型信息")
                break
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'stage': 'line_processing'
        }
    
    def _validate_entity_grouping(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证实体分组阶段"""
        errors = []
        
        if not container.groups and container.get_all_entities():
            errors.append("实体分组后缺少分组数据")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'stage': 'entity_grouping'
        }
    
    def _validate_auto_labeling(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证自动标注阶段"""
        errors = []
        
        # 检查标注实体
        if not container.labeled_entities and container.get_all_entities():
            errors.append("自动标注后缺少标注实体")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'stage': 'auto_labeling'
        }
    
    def _validate_manual_labeling(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证手动标注阶段"""
        return {'is_valid': True, 'errors': [], 'stage': 'manual_labeling'}
    
    def _validate_visualization(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证可视化阶段"""
        return {'is_valid': True, 'errors': [], 'stage': 'visualization'}
    
    def _validate_export(self, container: LayerDataContainer) -> Dict[str, Any]:
        """验证导出阶段"""
        return {'is_valid': True, 'errors': [], 'stage': 'export'}
    
    def _generate_container_id(self, container: LayerDataContainer) -> str:
        """生成容器ID"""
        id_string = f"{container.layer_name}_{container.layer_type.value}_{container.creation_time}"
        return hashlib.md5(id_string.encode()).hexdigest()[:12]
    
    def _calculate_container_hash(self, container: LayerDataContainer) -> str:
        """计算容器数据哈希"""
        try:
            container_dict = container.export_to_dict()
            container_json = json.dumps(container_dict, sort_keys=True, default=str)
            return hashlib.md5(container_json.encode()).hexdigest()
        except Exception:
            return f"hash_error_{time.time()}"
    
    def _record_transfer_error(self, container: LayerDataContainer, 
                             source_stage: TransferStage, target_stage: TransferStage, 
                             error_message: str):
        """记录传递错误"""
        container_id = self._generate_container_id(container)
        
        error_record = TransferRecord(
            timestamp=time.time(),
            source_stage=source_stage,
            target_stage=target_stage,
            container_id=container_id,
            data_hash="error",
            entity_count=len(container.get_all_entities()),
            validation_passed=False,
            errors=[error_message]
        )
        
        self.transfer_records.append(error_record)
        self.transfer_stats['failed_transfers'] += 1
        self.transfer_stats['data_integrity_errors'] += 1
    
    def get_transfer_summary(self) -> Dict[str, Any]:
        """获取传递摘要"""
        return {
            'transfer_stats': self.transfer_stats,
            'total_records': len(self.transfer_records),
            'registered_containers': len(self.container_registry),
            'recent_transfers': [
                {
                    'timestamp': record.timestamp,
                    'source_stage': record.source_stage.value,
                    'target_stage': record.target_stage.value,
                    'container_id': record.container_id,
                    'validation_passed': record.validation_passed,
                    'entity_count': record.entity_count
                }
                for record in self.transfer_records[-10:]  # 最近10条记录
            ]
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接合并分析测试
直接分析CAD数据处理器的合并行为，验证是否存在跨图层合并问题
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from cad_data_processor import CADDataProcessor
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_critical_test_case() -> List[Dict[str, Any]]:
    """
    创建关键测试用例
    设计最容易导致跨图层合并的情况
    """
    return [
        # 墙体线条1 - 主墙体
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]],
            'id': 'wall_main'
        },
        
        # 构造线条 - 与墙体完全重合
        {
            'type': 'LINE',
            'layer': 'CONSTRUCTION',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 9,
            'points': [[0, 0], [100, 0]],
            'id': 'construction_overlay'
        },
        
        # 门线条 - 与墙体部分重合
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [30, 0],
            'end_point': [50, 0],
            'color': 2,
            'points': [[30, 0], [50, 0]],
            'id': 'door_opening'
        },
        
        # 墙体线条2 - 与墙体1端点连接
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [100, 0],
            'end_point': [100, 100],
            'color': 1,
            'points': [[100, 0], [100, 100]],
            'id': 'wall_perpendicular'
        },
        
        # 设备线条 - 与墙体端点连接
        {
            'type': 'LINE',
            'layer': 'EQUIPMENT',
            'start_point': [100, 0],
            'end_point': [120, 0],
            'color': 8,
            'points': [[100, 0], [120, 0]],
            'id': 'equipment_extension'
        },
        
        # 栏杆线条 - 与墙体端点连接
        {
            'type': 'LINE',
            'layer': 'A-RAIL',
            'start_point': [0, 0],
            'end_point': [0, -20],
            'color': 4,
            'points': [[0, 0], [0, -20]],
            'id': 'railing_connection'
        },
        
        # 平行线条 - 距离墙体很近
        {
            'type': 'LINE',
            'layer': 'FURNITURE',
            'start_point': [0, 3],
            'end_point': [100, 3],
            'color': 7,
            'points': [[0, 3], [100, 3]],
            'id': 'furniture_parallel'
        }
    ]


def analyze_group_composition(groups: List[List[Dict[str, Any]]]) -> Dict[str, Any]:
    """
    详细分析分组组成
    
    Args:
        groups: 分组结果
        
    Returns:
        分析结果
    """
    analysis = {
        'total_groups': len(groups),
        'group_details': [],
        'cross_layer_groups': [],
        'layer_statistics': {},
        'entity_distribution': {}
    }
    
    for i, group in enumerate(groups):
        if not group:
            continue
            
        # 分析组内实体
        group_layers = set()
        group_types = set()
        entity_details = []
        
        for entity in group:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                entity_type = entity.get('type', 'UNKNOWN')
                entity_id = entity.get('id', f"entity_{hash(str(entity))}")
                
                group_layers.add(layer)
                group_types.add(entity_type)
                entity_details.append({
                    'id': entity_id,
                    'type': entity_type,
                    'layer': layer,
                    'start_point': entity.get('start_point'),
                    'end_point': entity.get('end_point')
                })
        
        group_info = {
            'group_index': i,
            'entity_count': len(group),
            'layers': list(group_layers),
            'types': list(group_types),
            'entities': entity_details,
            'is_cross_layer': len(group_layers) > 1
        }
        
        analysis['group_details'].append(group_info)
        
        # 检查跨图层组
        if len(group_layers) > 1:
            analysis['cross_layer_groups'].append(group_info)
        
        # 更新图层统计
        for layer in group_layers:
            if layer not in analysis['layer_statistics']:
                analysis['layer_statistics'][layer] = {
                    'total_entities': 0,
                    'groups_count': 0,
                    'mixed_groups': 0
                }
            
            layer_entity_count = sum(1 for e in entity_details if e['layer'] == layer)
            analysis['layer_statistics'][layer]['total_entities'] += layer_entity_count
            analysis['layer_statistics'][layer]['groups_count'] += 1
            
            if len(group_layers) > 1:
                analysis['layer_statistics'][layer]['mixed_groups'] += 1
    
    return analysis


def test_with_different_thresholds():
    """测试不同阈值下的合并行为"""
    print("🧪 测试不同阈值下的合并行为")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_critical_test_case()
    
    print(f"📊 测试实体: {len(test_entities)} 个")
    for entity in test_entities:
        print(f"   {entity['id']} ({entity['type']}) - {entity['layer']}")
        print(f"     坐标: {entity.get('start_point')} -> {entity.get('end_point')}")
    
    # 测试不同阈值
    thresholds = [5, 10, 20, 50, 100]
    results = []
    
    for threshold in thresholds:
        print(f"\n🔄 测试阈值: {threshold}")
        print("-" * 40)
        
        processor = CADDataProcessor()
        
        start_time = time.time()
        groups = processor.group_entities(test_entities, distance_threshold=threshold, debug=True)
        processing_time = time.time() - start_time
        
        # 详细分析结果
        analysis = analyze_group_composition(groups)
        
        print(f"   处理时间: {processing_time:.3f} 秒")
        print(f"   输出分组: {analysis['total_groups']} 个")
        print(f"   跨图层组: {len(analysis['cross_layer_groups'])} 个")
        
        # 显示每个组的详细信息
        for group_info in analysis['group_details']:
            print(f"\n   组 {group_info['group_index'] + 1}: {group_info['entity_count']} 个实体")
            print(f"     图层: {group_info['layers']}")
            
            if group_info['is_cross_layer']:
                print(f"     🚨 跨图层组！包含以下实体:")
                for entity in group_info['entities']:
                    print(f"       - {entity['id']} ({entity['layer']})")
            else:
                print(f"     ✅ 纯图层组")
        
        # 保存结果
        result = {
            'threshold': threshold,
            'processing_time': processing_time,
            'analysis': analysis,
            'raw_groups': groups
        }
        results.append(result)
    
    return results


def test_line_merger_cross_layer():
    """测试线条合并器的跨图层行为"""
    print(f"\n🧪 测试线条合并器跨图层行为")
    print("=" * 60)
    
    try:
        from line_merger import SimpleLineMerger
        
        # 创建测试线条（包含图层信息）
        test_lines_with_layers = [
            {
                'coords': [[0, 0], [100, 0]],
                'layer': 'A-WALL',
                'id': 'wall_main'
            },
            {
                'coords': [[0, 0], [100, 0]],  # 完全重合
                'layer': 'CONSTRUCTION',
                'id': 'construction_overlay'
            },
            {
                'coords': [[30, 0], [50, 0]],  # 部分重合
                'layer': 'A-DOOR',
                'id': 'door_opening'
            },
            {
                'coords': [[100, 0], [100, 100]],  # 端点连接
                'layer': 'A-WALL',
                'id': 'wall_perpendicular'
            },
            {
                'coords': [[100, 0], [120, 0]],  # 端点连接，不同图层
                'layer': 'EQUIPMENT',
                'id': 'equipment_extension'
            }
        ]
        
        print(f"📊 测试线条: {len(test_lines_with_layers)} 条")
        for line_info in test_lines_with_layers:
            print(f"   {line_info['id']} ({line_info['layer']}): {line_info['coords']}")
        
        # 提取坐标进行合并
        line_coords = [line_info['coords'] for line_info in test_lines_with_layers]
        
        merger = SimpleLineMerger(distance_threshold=5, angle_threshold=2)
        
        print(f"\n🔄 执行线条合并...")
        start_time = time.time()
        merged_lines = merger.merge_lines(line_coords)
        processing_time = time.time() - start_time
        
        print(f"\n📈 合并结果:")
        print(f"   处理时间: {processing_time:.3f} 秒")
        print(f"   原始线条: {len(line_coords)}")
        print(f"   合并后线条: {len(merged_lines)}")
        print(f"   合并统计: {merger.stats}")
        
        print(f"\n🔍 合并后的线条:")
        for i, merged_line in enumerate(merged_lines):
            print(f"   合并线条 {i + 1}: {merged_line}")
        
        # 分析：哪些原始线条被合并了
        print(f"\n🔍 合并分析:")
        print(f"   ⚠️ 注意：线条合并器不保留图层信息")
        print(f"   ⚠️ 不同图层的线条可能被合并为一条线")
        
        return {
            'original_lines': len(line_coords),
            'merged_lines': len(merged_lines),
            'processing_time': processing_time,
            'merger_stats': merger.stats,
            'potential_cross_layer_merging': len(line_coords) > len(merged_lines)
        }
        
    except ImportError:
        print("❌ 线条合并器模块不可用")
        return None


def run_direct_merging_analysis():
    """运行直接合并分析"""
    print("🚀 开始直接合并分析测试")
    print("=" * 80)
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'threshold_test_results': None,
        'line_merger_results': None,
        'summary': {}
    }
    
    try:
        # 1. 测试不同阈值下的分组行为
        results['threshold_test_results'] = test_with_different_thresholds()
        
        # 2. 测试线条合并器
        results['line_merger_results'] = test_line_merger_cross_layer()
        
        # 3. 生成总结
        if results['threshold_test_results']:
            cross_layer_counts = [
                len(r['analysis']['cross_layer_groups']) 
                for r in results['threshold_test_results']
            ]
            
            results['summary'] = {
                'thresholds_tested': len(results['threshold_test_results']),
                'max_cross_layer_groups': max(cross_layer_counts) if cross_layer_counts else 0,
                'min_cross_layer_groups': min(cross_layer_counts) if cross_layer_counts else 0,
                'cross_layer_issue_detected': any(count > 0 for count in cross_layer_counts),
                'line_merger_cross_layer_potential': results['line_merger_results']['potential_cross_layer_merging'] if results['line_merger_results'] else False
            }
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 直接合并分析完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        
        if results['summary']:
            print(f"   测试阈值数: {results['summary']['thresholds_tested']}")
            print(f"   检测到跨图层问题: {'是' if results['summary']['cross_layer_issue_detected'] else '否'}")
            print(f"   线条合并器潜在问题: {'是' if results['summary']['line_merger_cross_layer_potential'] else '否'}")
        
        # 保存结果
        with open('direct_merging_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: direct_merging_analysis_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_direct_merging_analysis()
    sys.exit(0 if success else 1)

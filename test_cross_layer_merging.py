#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试跨图层合并问题
验证在实体合并和分组过程中，不同图层的数据是否会被错误合并
"""

import os
import sys
import time
import json
import copy
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from cad_data_processor import CADDataProcessor
    from line_merger import SimpleLineMerger
    from layer_data_container import LayerDataContainer, LayerType
    from integrated_processing_manager import IntegratedProcessingManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_cross_layer_test_data() -> List[Dict[str, Any]]:
    """
    创建跨图层测试数据
    包含可能被错误合并的墙体、门窗、其他图层实体
    """
    test_entities = [
        # 墙体图层 - 主要墙体线条
        {
            'id': 'wall_1',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]]
        },
        {
            'id': 'wall_2',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [100, 0],
            'end_point': [100, 100],
            'color': 1,
            'points': [[100, 0], [100, 100]]
        },
        {
            'id': 'wall_3',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [100, 100],
            'end_point': [0, 100],
            'color': 1,
            'points': [[100, 100], [0, 100]]
        },
        
        # 门窗图层 - 与墙体端点重合的线条
        {
            'id': 'door_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [30, 0],  # 与wall_1重合
            'end_point': [50, 0],    # 与wall_1重合
            'color': 2,
            'points': [[30, 0], [50, 0]]
        },
        {
            'id': 'door_2',
            'type': 'ARC',
            'layer': 'A-DOOR',
            'center': [40, 0],
            'radius': 10,
            'color': 2,
            'start_angle': 0,
            'end_angle': 180
        },
        {
            'id': 'window_1',
            'type': 'LINE',
            'layer': 'A-WIND',
            'start_point': [100, 30],  # 与wall_2重合
            'end_point': [100, 50],    # 与wall_2重合
            'color': 3,
            'points': [[100, 30], [100, 50]]
        },
        
        # 栏杆图层 - 与墙体端点接近的线条
        {
            'id': 'rail_1',
            'type': 'LINE',
            'layer': 'A-RAIL',
            'start_point': [0, 100],   # 与wall_3端点重合
            'end_point': [0, 120],     # 延伸出去
            'color': 4,
            'points': [[0, 100], [0, 120]]
        },
        {
            'id': 'rail_2',
            'type': 'LINE',
            'layer': 'RAILING',
            'start_point': [0, 120],
            'end_point': [20, 120],
            'color': 4,
            'points': [[0, 120], [20, 120]]
        },
        
        # 其他图层 - 与墙体距离很近的线条
        {
            'id': 'other_1',
            'type': 'LINE',
            'layer': 'FURNITURE',
            'start_point': [10, 5],    # 距离墙体很近
            'end_point': [30, 5],
            'color': 7,
            'points': [[10, 5], [30, 5]]
        },
        {
            'id': 'other_2',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [50, 50],
            'radius': 15,
            'color': 8
        },
        
        # 文字图层 - 不应该被合并
        {
            'id': 'text_1',
            'type': 'TEXT',
            'layer': 'A-ANNO-TEXT',
            'text': '房间1',
            'position': [50, 50],
            'height': 5,
            'color': 5
        },
        
        # 标注图层 - 不应该被合并
        {
            'id': 'dim_1',
            'type': 'DIMENSION',
            'layer': 'A-DIMS',
            'def_points': [[0, 0], [100, 0], [50, -20]],
            'text': '100',
            'color': 6
        },
        
        # 边界情况：与墙体完全重合的其他图层线条
        {
            'id': 'overlap_1',
            'type': 'LINE',
            'layer': 'CONSTRUCTION',
            'start_point': [0, 0],     # 与wall_1完全重合
            'end_point': [100, 0],
            'color': 9,
            'points': [[0, 0], [100, 0]]
        }
    ]
    
    return test_entities


def test_traditional_processing():
    """测试传统处理方式的跨图层合并问题"""
    print("\n🧪 测试传统处理方式")
    print("=" * 60)
    
    test_entities = create_cross_layer_test_data()
    
    # 使用传统的CAD数据处理器
    processor = CADDataProcessor()
    
    print(f"📊 输入数据: {len(test_entities)} 个实体")
    print("   图层分布:")
    layer_counts = {}
    for entity in test_entities:
        layer = entity['layer']
        layer_counts[layer] = layer_counts.get(layer, 0) + 1
    
    for layer, count in layer_counts.items():
        print(f"     {layer}: {count} 个实体")
    
    # 执行分组处理
    print("\n🔄 执行传统分组处理...")
    start_time = time.time()
    
    groups = processor.group_entities(test_entities, distance_threshold=20, debug=True)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输出分组: {len(groups)} 个")
    
    # 分析跨图层合并情况
    cross_layer_issues = analyze_cross_layer_merging(groups, "传统处理")
    
    return {
        'method': 'traditional',
        'input_entities': len(test_entities),
        'output_groups': len(groups),
        'processing_time': processing_time,
        'groups': groups,
        'cross_layer_issues': cross_layer_issues
    }


def test_enhanced_processing():
    """测试增强处理方式的跨图层合并问题"""
    print("\n🧪 测试增强处理方式")
    print("=" * 60)
    
    test_entities = create_cross_layer_test_data()
    
    if not MODULES_AVAILABLE:
        print("❌ 增强处理模块不可用")
        return None
    
    # 使用增强的集成处理管理器
    manager = IntegratedProcessingManager()
    
    print(f"📊 输入数据: {len(test_entities)} 个实体")
    
    # 执行增强处理
    print("\n🔄 执行增强分组处理...")
    start_time = time.time()
    
    result = manager.process_file_entities(test_entities, "cross_layer_test.dxf")
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输出分组: {result['total_groups']} 个")
    print(f"   图层容器: {result['total_layers']} 个")
    
    # 分析跨图层合并情况
    cross_layer_issues = analyze_cross_layer_merging(result['groups'], "增强处理")
    
    return {
        'method': 'enhanced',
        'input_entities': len(test_entities),
        'output_groups': result['total_groups'],
        'processing_time': processing_time,
        'groups': result['groups'],
        'containers': result['containers'],
        'cross_layer_issues': cross_layer_issues,
        'integrity_check': result['integrity_check']
    }


def analyze_cross_layer_merging(groups: List[List[Dict[str, Any]]], method_name: str) -> Dict[str, Any]:
    """
    分析跨图层合并问题
    
    Args:
        groups: 分组结果
        method_name: 处理方法名称
        
    Returns:
        分析结果字典
    """
    print(f"\n🔍 分析 {method_name} 的跨图层合并情况:")
    
    issues = {
        'cross_layer_groups': [],
        'layer_mixing_count': 0,
        'pure_layer_groups': 0,
        'problematic_combinations': [],
        'layer_statistics': {}
    }
    
    # 定义特殊图层
    wall_layers = {'A-WALL', 'WALL-INNER'}
    door_window_layers = {'A-DOOR', 'A-WIND'}
    railing_layers = {'A-RAIL', 'RAILING'}
    text_layers = {'A-ANNO-TEXT', 'TEXT-LAYER'}
    dimension_layers = {'A-DIMS', 'DIM-LAYER'}
    
    for i, group in enumerate(groups):
        if not group:
            continue
            
        # 统计组内图层分布
        layer_counts = {}
        entity_details = []
        
        for entity in group:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                entity_id = entity.get('id', f"entity_{hash(str(entity))}")
                entity_type = entity.get('type', 'UNKNOWN')
                
                layer_counts[layer] = layer_counts.get(layer, 0) + 1
                entity_details.append({
                    'id': entity_id,
                    'type': entity_type,
                    'layer': layer
                })
        
        # 检查是否为跨图层组
        unique_layers = set(layer_counts.keys())
        
        if len(unique_layers) > 1:
            # 发现跨图层组
            issues['cross_layer_groups'].append({
                'group_index': i,
                'layers': list(unique_layers),
                'layer_counts': layer_counts,
                'entity_details': entity_details,
                'total_entities': len(group)
            })
            issues['layer_mixing_count'] += 1
            
            # 检查是否为问题性组合
            problematic = False
            problem_type = []
            
            # 墙体与其他图层混合
            if unique_layers & wall_layers and unique_layers - wall_layers:
                problematic = True
                problem_type.append("墙体与其他图层混合")
            
            # 门窗与非相关图层混合
            if unique_layers & door_window_layers and unique_layers - door_window_layers - wall_layers:
                problematic = True
                problem_type.append("门窗与非相关图层混合")
            
            # 文字/标注与其他图层混合
            if (unique_layers & text_layers and unique_layers - text_layers) or \
               (unique_layers & dimension_layers and unique_layers - dimension_layers):
                problematic = True
                problem_type.append("文字/标注与其他图层混合")
            
            if problematic:
                issues['problematic_combinations'].append({
                    'group_index': i,
                    'layers': list(unique_layers),
                    'problem_types': problem_type,
                    'entity_count': len(group)
                })
        else:
            issues['pure_layer_groups'] += 1
        
        # 更新图层统计
        for layer in unique_layers:
            if layer not in issues['layer_statistics']:
                issues['layer_statistics'][layer] = {
                    'groups': 0,
                    'entities': 0,
                    'mixed_groups': 0
                }
            
            issues['layer_statistics'][layer]['groups'] += 1
            issues['layer_statistics'][layer]['entities'] += layer_counts[layer]
            
            if len(unique_layers) > 1:
                issues['layer_statistics'][layer]['mixed_groups'] += 1
    
    # 打印分析结果
    print(f"   总分组数: {len(groups)}")
    print(f"   纯图层组: {issues['pure_layer_groups']}")
    print(f"   跨图层组: {issues['layer_mixing_count']}")
    print(f"   问题性组合: {len(issues['problematic_combinations'])}")
    
    if issues['cross_layer_groups']:
        print(f"\n   🚨 发现跨图层合并问题:")
        for cross_group in issues['cross_layer_groups']:
            print(f"     组 {cross_group['group_index'] + 1}: {cross_group['layers']} "
                  f"({cross_group['total_entities']} 个实体)")
    
    if issues['problematic_combinations']:
        print(f"\n   ⚠️ 问题性组合:")
        for prob in issues['problematic_combinations']:
            print(f"     组 {prob['group_index'] + 1}: {prob['problem_types']}")
    
    return issues


def test_line_merger_cross_layer():
    """测试线条合并器的跨图层问题"""
    print("\n🧪 测试线条合并器跨图层问题")
    print("=" * 60)
    
    # 创建可能被错误合并的线条
    test_lines = [
        # 墙体线条
        {'id': 'wall_line_1', 'layer': 'A-WALL', 'points': [[0, 0], [50, 0]]},
        {'id': 'wall_line_2', 'layer': 'A-WALL', 'points': [[50, 0], [100, 0]]},
        
        # 门窗线条（与墙体端点重合）
        {'id': 'door_line_1', 'layer': 'A-DOOR', 'points': [[30, 0], [40, 0]]},
        {'id': 'door_line_2', 'layer': 'A-DOOR', 'points': [[40, 0], [50, 0]]},
        
        # 其他图层线条（与墙体平行且接近）
        {'id': 'other_line_1', 'layer': 'CONSTRUCTION', 'points': [[0, 2], [50, 2]]},
        {'id': 'other_line_2', 'layer': 'CONSTRUCTION', 'points': [[50, 2], [100, 2]]},
    ]
    
    print(f"📊 输入线条: {len(test_lines)} 条")
    for line in test_lines:
        print(f"   {line['id']} ({line['layer']}): {line['points']}")
    
    # 测试简单线条合并器
    merger = SimpleLineMerger(distance_threshold=5, angle_threshold=2)
    
    print(f"\n🔄 执行线条合并...")
    start_time = time.time()
    
    # 转换为合并器需要的格式
    line_coords = [line['points'] for line in test_lines]
    merged_lines = merger.merge_lines(line_coords)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 合并结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   原始线条: {len(test_lines)}")
    print(f"   合并后线条: {len(merged_lines)}")
    print(f"   合并统计: {merger.stats}")
    
    # 分析合并结果
    print(f"\n🔍 合并后的线条:")
    for i, merged_line in enumerate(merged_lines):
        print(f"   合并线条 {i + 1}: {merged_line}")
    
    return {
        'original_lines': len(test_lines),
        'merged_lines': len(merged_lines),
        'processing_time': processing_time,
        'merger_stats': merger.stats
    }


def run_comprehensive_cross_layer_test():
    """运行综合跨图层合并测试"""
    print("🚀 开始综合跨图层合并测试")
    print("=" * 80)
    
    start_time = time.time()
    
    # 测试结果收集
    results = {
        'test_time': time.time(),
        'test_entities': create_cross_layer_test_data(),
        'traditional_result': None,
        'enhanced_result': None,
        'line_merger_result': None,
        'comparison': None
    }
    
    try:
        # 1. 测试传统处理方式
        results['traditional_result'] = test_traditional_processing()
        
        # 2. 测试增强处理方式
        if MODULES_AVAILABLE:
            results['enhanced_result'] = test_enhanced_processing()
        
        # 3. 测试线条合并器
        results['line_merger_result'] = test_line_merger_cross_layer()
        
        # 4. 对比分析
        results['comparison'] = compare_processing_methods(
            results['traditional_result'],
            results['enhanced_result']
        )
        
        # 总结测试结果
        total_time = time.time() - start_time
        
        print(f"\n🎉 综合测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   测试实体: {len(results['test_entities'])} 个")
        
        # 保存测试结果
        with open('cross_layer_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: cross_layer_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def compare_processing_methods(traditional_result, enhanced_result):
    """对比两种处理方法的结果"""
    if not traditional_result or not enhanced_result:
        return None
    
    print(f"\n📊 处理方法对比")
    print("=" * 60)
    
    comparison = {
        'cross_layer_issues_comparison': {},
        'performance_comparison': {},
        'quality_assessment': {}
    }
    
    # 跨图层问题对比
    trad_issues = traditional_result['cross_layer_issues']
    enh_issues = enhanced_result['cross_layer_issues']
    
    comparison['cross_layer_issues_comparison'] = {
        'traditional': {
            'cross_layer_groups': len(trad_issues['cross_layer_groups']),
            'problematic_combinations': len(trad_issues['problematic_combinations']),
            'pure_layer_groups': trad_issues['pure_layer_groups']
        },
        'enhanced': {
            'cross_layer_groups': len(enh_issues['cross_layer_groups']),
            'problematic_combinations': len(enh_issues['problematic_combinations']),
            'pure_layer_groups': enh_issues['pure_layer_groups']
        }
    }
    
    # 性能对比
    comparison['performance_comparison'] = {
        'traditional_time': traditional_result['processing_time'],
        'enhanced_time': enhanced_result['processing_time'],
        'time_ratio': enhanced_result['processing_time'] / traditional_result['processing_time']
    }
    
    # 质量评估
    trad_cross_rate = len(trad_issues['cross_layer_groups']) / traditional_result['output_groups']
    enh_cross_rate = len(enh_issues['cross_layer_groups']) / enhanced_result['output_groups']
    
    comparison['quality_assessment'] = {
        'traditional_cross_layer_rate': trad_cross_rate,
        'enhanced_cross_layer_rate': enh_cross_rate,
        'improvement_ratio': (trad_cross_rate - enh_cross_rate) / trad_cross_rate if trad_cross_rate > 0 else 0
    }
    
    # 打印对比结果
    print(f"跨图层问题对比:")
    print(f"   传统方法: {len(trad_issues['cross_layer_groups'])} 个跨图层组")
    print(f"   增强方法: {len(enh_issues['cross_layer_groups'])} 个跨图层组")
    print(f"   改进效果: {comparison['quality_assessment']['improvement_ratio']:.1%}")
    
    print(f"\n性能对比:")
    print(f"   传统方法: {traditional_result['processing_time']:.3f} 秒")
    print(f"   增强方法: {enhanced_result['processing_time']:.3f} 秒")
    print(f"   时间比率: {comparison['performance_comparison']['time_ratio']:.2f}x")
    
    return comparison


if __name__ == "__main__":
    success = run_comprehensive_cross_layer_test()
    sys.exit(0 if success else 1)

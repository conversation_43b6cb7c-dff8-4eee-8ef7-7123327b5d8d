#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的可视化更新策略
解决可视化更新导致的性能问题
"""

import time
from typing import List, Dict, Any, Optional


class OptimizedVisualizationManager:
    """
    优化的可视化管理器
    
    通过缓存、批量更新和智能跳过来优化可视化性能
    """
    
    def __init__(self, visualizer=None):
        """
        初始化优化可视化管理器
        
        Args:
            visualizer: 原始可视化器
        """
        self.visualizer = visualizer
        self.last_update_time = 0
        self.update_throttle = 0.1  # 最小更新间隔（秒）
        
        # 缓存
        self.cached_entities = None
        self.cached_entities_hash = None
        self.cached_bounds = None
        
        # 性能统计
        self.stats = {
            'total_updates': 0,
            'skipped_updates': 0,
            'cache_hits': 0,
            'total_time': 0.0
        }
    
    def optimized_draw_entities(self, entities: List[Dict[str, Any]], force_update: bool = False) -> bool:
        """
        优化的实体绘制
        
        Args:
            entities: 实体列表
            force_update: 是否强制更新
            
        Returns:
            是否执行了更新
        """
        if not entities:
            return False
        
        start_time = time.time()
        self.stats['total_updates'] += 1
        
        # 检查是否需要更新
        if not force_update and not self._should_update_entities(entities):
            self.stats['skipped_updates'] += 1
            return False
        
        # 检查更新频率限制
        current_time = time.time()
        if not force_update and (current_time - self.last_update_time) < self.update_throttle:
            self.stats['skipped_updates'] += 1
            return False
        
        try:
            # 使用简化的绘制方法
            if self.visualizer:
                self._fast_draw_entities(entities)
                self.last_update_time = current_time
                
                # 更新缓存
                self.cached_entities = entities
                self.cached_entities_hash = self._calculate_entities_hash(entities)
                
                self.stats['total_time'] += time.time() - start_time
                return True
        
        except Exception as e:
            print(f"❌ 优化绘制失败: {e}")
            return False
        
        return False
    
    def optimized_visualize_overview(self, all_entities: List[Dict[str, Any]], 
                                   current_group: Optional[List[Dict[str, Any]]] = None,
                                   labeled_entities: Optional[List[Dict[str, Any]]] = None,
                                   force_update: bool = False) -> bool:
        """
        优化的概览可视化
        
        Args:
            all_entities: 所有实体
            current_group: 当前组实体
            labeled_entities: 已标注实体
            force_update: 是否强制更新
            
        Returns:
            是否执行了更新
        """
        if not all_entities:
            return False
        
        start_time = time.time()
        self.stats['total_updates'] += 1
        
        # 检查更新频率限制
        current_time = time.time()
        if not force_update and (current_time - self.last_update_time) < self.update_throttle:
            self.stats['skipped_updates'] += 1
            return False
        
        try:
            # 使用简化的概览绘制
            if self.visualizer:
                self._fast_visualize_overview(all_entities, current_group, labeled_entities)
                self.last_update_time = current_time
                
                self.stats['total_time'] += time.time() - start_time
                return True
        
        except Exception as e:
            print(f"❌ 优化概览可视化失败: {e}")
            return False
        
        return False
    
    def _should_update_entities(self, entities: List[Dict[str, Any]]) -> bool:
        """检查是否需要更新实体显示"""
        if not self.cached_entities:
            return True
        
        # 检查实体数量
        if len(entities) != len(self.cached_entities):
            return True
        
        # 检查实体哈希
        current_hash = self._calculate_entities_hash(entities)
        if current_hash != self.cached_entities_hash:
            return True
        
        self.stats['cache_hits'] += 1
        return False
    
    def _calculate_entities_hash(self, entities: List[Dict[str, Any]]) -> str:
        """计算实体列表的简单哈希"""
        if not entities:
            return ""
        
        # 使用实体数量和前几个实体的ID作为简单哈希
        hash_parts = [str(len(entities))]
        
        for i, entity in enumerate(entities[:5]):  # 只检查前5个实体
            if isinstance(entity, dict) and 'id' in entity:
                hash_parts.append(str(entity['id']))
        
        return "|".join(hash_parts)
    
    def _fast_draw_entities(self, entities: List[Dict[str, Any]]):
        """快速绘制实体（简化版）"""
        if not self.visualizer or not hasattr(self.visualizer, 'ax_detail'):
            return
        
        print(f"⚡ 快速绘制 {len(entities)} 个实体")
        
        # 清除并设置基本属性
        ax = self.visualizer.ax_detail
        ax.clear()
        ax.set_aspect('equal')
        
        # 批量绘制（简化版本）
        lines_x = []
        lines_y = []
        
        for entity in entities:
            try:
                coords = self._get_entity_coordinates_fast(entity)
                if coords and len(coords) >= 2:
                    x_coords = [coord[0] for coord in coords]
                    y_coords = [coord[1] for coord in coords]
                    lines_x.extend(x_coords + [None])  # None用于分隔线段
                    lines_y.extend(y_coords + [None])
            except:
                continue
        
        # 一次性绘制所有线条
        if lines_x and lines_y:
            ax.plot(lines_x, lines_y, 'b-', linewidth=0.5, alpha=0.7)
        
        # 设置坐标范围（使用缓存）
        if self.cached_bounds:
            min_x, min_y, max_x, max_y = self.cached_bounds
        else:
            min_x, min_y, max_x, max_y = self._calculate_bounds_fast(entities)
            self.cached_bounds = (min_x, min_y, max_x, max_y)
        
        if min_x != float('inf'):
            margin = max((max_x - min_x) * 0.05, (max_y - min_y) * 0.05, 5)
            ax.set_xlim(min_x - margin, max_x + margin)
            ax.set_ylim(min_y - margin, max_y + margin)
        
        ax.set_title('实体详细视图（快速模式）', fontsize=10)
    
    def _fast_visualize_overview(self, all_entities: List[Dict[str, Any]], 
                               current_group: Optional[List[Dict[str, Any]]] = None,
                               labeled_entities: Optional[List[Dict[str, Any]]] = None):
        """快速概览可视化（简化版）"""
        if not self.visualizer or not hasattr(self.visualizer, 'ax_overview'):
            return
        
        print(f"⚡ 快速概览可视化 {len(all_entities)} 个实体")
        
        ax = self.visualizer.ax_overview
        ax.clear()
        ax.set_aspect('equal')
        
        # 简化绘制：只绘制轮廓
        all_x = []
        all_y = []
        current_x = []
        current_y = []
        
        # 收集所有实体坐标
        for entity in all_entities:
            try:
                coords = self._get_entity_coordinates_fast(entity)
                if coords and len(coords) >= 2:
                    x_coords = [coord[0] for coord in coords]
                    y_coords = [coord[1] for coord in coords]
                    all_x.extend(x_coords + [None])
                    all_y.extend(y_coords + [None])
            except:
                continue
        
        # 收集当前组坐标
        if current_group:
            for entity in current_group:
                try:
                    coords = self._get_entity_coordinates_fast(entity)
                    if coords and len(coords) >= 2:
                        x_coords = [coord[0] for coord in coords]
                        y_coords = [coord[1] for coord in coords]
                        current_x.extend(x_coords + [None])
                        current_y.extend(y_coords + [None])
                except:
                    continue
        
        # 绘制所有实体（灰色）
        if all_x and all_y:
            ax.plot(all_x, all_y, color='lightgray', linewidth=0.3, alpha=0.5)
        
        # 绘制当前组（红色高亮）
        if current_x and current_y:
            ax.plot(current_x, current_y, color='red', linewidth=1.0, alpha=0.8)
        
        # 设置坐标范围
        if self.cached_bounds:
            min_x, min_y, max_x, max_y = self.cached_bounds
        else:
            min_x, min_y, max_x, max_y = self._calculate_bounds_fast(all_entities)
            self.cached_bounds = (min_x, min_y, max_x, max_y)
        
        if min_x != float('inf'):
            margin = max((max_x - min_x) * 0.05, (max_y - min_y) * 0.05, 10)
            ax.set_xlim(min_x - margin, max_x + margin)
            ax.set_ylim(min_y - margin, max_y + margin)
        
        ax.set_title('全图概览（快速模式）', fontsize=10)
    
    def _get_entity_coordinates_fast(self, entity: Dict[str, Any]) -> List[List[float]]:
        """快速获取实体坐标"""
        if not isinstance(entity, dict):
            return []
        
        # 优先使用points
        if 'points' in entity and entity['points']:
            return entity['points']
        
        # 使用start_point和end_point
        if 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        
        # 使用position（文字等）
        if 'position' in entity:
            pos = entity['position']
            return [pos, pos]  # 点实体
        
        return []
    
    def _calculate_bounds_fast(self, entities: List[Dict[str, Any]]) -> tuple:
        """快速计算边界"""
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for entity in entities:
            try:
                coords = self._get_entity_coordinates_fast(entity)
                for coord in coords:
                    if len(coord) >= 2:
                        x, y = coord[0], coord[1]
                        min_x = min(min_x, x)
                        max_x = max(max_x, x)
                        min_y = min(min_y, y)
                        max_y = max(max_y, y)
            except:
                continue
        
        return min_x, min_y, max_x, max_y
    
    def force_update(self):
        """强制下次更新"""
        self.last_update_time = 0
        self.cached_entities = None
        self.cached_entities_hash = None
        self.cached_bounds = None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total_updates = self.stats['total_updates']
        if total_updates > 0:
            skip_rate = self.stats['skipped_updates'] / total_updates
            cache_hit_rate = self.stats['cache_hits'] / total_updates
            avg_time = self.stats['total_time'] / (total_updates - self.stats['skipped_updates']) if total_updates > self.stats['skipped_updates'] else 0
        else:
            skip_rate = cache_hit_rate = avg_time = 0
        
        return {
            'total_updates': total_updates,
            'skipped_updates': self.stats['skipped_updates'],
            'cache_hits': self.stats['cache_hits'],
            'skip_rate': skip_rate,
            'cache_hit_rate': cache_hit_rate,
            'average_update_time': avg_time,
            'total_time': self.stats['total_time']
        }


# 工厂函数
def create_optimized_visualization_manager(visualizer=None) -> OptimizedVisualizationManager:
    """创建优化可视化管理器"""
    return OptimizedVisualizationManager(visualizer)

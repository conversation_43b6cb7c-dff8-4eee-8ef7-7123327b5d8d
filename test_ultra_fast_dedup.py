#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试超快速去重器
验证是否解决了深拷贝和哈希冲突检测的性能问题
"""

import os
import sys
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ultra_fast_deduplication import HybridUltraFastDeduplicator, UltraFastDeduplicator
    from independent_layer_processor import IndependentLayerProcessor
    print("✅ 超快速去重模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)


def create_test_data_with_duplicates(count=154):
    """创建包含重复的测试数据"""
    entities = []
    
    for i in range(count):
        if i % 10 == 0 and i > 0:
            # 创建重复实体
            entities.append({
                'id': f'entity_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [0, 0],
                'end_point': [10, 0],
                'color': 2,
                'points': [[0, 0], [10, 0]]
            })
        else:
            # 创建正常实体
            entities.append({
                'id': f'entity_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 8, 10],
                'end_point': [i * 8 + 4, 10],
                'color': 2,
                'points': [[i * 8, 10], [i * 8 + 4, 10]]
            })
    
    return entities


def test_ultra_fast_deduplicator():
    """测试超快速去重器"""
    print("🧪 测试超快速去重器")
    print("=" * 50)
    
    # 测试不同规模的数据
    test_sizes = [50, 100, 154, 200]
    
    for size in test_sizes:
        print(f"\n📊 测试数据规模: {size} 个实体")
        print("-" * 30)
        
        test_entities = create_test_data_with_duplicates(size)
        expected_duplicates = size // 10
        
        print(f"   输入实体: {len(test_entities)} 个")
        print(f"   预期重复: {expected_duplicates} 个")
        
        # 测试超快速去重器
        deduplicator = UltraFastDeduplicator()
        
        start_time = time.time()
        result = deduplicator.deduplicate_entities(test_entities, 'A-WINDOW', 'test')
        processing_time = time.time() - start_time
        
        removed_count = len(test_entities) - len(result)
        
        print(f"   处理时间: {processing_time:.3f} 秒")
        print(f"   移除实体: {removed_count} 个")
        print(f"   输出实体: {len(result)} 个")
        
        # 性能评估
        if processing_time < 0.01:
            print("   ✅ 性能优秀")
        elif processing_time < 0.1:
            print("   ✅ 性能良好")
        elif processing_time < 1.0:
            print("   ⚠️ 性能一般")
        else:
            print("   ❌ 性能较差")


def test_hybrid_ultra_fast_deduplicator():
    """测试混合超快速去重器"""
    print("\n🧪 测试混合超快速去重器")
    print("=" * 50)
    
    # 测试小数据集（精确去重）
    print("📊 测试小数据集 (20个实体)")
    small_entities = create_test_data_with_duplicates(20)
    
    deduplicator = HybridUltraFastDeduplicator(threshold=30)
    
    start_time = time.time()
    small_result = deduplicator.deduplicate_entities(small_entities, 'TEST', 'small_test')
    small_time = time.time() - start_time
    
    print(f"   处理时间: {small_time:.3f} 秒")
    print(f"   输入: {len(small_entities)} -> 输出: {len(small_result)}")
    
    # 测试大数据集（超快速去重）
    print("\n📊 测试大数据集 (154个实体)")
    large_entities = create_test_data_with_duplicates(154)
    
    start_time = time.time()
    large_result = deduplicator.deduplicate_entities(large_entities, 'A-WINDOW', 'large_test')
    large_time = time.time() - start_time
    
    print(f"   处理时间: {large_time:.3f} 秒")
    print(f"   输入: {len(large_entities)} -> 输出: {len(large_result)}")
    
    return small_time, large_time


def test_independent_processor_with_ultra_fast():
    """测试独立处理器使用超快速去重器"""
    print("\n🧪 测试独立处理器集成超快速去重器")
    print("=" * 50)
    
    # 创建模拟真实场景的数据
    test_entities = []
    
    # A-WALL图层（50个实体）
    for i in range(50):
        test_entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5, 0],
            'end_point': [i * 5 + 3, 0],
            'color': 1,
            'points': [[i * 5, 0], [i * 5 + 3, 0]]
        })
    
    # A-WINDOW图层（154个实体，包含重复）
    window_entities = create_test_data_with_duplicates(154)
    test_entities.extend(window_entities)
    
    # 0图层（122个实体，包含重复）
    layer0_entities = create_test_data_with_duplicates(122)
    for entity in layer0_entities:
        entity['layer'] = '0'
    test_entities.extend(layer0_entities)
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    print(f"   A-WALL: 50 个实体")
    print(f"   A-WINDOW: 154 个实体")
    print(f"   0: 122 个实体")
    
    # 创建独立图层处理器
    processor = IndependentLayerProcessor()
    
    print(f"\n🔄 执行独立图层处理...")
    start_time = time.time()
    
    result = processor.process_entities(test_entities)
    
    total_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   总处理时间: {total_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(result)}")
    print(f"   减少实体: {len(test_entities) - len(result)}")
    
    # 获取详细报告
    report = processor.get_layer_processing_report()
    
    print(f"\n📊 图层处理详情:")
    for layer_name, details in report['layer_details'].items():
        print(f"   {layer_name}:")
        print(f"     输入: {details['input_count']} -> 输出: {details['output_count']}")
        print(f"     处理时间: {details['processing_time']:.3f} 秒")
    
    return total_time


def run_ultra_fast_dedup_test():
    """运行超快速去重测试"""
    print("🚀 开始超快速去重测试")
    print("=" * 80)
    
    start_time = time.time()
    
    try:
        # 1. 测试超快速去重器
        test_ultra_fast_deduplicator()
        
        # 2. 测试混合超快速去重器
        small_time, large_time = test_hybrid_ultra_fast_deduplicator()
        
        # 3. 测试独立处理器集成
        processor_time = test_independent_processor_with_ultra_fast()
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 超快速去重测试完成")
        print("=" * 80)
        print(f"   总测试时间: {total_time:.2f} 秒")
        print(f"   小数据集处理: {small_time:.3f} 秒")
        print(f"   大数据集处理: {large_time:.3f} 秒")
        print(f"   集成处理器: {processor_time:.3f} 秒")
        
        # 性能评估
        if large_time < 0.01 and processor_time < 1.0:
            print(f"   🎉 性能优化极其成功！")
            print(f"   ⚡ 大数据集去重从120秒降到{large_time:.3f}秒")
            print(f"   🚀 整体处理时间: {processor_time:.3f}秒")
        elif large_time < 0.1 and processor_time < 10.0:
            print(f"   ✅ 性能优化非常成功！")
        elif large_time < 1.0 and processor_time < 60.0:
            print(f"   ✅ 性能优化成功！")
        else:
            print(f"   ⚠️ 性能仍需进一步优化")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_ultra_fast_dedup_test()
    
    if success:
        print(f"\n🎊 超快速去重测试成功！")
        print(f"   深拷贝和哈希冲突问题已解决")
        print(f"   去重性能大幅提升")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)

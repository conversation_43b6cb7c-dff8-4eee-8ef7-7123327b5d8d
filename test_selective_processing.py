#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试选择性处理模式
验证墙体图层迭代合并和其他图层去重处理的效果
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from selective_layer_processor import SelectiveLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_test_data_with_mixed_layers() -> List[Dict[str, Any]]:
    """创建包含墙体和其他图层的混合测试数据"""
    entities = []
    
    # 墙体图层数据 (A-WALL) - 应该进行迭代合并
    wall_entities = [
        # 连续的墙体线条，应该被合并
        {
            'id': 'wall_1',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1,
            'points': [[0, 0], [10, 0]]
        },
        {
            'id': 'wall_2',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [10, 0],
            'end_point': [20, 0],
            'color': 1,
            'points': [[10, 0], [20, 0]]
        },
        {
            'id': 'wall_3',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [20, 0],
            'end_point': [30, 0],
            'color': 1,
            'points': [[20, 0], [30, 0]]
        },
        # 垂直墙体
        {
            'id': 'wall_4',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [0, 10],
            'color': 1,
            'points': [[0, 0], [0, 10]]
        }
    ]
    
    # 门窗图层数据 (A-DOOR) - 应该进行去重处理
    door_entities = [
        # 重复的门实体，应该被去重
        {
            'id': 'door_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        },
        {
            'id': 'door_1_duplicate',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        },
        {
            'id': 'door_2',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [15, 0],
            'end_point': [18, 0],
            'color': 2,
            'points': [[15, 0], [18, 0]]
        }
    ]
    
    # 文字图层数据 (A-TEXT) - 应该进行去重处理
    text_entities = [
        # 重复的文字实体
        {
            'id': 'text_1',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [10, 5],
            'height': 3,
            'color': 3
        },
        {
            'id': 'text_1_duplicate',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [10, 5],
            'height': 3,
            'color': 3
        },
        {
            'id': 'text_2',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间2',
            'position': [20, 5],
            'height': 3,
            'color': 3
        }
    ]
    
    # 设备图层数据 (EQUIPMENT) - 应该进行去重处理
    equipment_entities = [
        # 重复的设备实体
        {
            'id': 'equipment_1',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [5, 5],
            'radius': 2,
            'color': 4
        },
        {
            'id': 'equipment_1_duplicate',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [5, 5],
            'radius': 2,
            'color': 4
        }
    ]
    
    # 合并所有实体
    entities.extend(wall_entities)
    entities.extend(door_entities)
    entities.extend(text_entities)
    entities.extend(equipment_entities)
    
    return entities


def test_selective_layer_processor():
    """测试选择性图层处理器"""
    print("🧪 测试选择性图层处理器")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_test_data_with_mixed_layers()
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    
    # 按图层统计输入数据
    input_layer_stats = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in input_layer_stats:
            input_layer_stats[layer] = {'total': 0, 'types': set()}
        input_layer_stats[layer]['total'] += 1
        input_layer_stats[layer]['types'].add(entity.get('type', 'UNKNOWN'))
    
    print("📋 输入数据统计:")
    for layer, stats in input_layer_stats.items():
        print(f"   {layer}: {stats['total']} 个实体, 类型: {list(stats['types'])}")
    
    # 创建选择性处理器
    processor = SelectiveLayerProcessor(distance_threshold=5, angle_threshold=2)
    
    print(f"\n🔄 执行选择性处理...")
    start_time = time.time()
    
    processed_entities = processor.process_entities(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(processed_entities)}")
    
    # 按图层统计输出数据
    output_layer_stats = {}
    for entity in processed_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in output_layer_stats:
            output_layer_stats[layer] = {'total': 0, 'types': set(), 'processing_types': set()}
        output_layer_stats[layer]['total'] += 1
        output_layer_stats[layer]['types'].add(entity.get('type', 'UNKNOWN'))
        if 'processing_type' in entity:
            output_layer_stats[layer]['processing_types'].add(entity['processing_type'])
    
    print("\n📋 输出数据统计:")
    for layer, stats in output_layer_stats.items():
        print(f"   {layer}: {stats['total']} 个实体, 类型: {list(stats['types'])}")
        if stats['processing_types']:
            print(f"     处理方式: {list(stats['processing_types'])}")
    
    # 获取处理统计
    processing_stats = processor.get_processing_statistics()
    print(f"\n📊 处理统计:")
    print(f"   墙体图层数: {processing_stats['processing_stats']['wall_layers_processed']}")
    print(f"   其他图层数: {processing_stats['processing_stats']['other_layers_processed']}")
    print(f"   墙体实体合并数: {processing_stats['processing_stats']['wall_entities_merged']}")
    print(f"   其他实体去重数: {processing_stats['processing_stats']['other_entities_deduplicated']}")
    
    # 验证处理结果
    validation = processor.validate_processing_result(test_entities, processed_entities)
    print(f"\n🔍 验证结果:")
    print(f"   验证通过: {validation['is_valid']}")
    print(f"   图层保留率: {validation['statistics']['layer_preservation_rate']:.1%}")
    print(f"   实体减少率: {validation['statistics']['reduction_rate']:.1%}")
    
    if validation['errors']:
        print(f"   错误: {validation['errors']}")
    if validation['warnings']:
        print(f"   警告: {validation['warnings']}")
    
    return {
        'input_entities': len(test_entities),
        'output_entities': len(processed_entities),
        'processing_time': processing_time,
        'input_layer_stats': input_layer_stats,
        'output_layer_stats': output_layer_stats,
        'processing_stats': processing_stats,
        'validation': validation
    }


def test_selective_mode_in_manager():
    """测试模式管理器中的选择性处理模式"""
    print("\n🧪 测试模式管理器中的选择性处理模式")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_test_data_with_mixed_layers()
    
    # 创建模式管理器
    mode_manager = LineProcessingModeManager()
    
    # 设置为选择性处理模式
    mode_manager.set_mode(LineProcessingMode.SELECTIVE)
    
    print(f"📊 当前模式: {mode_manager.get_mode_info()['mode_config']['name']}")
    print(f"📊 模式描述: {mode_manager.get_mode_info()['mode_config']['description']}")
    
    print(f"\n🔄 执行选择性处理...")
    start_time = time.time()
    
    result = mode_manager.process_entities(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理成功: {result['success']}")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(result['entities'])}")
    print(f"   模式信息: {result.get('mode_info', '')}")
    
    # 显示处理统计
    if 'processing_stats' in result:
        stats = result['processing_stats']['processing_stats']
        print(f"\n📊 详细统计:")
        print(f"   墙体图层处理: {stats['wall_layers_processed']} 个")
        print(f"   其他图层处理: {stats['other_layers_processed']} 个")
        print(f"   墙体实体合并: {stats['wall_entities_merged']} 个")
        print(f"   其他实体去重: {stats['other_entities_deduplicated']} 个")
    
    # 显示验证结果
    if 'validation' in result:
        validation = result['validation']
        print(f"\n🔍 验证结果:")
        print(f"   验证通过: {validation['is_valid']}")
        print(f"   实体减少率: {validation['statistics']['reduction_rate']:.1%}")
    
    return result


def run_comprehensive_selective_test():
    """运行综合选择性处理测试"""
    print("🚀 开始选择性处理综合测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'processor_test_result': None,
        'manager_test_result': None,
        'summary': {}
    }
    
    try:
        # 1. 测试选择性图层处理器
        results['processor_test_result'] = test_selective_layer_processor()
        
        # 2. 测试模式管理器中的选择性模式
        results['manager_test_result'] = test_selective_mode_in_manager()
        
        # 3. 生成总结
        total_time = time.time() - start_time
        
        # 分析处理效果
        processor_success = results['processor_test_result'] is not None
        manager_success = results['manager_test_result'] is not None and results['manager_test_result']['success']
        
        # 分析性能
        processor_time = results['processor_test_result']['processing_time'] if processor_success else 0
        manager_time = results['manager_test_result']['processing_time'] if manager_success else 0
        
        results['summary'] = {
            'total_test_time': total_time,
            'processor_test_success': processor_success,
            'manager_test_success': manager_success,
            'all_tests_passed': processor_success and manager_success,
            'processor_processing_time': processor_time,
            'manager_processing_time': manager_time,
            'performance_acceptable': processor_time < 1.0 and manager_time < 1.0
        }
        
        print(f"\n🎉 选择性处理综合测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   处理器测试: {'通过' if processor_success else '失败'}")
        print(f"   管理器测试: {'通过' if manager_success else '失败'}")
        print(f"   性能表现: {'良好' if results['summary']['performance_acceptable'] else '需优化'}")
        print(f"   所有测试通过: {'是' if results['summary']['all_tests_passed'] else '否'}")
        
        # 保存测试结果
        with open('selective_processing_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: selective_processing_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_selective_test()
    sys.exit(0 if success else 1)

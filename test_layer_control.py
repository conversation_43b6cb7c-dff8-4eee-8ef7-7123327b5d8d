#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图层控制按钮的脚本
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_layer_control():
    """测试图层控制功能"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🧪 开始测试图层控制功能...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("图层控制测试")
        root.geometry("1200x800")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 检查图层控制相关的方法和变量
        layer_control_items = [
            '_create_combined_image_control_area',
            '_create_layer_control_area',
            '_create_compact_shadow_control_area',
            'layer_control_container',
            'shadow_control_container',
            'layer_states',
            'layer_order'
        ]
        
        print("\n📋 检查图层控制相关项目:")
        for item_name in layer_control_items:
            if hasattr(app, item_name):
                print(f"  ✅ {item_name} - 存在")
            else:
                print(f"  ❌ {item_name} - 缺失")
        
        # 检查图层状态变量
        if hasattr(app, 'layer_states'):
            print(f"\n📊 图层状态变量:")
            for layer_key, var in app.layer_states.items():
                print(f"  - {layer_key}: {var.get()}")
        
        # 检查图层顺序
        if hasattr(app, 'layer_order'):
            print(f"\n📋 图层顺序: {app.layer_order}")
        
        # 创建测试按钮
        test_frame = tk.Frame(root)
        test_frame.pack(side='bottom', fill='x', padx=10, pady=10)
        
        tk.Label(test_frame, text="图层控制测试按钮:", font=('Arial', 12, 'bold')).pack(anchor='w')
        
        button_frame = tk.Frame(test_frame)
        button_frame.pack(fill='x', pady=5)
        
        # 测试图层切换
        def test_layer_toggle():
            try:
                if hasattr(app, 'layer_states'):
                    for layer_key in app.layer_states:
                        current_state = app.layer_states[layer_key].get()
                        app.layer_states[layer_key].set(not current_state)
                        print(f"🔄 切换图层 {layer_key}: {current_state} -> {not current_state}")
                messagebox.showinfo("测试", "图层切换测试完成")
            except Exception as e:
                messagebox.showerror("错误", f"图层切换测试失败: {e}")
        
        tk.Button(button_frame, text="测试图层切换", command=test_layer_toggle,
                 bg='#2196F3', fg='white').pack(side='left', padx=2)
        
        # 测试应用图层设置
        def test_apply_layer_settings():
            try:
                if hasattr(app, '_apply_layer_settings'):
                    app._apply_layer_settings()
                    messagebox.showinfo("测试", "应用图层设置测试完成")
                else:
                    messagebox.showwarning("警告", "未找到_apply_layer_settings方法")
            except Exception as e:
                messagebox.showerror("错误", f"应用图层设置测试失败: {e}")
        
        tk.Button(button_frame, text="测试应用设置", command=test_apply_layer_settings,
                 bg='#FF5722', fg='white').pack(side='left', padx=2)
        
        print("\n✅ 图层控制测试环境已准备就绪")
        print("📝 请在界面中查看图层控制区域是否正确显示")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_layer_control()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主程序流程
模拟主程序中从点击线条处理到数据输出的实际调用流程
"""

import os
import sys
import time
import threading
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    print("✅ 线条处理模块导入成功")
except ImportError as e:
    print(f"❌ 线条处理模块导入失败: {e}")
    sys.exit(1)

# 尝试导入主程序相关模块
try:
    import tkinter as tk
    from tkinter import ttk
    TKINTER_AVAILABLE = True
    print("✅ Tkinter导入成功")
except ImportError:
    TKINTER_AVAILABLE = False
    print("⚠️ Tkinter不可用")

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
    print("✅ Matplotlib导入成功")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib不可用")


class MockProcessor:
    """模拟主程序中的处理器"""
    
    def __init__(self):
        """初始化模拟处理器"""
        self.raw_entities = self._create_test_entities()
        self.merged_entities = []
        self.processing_complete = False
        
    def _create_test_entities(self):
        """创建测试实体数据"""
        entities = []
        
        # A-WALL图层（208个实体）
        for i in range(208):
            entities.append({
                'id': f'wall_{i}',
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_point': [i * 5.0, 0.0],
                'end_point': [i * 5.0 + 3.0, 0.0],
                'color': 1,
                'points': [[i * 5.0, 0.0], [i * 5.0 + 3.0, 0.0]]
            })
        
        # A-WINDOW图层（154个实体，包含重复）
        for i in range(154):
            if i % 10 == 0 and i > 0:
                # 重复实体
                base_index = i - 1
                entities.append({
                    'id': f'window_{i}_duplicate',
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'start_point': [base_index * 8.0, 10.0],
                    'end_point': [base_index * 8.0 + 4.0, 10.0],
                    'color': 2,
                    'points': [[base_index * 8.0, 10.0], [base_index * 8.0 + 4.0, 10.0]]
                })
            else:
                entities.append({
                    'id': f'window_{i}',
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'start_point': [i * 8.0, 10.0],
                    'end_point': [i * 8.0 + 4.0, 10.0],
                    'color': 2,
                    'points': [[i * 8.0, 10.0], [i * 8.0 + 4.0, 10.0]]
                })
        
        # 0图层（122个实体，包含重复）
        for i in range(122):
            if i % 8 == 0 and i > 0:
                # 重复实体
                base_index = i - 1
                entities.append({
                    'id': f'layer0_{i}_duplicate',
                    'type': 'LINE',
                    'layer': '0',
                    'start_point': [base_index * 6.0, 20.0],
                    'end_point': [base_index * 6.0 + 2.0, 20.0],
                    'color': 3,
                    'points': [[base_index * 6.0, 20.0], [base_index * 6.0 + 2.0, 20.0]]
                })
            else:
                entities.append({
                    'id': f'layer0_{i}',
                    'type': 'LINE',
                    'layer': '0',
                    'start_point': [i * 6.0, 20.0],
                    'end_point': [i * 6.0 + 2.0, 20.0],
                    'color': 3,
                    'points': [[i * 6.0, 20.0], [i * 6.0 + 2.0, 20.0]]
                })
        
        return entities


class MockVisualizer:
    """模拟可视化器"""
    
    def __init__(self):
        """初始化模拟可视化器"""
        self.draw_calls = 0
        self.overview_calls = 0
        self.update_calls = 0
        
        if MATPLOTLIB_AVAILABLE:
            self.fig, (self.ax_detail, self.ax_overview) = plt.subplots(1, 2, figsize=(12, 6))
        else:
            self.ax_detail = MockAxis()
            self.ax_overview = MockAxis()
    
    def draw_entities(self, entities):
        """绘制实体（模拟原始慢速方法）"""
        self.draw_calls += 1
        
        if not entities:
            return
        
        print(f"🎨 可视化器：绘制实体列表，共 {len(entities)} 个实体")
        
        # 模拟原始方法的慢速绘制
        start_time = time.time()
        
        if MATPLOTLIB_AVAILABLE:
            self.ax_detail.clear()
            self.ax_detail.set_aspect('equal')
            self.ax_detail.grid(True, linestyle='--', alpha=0.3)
            
            # 逐个绘制实体（模拟原始慢速方法）
            for i, entity in enumerate(entities):
                if entity.get('type') == 'LINE' and 'points' in entity:
                    points = entity['points']
                    if len(points) >= 2:
                        x = [p[0] for p in points[:2]]
                        y = [p[1] for p in points[:2]]
                        color = 'blue' if entity.get('layer') == 'A-WALL' else 'red'
                        self.ax_detail.plot(x, y, color=color, linewidth=1.0, alpha=0.8)
                
                # 模拟每个实体的绘制时间
                if i % 50 == 0:
                    time.sleep(0.001)
        else:
            # 模拟绘制时间
            time.sleep(0.001 * len(entities))
        
        draw_time = time.time() - start_time
        print(f"✅ 实体列表绘制完成，耗时: {draw_time:.3f} 秒")
    
    def visualize_overview(self, all_entities, current_group_entities=None, 
                         labeled_entities=None, **kwargs):
        """可视化全图概览（模拟原始慢速方法）"""
        self.overview_calls += 1
        
        print(f"🌍 可视化器：开始绘制全图概览")
        print(f"  - 总实体数: {len(all_entities) if all_entities else 0}")
        
        if not all_entities:
            print("  ⚠️ 没有实体数据，跳过绘制")
            return
        
        start_time = time.time()
        
        if MATPLOTLIB_AVAILABLE:
            self.ax_overview.clear()
            self.ax_overview.set_aspect('equal')
            self.ax_overview.grid(True, linestyle='--', alpha=0.3)
            
            # 模拟原始的慢速概览绘制
            for i, entity in enumerate(all_entities):
                # 模拟组查找的开销（这是主要瓶颈）
                time.sleep(0.00005)  # 每个实体0.05毫秒的查找时间
                
                if entity.get('type') == 'LINE' and 'points' in entity:
                    points = entity['points']
                    if len(points) >= 2:
                        x = [p[0] for p in points[:2]]
                        y = [p[1] for p in points[:2]]
                        color = 'lightgray'
                        self.ax_overview.plot(x, y, color=color, linewidth=0.5, alpha=0.7)
                
                # 模拟批处理
                if i % 100 == 0:
                    time.sleep(0.001)
        else:
            # 模拟概览时间（包括组查找开销）
            time.sleep(0.00005 * len(all_entities))  # 组查找时间
            time.sleep(0.001 * len(all_entities))    # 绘制时间
        
        overview_time = time.time() - start_time
        print(f"✅ 全图概览绘制完成，耗时: {overview_time:.3f} 秒")


class MockAxis:
    """模拟matplotlib轴对象"""
    
    def clear(self):
        pass
    
    def set_aspect(self, aspect):
        pass
    
    def grid(self, *args, **kwargs):
        pass
    
    def plot(self, x, y, *args, **kwargs):
        pass


def simulate_main_program_line_processing():
    """模拟主程序中的线条处理流程"""
    print("🧪 模拟主程序线条处理流程")
    print("=" * 80)
    
    # 步骤1: 模拟用户点击线条处理按钮
    print("📊 步骤1: 用户点击线条处理按钮")
    click_start = time.time()
    
    # 模拟按钮点击的响应时间
    time.sleep(0.001)  # 模拟UI响应
    
    click_time = time.time() - click_start
    print(f"   按钮响应时间: {click_time:.3f} 秒")
    
    # 步骤2: 创建处理器和可视化器
    print("📊 步骤2: 创建处理器和可视化器")
    setup_start = time.time()
    
    processor = MockProcessor()
    visualizer = MockVisualizer()
    
    setup_time = time.time() - setup_start
    print(f"   组件创建时间: {setup_time:.3f} 秒")
    print(f"   原始实体数量: {len(processor.raw_entities)}")
    
    # 步骤3: 数据读取和验证
    print("📊 步骤3: 数据读取和验证")
    data_start = time.time()
    
    # 模拟从processor.raw_entities读取数据
    input_entities = processor.raw_entities
    
    # 模拟数据验证
    valid_entities = []
    for entity in input_entities:
        if isinstance(entity, dict) and 'type' in entity and 'layer' in entity:
            valid_entities.append(entity)
    
    data_time = time.time() - data_start
    print(f"   数据读取验证时间: {data_time:.3f} 秒")
    print(f"   有效实体数量: {len(valid_entities)}")
    
    # 步骤4: 初始可视化（显示原始数据）
    print("📊 步骤4: 初始可视化（显示原始数据）")
    initial_viz_start = time.time()
    
    visualizer.draw_entities(valid_entities)
    visualizer.visualize_overview(valid_entities)
    
    initial_viz_time = time.time() - initial_viz_start
    print(f"   初始可视化时间: {initial_viz_time:.3f} 秒")
    
    # 步骤5: 创建线条处理模式管理器
    print("📊 步骤5: 创建线条处理模式管理器")
    mode_start = time.time()
    
    line_mode_manager = LineProcessingModeManager()
    line_mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    
    mode_time = time.time() - mode_start
    print(f"   模式管理器创建时间: {mode_time:.3f} 秒")
    
    # 步骤6: 执行线条处理
    print("📊 步骤6: 执行线条处理")
    processing_start = time.time()
    
    result = line_mode_manager.process_entities(valid_entities)
    
    processing_time = time.time() - processing_start
    print(f"   线条处理时间: {processing_time:.3f} 秒")
    
    # 验证处理结果
    success = result.get('success', False)
    processed_entities = result.get('entities', [])
    print(f"   处理成功: {success}")
    print(f"   处理后实体数量: {len(processed_entities)}")
    
    # 步骤7: 更新处理器状态
    print("📊 步骤7: 更新处理器状态")
    update_start = time.time()
    
    # 模拟更新processor.merged_entities
    processor.merged_entities = processed_entities
    processor.processing_complete = True
    
    update_time = time.time() - update_start
    print(f"   状态更新时间: {update_time:.3f} 秒")
    
    # 步骤8: 更新可视化（显示处理后数据）
    print("📊 步骤8: 更新可视化（显示处理后数据）")
    final_viz_start = time.time()
    
    visualizer.draw_entities(processed_entities)
    visualizer.visualize_overview(processed_entities)
    
    final_viz_time = time.time() - final_viz_start
    print(f"   最终可视化时间: {final_viz_time:.3f} 秒")
    
    # 步骤9: 数据输出准备
    print("📊 步骤9: 数据输出准备")
    output_start = time.time()
    
    # 模拟数据输出准备
    output_data = {
        'entities': processed_entities,
        'statistics': {
            'input_count': len(valid_entities),
            'output_count': len(processed_entities),
            'processing_time': processing_time
        }
    }
    
    output_time = time.time() - output_start
    print(f"   数据输出准备时间: {output_time:.3f} 秒")
    
    # 总结
    total_time = (click_time + setup_time + data_time + initial_viz_time + 
                 mode_time + processing_time + update_time + final_viz_time + output_time)
    
    print(f"\n📈 主程序流程时间分解:")
    print(f"   步骤1 - 按钮响应: {click_time:.3f} 秒 ({click_time/total_time*100:.1f}%)")
    print(f"   步骤2 - 组件创建: {setup_time:.3f} 秒 ({setup_time/total_time*100:.1f}%)")
    print(f"   步骤3 - 数据读取: {data_time:.3f} 秒 ({data_time/total_time*100:.1f}%)")
    print(f"   步骤4 - 初始可视化: {initial_viz_time:.3f} 秒 ({initial_viz_time/total_time*100:.1f}%)")
    print(f"   步骤5 - 模式管理器: {mode_time:.3f} 秒 ({mode_time/total_time*100:.1f}%)")
    print(f"   步骤6 - 线条处理: {processing_time:.3f} 秒 ({processing_time/total_time*100:.1f}%)")
    print(f"   步骤7 - 状态更新: {update_time:.3f} 秒 ({update_time/total_time*100:.1f}%)")
    print(f"   步骤8 - 最终可视化: {final_viz_time:.3f} 秒 ({final_viz_time/total_time*100:.1f}%)")
    print(f"   步骤9 - 数据输出: {output_time:.3f} 秒 ({output_time/total_time*100:.1f}%)")
    print(f"   总时间: {total_time:.3f} 秒")
    
    # 识别瓶颈
    steps = [
        ('按钮响应', click_time),
        ('组件创建', setup_time),
        ('数据读取', data_time),
        ('初始可视化', initial_viz_time),
        ('模式管理器', mode_time),
        ('线条处理', processing_time),
        ('状态更新', update_time),
        ('最终可视化', final_viz_time),
        ('数据输出', output_time)
    ]
    
    # 找出最慢的步骤
    slowest_step = max(steps, key=lambda x: x[1])
    print(f"\n🚨 主程序流程瓶颈识别:")
    print(f"   最慢步骤: {slowest_step[0]} ({slowest_step[1]:.3f} 秒)")
    
    # 可视化相关时间
    visualization_time = initial_viz_time + final_viz_time
    pure_processing_time = processing_time
    
    print(f"\n📊 时间分类:")
    print(f"   纯处理时间: {pure_processing_time:.3f} 秒 ({pure_processing_time/total_time*100:.1f}%)")
    print(f"   可视化时间: {visualization_time:.3f} 秒 ({visualization_time/total_time*100:.1f}%)")
    print(f"   其他时间: {total_time - pure_processing_time - visualization_time:.3f} 秒")
    
    if visualization_time > pure_processing_time * 2:
        print(f"   ⚠️ 可视化时间是处理时间的 {visualization_time/pure_processing_time:.1f} 倍")
        print(f"   🎯 主要瓶颈在可视化环节")
    
    return {
        'total_time': total_time,
        'processing_time': pure_processing_time,
        'visualization_time': visualization_time,
        'bottleneck': slowest_step,
        'steps': dict(steps)
    }


def run_main_program_flow_test():
    """运行主程序流程测试"""
    print("🚀 开始主程序流程测试")
    print("=" * 100)
    
    overall_start = time.time()
    
    try:
        # 执行主程序流程模拟
        results = simulate_main_program_line_processing()
        
        overall_time = time.time() - overall_start
        
        print(f"\n🎉 主程序流程测试完成")
        print("=" * 100)
        print(f"   总测试时间: {overall_time:.2f} 秒")
        
        # 最终分析
        print(f"\n📊 主程序性能分析:")
        print(f"   模拟流程总时间: {results['total_time']:.3f} 秒")
        print(f"   处理效率: {results['processing_time']:.3f} 秒")
        print(f"   可视化开销: {results['visualization_time']:.3f} 秒")
        print(f"   主要瓶颈: {results['bottleneck'][0]} ({results['bottleneck'][1]:.3f} 秒)")
        
        if results['visualization_time'] > 1.0:
            print(f"   ❌ 可视化时间过长: {results['visualization_time']:.3f} 秒")
            print(f"   🎯 这是用户感受到等待时间过长的主要原因")
        elif results['processing_time'] > 1.0:
            print(f"   ❌ 处理时间过长: {results['processing_time']:.3f} 秒")
        else:
            print(f"   ✅ 整体性能良好")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_main_program_flow_test()
    
    if success:
        print(f"\n🎊 主程序流程测试成功！")
        print(f"   已识别出用户体验中的具体瓶颈")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)

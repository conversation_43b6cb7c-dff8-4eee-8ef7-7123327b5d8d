#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断性能瓶颈
找出线条处理过程中的具体性能问题
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from independent_layer_processor import IndependentLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    PROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"❌ 处理模块导入失败: {e}")
    PROCESSING_AVAILABLE = False

try:
    # 尝试导入可视化相关模块
    from enhanced_visualizer_v2_fill import EnhancedVisualizerV2Fill
    VISUALIZER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 可视化模块导入失败: {e}")
    VISUALIZER_AVAILABLE = False


def create_large_realistic_data(wall_count=208, window_count=154, layer0_count=122):
    """创建大规模真实数据"""
    entities = []
    
    print(f"📊 创建测试数据: 墙体{wall_count}, 窗户{window_count}, 0图层{layer0_count}")
    
    # A-WALL图层（墙体）
    for i in range(wall_count):
        entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5, 0],
            'end_point': [i * 5 + 3, 0],
            'color': 1,
            'points': [[i * 5, 0], [i * 5 + 3, 0]]
        })
    
    # A-WINDOW图层（窗户，包含重复）
    for i in range(window_count):
        if i % 10 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'window_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [base_index * 8, 10],
                'end_point': [base_index * 8 + 4, 10],
                'color': 2,
                'points': [[base_index * 8, 10], [base_index * 8 + 4, 10]]
            })
        else:
            entities.append({
                'id': f'window_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 8, 10],
                'end_point': [i * 8 + 4, 10],
                'color': 2,
                'points': [[i * 8, 10], [i * 8 + 4, 10]]
            })
    
    # 0图层（包含重复）
    for i in range(layer0_count):
        if i % 8 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'layer0_{i}_duplicate',
                'type': 'LINE',
                'layer': '0',
                'start_point': [base_index * 6, 20],
                'end_point': [base_index * 6 + 2, 20],
                'color': 3,
                'points': [[base_index * 6, 20], [base_index * 6 + 2, 20]]
            })
        else:
            entities.append({
                'id': f'layer0_{i}',
                'type': 'LINE',
                'layer': '0',
                'start_point': [i * 6, 20],
                'end_point': [i * 6 + 2, 20],
                'color': 3,
                'points': [[i * 6, 20], [i * 6 + 2, 20]]
            })
    
    return entities


def test_processing_performance():
    """测试处理性能"""
    print("🧪 测试处理性能")
    print("=" * 50)
    
    if not PROCESSING_AVAILABLE:
        print("❌ 处理模块不可用")
        return None
    
    # 创建测试数据
    test_entities = create_large_realistic_data()
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    
    # 测试独立图层处理器
    print(f"\n🔄 测试独立图层处理器...")
    processor = IndependentLayerProcessor()
    
    start_time = time.time()
    result = processor.process_entities(test_entities)
    processing_time = time.time() - start_time
    
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(result)}")
    
    # 测试模式管理器
    print(f"\n🔄 测试模式管理器...")
    mode_manager = LineProcessingModeManager()
    mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    
    start_time = time.time()
    manager_result = mode_manager.process_entities(test_entities)
    manager_time = time.time() - start_time
    
    print(f"   处理时间: {manager_time:.3f} 秒")
    print(f"   处理成功: {manager_result['success']}")
    print(f"   输出实体: {len(manager_result['entities'])}")
    
    return {
        'processor_time': processing_time,
        'manager_time': manager_time,
        'input_entities': len(test_entities),
        'output_entities': len(result),
        'processed_entities': result
    }


def test_visualization_performance(entities):
    """测试可视化性能"""
    print("\n🧪 测试可视化性能")
    print("=" * 50)
    
    if not VISUALIZER_AVAILABLE:
        print("❌ 可视化模块不可用")
        return None
    
    try:
        # 创建可视化器
        print("🎨 创建可视化器...")
        visualizer = EnhancedVisualizerV2Fill()
        
        # 测试清除操作
        print("🧹 测试清除操作...")
        start_time = time.time()
        visualizer.clear_all()
        clear_time = time.time() - start_time
        print(f"   清除时间: {clear_time:.3f} 秒")
        
        # 测试绘制实体
        print(f"🎨 测试绘制实体 ({len(entities)} 个)...")
        start_time = time.time()
        visualizer.draw_entities(entities)
        draw_time = time.time() - start_time
        print(f"   绘制时间: {draw_time:.3f} 秒")
        
        # 测试概览可视化
        print("🌍 测试概览可视化...")
        start_time = time.time()
        visualizer.visualize_overview(entities, None, [])
        overview_time = time.time() - start_time
        print(f"   概览时间: {overview_time:.3f} 秒")
        
        total_viz_time = clear_time + draw_time + overview_time
        print(f"📊 可视化总时间: {total_viz_time:.3f} 秒")
        
        return {
            'clear_time': clear_time,
            'draw_time': draw_time,
            'overview_time': overview_time,
            'total_time': total_viz_time
        }
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_data_operations():
    """测试数据操作性能"""
    print("\n🧪 测试数据操作性能")
    print("=" * 50)
    
    # 创建测试数据
    test_entities = create_large_realistic_data()
    
    # 测试数据复制
    print("📋 测试数据复制...")
    start_time = time.time()
    import copy
    copied_entities = copy.deepcopy(test_entities)
    copy_time = time.time() - start_time
    print(f"   深拷贝时间: {copy_time:.3f} 秒")
    
    # 测试数据序列化
    print("💾 测试数据序列化...")
    start_time = time.time()
    json_str = json.dumps(test_entities, default=str)
    serialize_time = time.time() - start_time
    print(f"   序列化时间: {serialize_time:.3f} 秒")
    
    # 测试数据反序列化
    print("📤 测试数据反序列化...")
    start_time = time.time()
    deserialized_entities = json.loads(json_str)
    deserialize_time = time.time() - start_time
    print(f"   反序列化时间: {deserialize_time:.3f} 秒")
    
    return {
        'copy_time': copy_time,
        'serialize_time': serialize_time,
        'deserialize_time': deserialize_time
    }


def run_comprehensive_diagnosis():
    """运行综合性能诊断"""
    print("🚀 开始综合性能诊断")
    print("=" * 80)
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'processing_test': None,
        'visualization_test': None,
        'data_operations_test': None,
        'bottleneck_analysis': {}
    }
    
    try:
        # 1. 测试处理性能
        processing_result = test_processing_performance()
        results['processing_test'] = processing_result
        
        # 2. 测试可视化性能
        if processing_result and 'processed_entities' in processing_result:
            viz_result = test_visualization_performance(processing_result['processed_entities'])
            results['visualization_test'] = viz_result
        
        # 3. 测试数据操作性能
        data_result = test_data_operations()
        results['data_operations_test'] = data_result
        
        # 4. 分析瓶颈
        print(f"\n🔍 性能瓶颈分析")
        print("=" * 50)
        
        if processing_result:
            proc_time = processing_result['processor_time']
            print(f"📊 处理阶段: {proc_time:.3f} 秒")
            
            if proc_time > 1.0:
                print(f"   ⚠️ 处理时间过长 (>{proc_time:.1f}秒)")
                results['bottleneck_analysis']['processing'] = 'slow'
            else:
                print(f"   ✅ 处理时间正常")
                results['bottleneck_analysis']['processing'] = 'fast'
        
        if viz_result:
            viz_time = viz_result['total_time']
            print(f"🎨 可视化阶段: {viz_time:.3f} 秒")
            
            if viz_time > 5.0:
                print(f"   ⚠️ 可视化时间过长 (>{viz_time:.1f}秒)")
                results['bottleneck_analysis']['visualization'] = 'slow'
                
                # 详细分析可视化子阶段
                if viz_result['draw_time'] > 2.0:
                    print(f"     🎨 绘制实体过慢: {viz_result['draw_time']:.3f} 秒")
                if viz_result['overview_time'] > 2.0:
                    print(f"     🌍 概览绘制过慢: {viz_result['overview_time']:.3f} 秒")
            else:
                print(f"   ✅ 可视化时间正常")
                results['bottleneck_analysis']['visualization'] = 'fast'
        
        if data_result:
            copy_time = data_result['copy_time']
            if copy_time > 1.0:
                print(f"📋 数据复制过慢: {copy_time:.3f} 秒")
                results['bottleneck_analysis']['data_copy'] = 'slow'
            else:
                print(f"📋 数据复制正常: {copy_time:.3f} 秒")
                results['bottleneck_analysis']['data_copy'] = 'fast'
        
        # 总结
        total_time = time.time() - start_time
        print(f"\n📈 诊断总结")
        print("=" * 50)
        print(f"   总诊断时间: {total_time:.2f} 秒")
        
        # 识别主要瓶颈
        bottlenecks = []
        if results['bottleneck_analysis'].get('processing') == 'slow':
            bottlenecks.append('处理算法')
        if results['bottleneck_analysis'].get('visualization') == 'slow':
            bottlenecks.append('可视化渲染')
        if results['bottleneck_analysis'].get('data_copy') == 'slow':
            bottlenecks.append('数据操作')
        
        if bottlenecks:
            print(f"   🚨 发现瓶颈: {', '.join(bottlenecks)}")
            results['bottleneck_analysis']['main_bottlenecks'] = bottlenecks
        else:
            print(f"   ✅ 未发现明显瓶颈")
            results['bottleneck_analysis']['main_bottlenecks'] = []
        
        # 保存诊断结果
        with open('performance_diagnosis_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   诊断结果已保存到: performance_diagnosis_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_diagnosis()
    sys.exit(0 if success else 1)

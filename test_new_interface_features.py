#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新界面功能的脚本
验证：
1. 新增的图像控制按钮区域
2. 阴影识别功能
3. 保存填充按钮的颜色检查提示
4. 重置视图按钮
5. 图像保存和放大查看按钮
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_interface():
    """测试新界面功能"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🧪 开始测试新界面功能...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("新界面功能测试")
        root.geometry("1200x800")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 测试新增的方法是否存在
        test_methods = [
            '_create_image_control_buttons',
            '_create_shadow_control_area', 
            '_reset_left_view',
            '_reset_right_view',
            '_save_current_image',
            '_open_zoom_view_window',
            '_start_shadow_recognition',
            '_update_shadow_preview',
            '_apply_shadow_settings',
            '_check_color_completeness'
        ]
        
        print("\n📋 检查新增方法:")
        for method_name in test_methods:
            if hasattr(app, method_name):
                print(f"  ✅ {method_name} - 存在")
            else:
                print(f"  ❌ {method_name} - 缺失")
        
        # 测试新增的变量是否存在
        test_variables = [
            'shadow_length_var',
            'shadow_transparency_var',
            'image_control_buttons_frame'
        ]
        
        print("\n📋 检查新增变量:")
        for var_name in test_variables:
            if hasattr(app, var_name):
                print(f"  ✅ {var_name} - 存在")
            else:
                print(f"  ❌ {var_name} - 缺失")
        
        # 创建测试按钮来验证功能
        test_frame = tk.Frame(root)
        test_frame.pack(side='bottom', fill='x', padx=10, pady=10)
        
        tk.Label(test_frame, text="功能测试按钮:", font=('Arial', 12, 'bold')).pack(anchor='w')
        
        button_frame = tk.Frame(test_frame)
        button_frame.pack(fill='x', pady=5)
        
        # 测试重置视图按钮
        def test_reset_views():
            try:
                app._reset_left_view()
                app._reset_right_view()
                messagebox.showinfo("测试", "重置视图功能测试完成")
            except Exception as e:
                messagebox.showerror("错误", f"重置视图测试失败: {e}")
        
        tk.Button(button_frame, text="测试重置视图", command=test_reset_views,
                 bg='#4CAF50', fg='white').pack(side='left', padx=2)
        
        # 测试阴影识别按钮
        def test_shadow_recognition():
            try:
                app._start_shadow_recognition()
                messagebox.showinfo("测试", "阴影识别功能测试完成")
            except Exception as e:
                messagebox.showerror("错误", f"阴影识别测试失败: {e}")
        
        tk.Button(button_frame, text="测试阴影识别", command=test_shadow_recognition,
                 bg='#607D8B', fg='white').pack(side='left', padx=2)
        
        # 测试图像保存按钮
        def test_save_image():
            try:
                app._save_current_image()
                messagebox.showinfo("测试", "图像保存功能测试完成")
            except Exception as e:
                messagebox.showerror("错误", f"图像保存测试失败: {e}")
        
        tk.Button(button_frame, text="测试图像保存", command=test_save_image,
                 bg='#9C27B0', fg='white').pack(side='left', padx=2)
        
        # 测试放大查看按钮
        def test_zoom_view():
            try:
                app._open_zoom_view_window()
                messagebox.showinfo("测试", "放大查看功能测试完成")
            except Exception as e:
                messagebox.showerror("错误", f"放大查看测试失败: {e}")
        
        tk.Button(button_frame, text="测试放大查看", command=test_zoom_view,
                 bg='#FF9800', fg='white').pack(side='left', padx=2)
        
        # 测试颜色检查功能
        def test_color_check():
            try:
                result = app._check_color_completeness()
                messagebox.showinfo("颜色检查结果", 
                    f"总实体数: {result['total_entities']}\n"
                    f"所有类型: {result['all_types']}\n"
                    f"缺失颜色: {result['missing_colors']}\n"
                    f"有缺失: {result['has_missing']}")
            except Exception as e:
                messagebox.showerror("错误", f"颜色检查测试失败: {e}")
        
        tk.Button(button_frame, text="测试颜色检查", command=test_color_check,
                 bg='#2196F3', fg='white').pack(side='left', padx=2)
        
        print("\n✅ 新界面功能测试环境已准备就绪")
        print("📝 请在界面中测试各项新功能")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_interface()

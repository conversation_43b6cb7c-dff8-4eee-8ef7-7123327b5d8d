#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单性能检查
快速验证优化效果
"""

import os
import sys
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试模块导入...")
    
    try:
        from independent_layer_processor import IndependentLayerProcessor
        print("✅ IndependentLayerProcessor 导入成功")
    except ImportError as e:
        print(f"❌ IndependentLayerProcessor 导入失败: {e}")
        return False
    
    try:
        from optimized_deduplication import HybridDeduplicator
        print("✅ HybridDeduplicator 导入成功")
    except ImportError as e:
        print(f"❌ HybridDeduplicator 导入失败: {e}")
        return False
    
    try:
        from simple_fast_merger import SimpleFastMerger
        print("✅ SimpleFastMerger 导入成功")
    except ImportError as e:
        print(f"❌ SimpleFastMerger 导入失败: {e}")
        return False
    
    return True


def test_small_data_processing():
    """测试小数据处理"""
    print("\n🧪 测试小数据处理...")
    
    try:
        from independent_layer_processor import IndependentLayerProcessor
        
        # 创建小量测试数据
        test_entities = []
        
        # 墙体图层（10个实体）
        for i in range(10):
            test_entities.append({
                'id': f'wall_{i}',
                'type': 'LINE',
                'layer': 'A-WALL',
                'start_point': [i * 5, 0],
                'end_point': [i * 5 + 3, 0],
                'color': 1,
                'points': [[i * 5, 0], [i * 5 + 3, 0]]
            })
        
        # 窗户图层（5个实体，1个重复）
        for i in range(5):
            if i == 4:
                # 重复实体
                test_entities.append({
                    'id': f'window_{i}_duplicate',
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'start_point': [0, 10],
                    'end_point': [4, 10],
                    'color': 2,
                    'points': [[0, 10], [4, 10]]
                })
            else:
                test_entities.append({
                    'id': f'window_{i}',
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'start_point': [i * 8, 10],
                    'end_point': [i * 8 + 4, 10],
                    'color': 2,
                    'points': [[i * 8, 10], [i * 8 + 4, 10]]
                })
        
        print(f"   输入数据: {len(test_entities)} 个实体")
        
        # 创建处理器
        processor = IndependentLayerProcessor()
        
        # 执行处理
        start_time = time.time()
        result = processor.process_entities(test_entities)
        processing_time = time.time() - start_time
        
        print(f"   处理时间: {processing_time:.3f} 秒")
        print(f"   输出数据: {len(result)} 个实体")
        print(f"   减少实体: {len(test_entities) - len(result)} 个")
        
        if processing_time < 1.0:
            print("✅ 小数据处理性能良好")
            return True
        else:
            print("⚠️ 小数据处理性能需要优化")
            return False
    
    except Exception as e:
        print(f"❌ 小数据处理测试失败: {e}")
        return False


def test_deduplication_performance():
    """测试去重性能"""
    print("\n🧪 测试去重性能...")
    
    try:
        from optimized_deduplication import HybridDeduplicator
        
        # 创建测试数据（100个实体，10个重复）
        test_entities = []
        for i in range(100):
            if i % 10 == 0 and i > 0:
                # 重复实体
                test_entities.append({
                    'id': f'entity_{i}_duplicate',
                    'type': 'LINE',
                    'layer': 'TEST',
                    'start_point': [0, 0],
                    'end_point': [10, 0],
                    'color': 1,
                    'points': [[0, 0], [10, 0]]
                })
            else:
                test_entities.append({
                    'id': f'entity_{i}',
                    'type': 'LINE',
                    'layer': 'TEST',
                    'start_point': [i, 0],
                    'end_point': [i + 10, 0],
                    'color': 1,
                    'points': [[i, 0], [i + 10, 0]]
                })
        
        print(f"   输入数据: {len(test_entities)} 个实体")
        
        # 创建去重器
        deduplicator = HybridDeduplicator(threshold=50)
        
        # 执行去重
        start_time = time.time()
        result = deduplicator.deduplicate_entities(test_entities, 'TEST', 'performance_test')
        dedup_time = time.time() - start_time
        
        print(f"   去重时间: {dedup_time:.3f} 秒")
        print(f"   输出数据: {len(result)} 个实体")
        print(f"   移除重复: {len(test_entities) - len(result)} 个")
        
        if dedup_time < 0.1:
            print("✅ 去重性能优秀")
            return True
        elif dedup_time < 1.0:
            print("✅ 去重性能良好")
            return True
        else:
            print("⚠️ 去重性能需要优化")
            return False
    
    except Exception as e:
        print(f"❌ 去重性能测试失败: {e}")
        return False


def test_merge_performance():
    """测试合并性能"""
    print("\n🧪 测试合并性能...")
    
    try:
        from simple_fast_merger import SimpleFastMerger
        
        # 创建测试数据（50条连续线条）
        test_lines = []
        for i in range(50):
            test_lines.append([[i * 5, 0], [i * 5 + 5, 0]])
        
        print(f"   输入线条: {len(test_lines)} 条")
        
        # 创建合并器
        merger = SimpleFastMerger(distance_threshold=1, angle_threshold=5)
        
        # 执行合并
        start_time = time.time()
        result = merger.merge_lines(test_lines)
        merge_time = time.time() - start_time
        
        print(f"   合并时间: {merge_time:.3f} 秒")
        print(f"   输出线条: {len(result)} 条")
        print(f"   合并效果: {len(test_lines) - len(result)} 条被合并")
        
        if merge_time < 0.1:
            print("✅ 合并性能优秀")
            return True
        elif merge_time < 1.0:
            print("✅ 合并性能良好")
            return True
        else:
            print("⚠️ 合并性能需要优化")
            return False
    
    except Exception as e:
        print(f"❌ 合并性能测试失败: {e}")
        return False


def run_simple_performance_check():
    """运行简单性能检查"""
    print("🚀 开始简单性能检查")
    print("=" * 50)
    
    results = []
    
    # 1. 测试导入
    results.append(test_basic_imports())
    
    # 2. 测试小数据处理
    results.append(test_small_data_processing())
    
    # 3. 测试去重性能
    results.append(test_deduplication_performance())
    
    # 4. 测试合并性能
    results.append(test_merge_performance())
    
    # 总结
    print(f"\n📊 性能检查总结")
    print("=" * 50)
    
    passed_tests = sum(results)
    total_tests = len(results)
    
    print(f"   通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有性能测试通过！")
        print("✅ 优化措施生效，性能问题已解决")
        return True
    elif passed_tests >= total_tests * 0.75:
        print("✅ 大部分性能测试通过")
        print("⚠️ 仍有部分性能问题需要关注")
        return True
    else:
        print("❌ 多项性能测试失败")
        print("🔧 需要进一步优化")
        return False


if __name__ == "__main__":
    success = run_simple_performance_check()
    
    if success:
        print(f"\n🎊 性能检查成功！")
        print(f"   线条处理性能问题已基本解决")
        print(f"   建议在实际使用中验证效果")
    else:
        print(f"\n😞 性能检查发现问题")
        print(f"   需要进一步调试和优化")
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进的集成效果
验证基于测试结果的代码调整是否有效
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from layer_aware_line_merger import LayerAwareLineMerger, EnhancedLineMerger
    from enhanced_cad_processor import EnhancedCADProcessor
    from main_program_integration import MainProgramIntegration
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_test_data_with_cross_layer_risks() -> List[Dict[str, Any]]:
    """创建具有跨图层合并风险的测试数据"""
    return [
        # 墙体线条1 - 主墙体
        {
            'id': 'wall_main',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]]
        },
        
        # 构造线条 - 与墙体完全重合（高风险）
        {
            'id': 'construction_overlay',
            'type': 'LINE',
            'layer': 'CONSTRUCTION',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 9,
            'points': [[0, 0], [100, 0]]
        },
        
        # 门线条 - 与墙体部分重合（中风险）
        {
            'id': 'door_opening',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [30, 0],
            'end_point': [50, 0],
            'color': 2,
            'points': [[30, 0], [50, 0]]
        },
        
        # 墙体线条2 - 与墙体1端点连接
        {
            'id': 'wall_perpendicular',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [100, 0],
            'end_point': [100, 100],
            'color': 1,
            'points': [[100, 0], [100, 100]]
        },
        
        # 设备线条 - 与墙体端点连接（低风险）
        {
            'id': 'equipment_extension',
            'type': 'LINE',
            'layer': 'EQUIPMENT',
            'start_point': [100, 0],
            'end_point': [120, 0],
            'color': 8,
            'points': [[100, 0], [120, 0]]
        },
        
        # 文字实体 - 不应该被合并
        {
            'id': 'room_text',
            'type': 'TEXT',
            'layer': 'A-ANNO-TEXT',
            'text': '房间1',
            'position': [50, 50],
            'height': 5,
            'color': 5
        },
        
        # 标注实体 - 不应该被合并
        {
            'id': 'wall_dimension',
            'type': 'DIMENSION',
            'layer': 'A-DIMS',
            'def_points': [[0, 0], [100, 0], [50, -20]],
            'text': '100',
            'color': 6
        }
    ]


def test_layer_aware_line_merger():
    """测试图层感知线条合并器"""
    print("\n🧪 测试图层感知线条合并器")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_test_data_with_cross_layer_risks()
    
    print(f"📊 输入实体: {len(test_entities)} 个")
    for entity in test_entities:
        print(f"   {entity['id']} ({entity['type']}) - {entity['layer']}")
    
    # 创建图层感知合并器
    merger = LayerAwareLineMerger(distance_threshold=5, angle_threshold=2)
    
    print(f"\n🔄 执行图层感知合并...")
    start_time = time.time()
    
    merged_entities = merger.merge_entities_by_layer(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 合并结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(merged_entities)}")
    
    # 分析图层保留情况
    input_layers = set()
    output_layers = set()
    
    for entity in test_entities:
        if 'layer' in entity:
            input_layers.add(entity['layer'])
    
    for entity in merged_entities:
        if 'layer' in entity:
            output_layers.add(entity['layer'])
    
    print(f"\n🔍 图层分析:")
    print(f"   输入图层: {sorted(input_layers)}")
    print(f"   输出图层: {sorted(output_layers)}")
    print(f"   图层保留率: {len(output_layers) / len(input_layers) * 100:.1f}%")
    
    # 验证合并结果
    validation = merger.validate_merge_result(test_entities, merged_entities)
    print(f"\n✅ 验证结果:")
    print(f"   验证通过: {validation['is_valid']}")
    if validation['errors']:
        print(f"   错误: {validation['errors']}")
    if validation['warnings']:
        print(f"   警告: {validation['warnings']}")
    
    # 获取合并统计
    stats = merger.get_merge_statistics()
    print(f"\n📊 合并统计:")
    print(f"   处理图层数: {stats['merge_stats']['layers_processed']}")
    print(f"   跨图层预防: {stats['cross_layer_prevention']['prevention_count']}")
    
    return {
        'input_entities': len(test_entities),
        'output_entities': len(merged_entities),
        'input_layers': len(input_layers),
        'output_layers': len(output_layers),
        'layer_preservation_rate': len(output_layers) / len(input_layers) if input_layers else 1.0,
        'validation_passed': validation['is_valid'],
        'processing_time': processing_time,
        'merge_stats': stats
    }


def test_enhanced_cad_processor():
    """测试增强CAD数据处理器"""
    print("\n🧪 测试增强CAD数据处理器")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_test_data_with_cross_layer_risks()
    
    # 测试传统改进模式
    print(f"📊 测试传统改进模式...")
    processor_traditional = EnhancedCADProcessor(use_enhanced_architecture=False)
    
    start_time = time.time()
    result_traditional = processor_traditional.process_entities(
        test_entities, 
        file_path="test_cross_layer.dxf",
        distance_threshold=20
    )
    traditional_time = time.time() - start_time
    
    print(f"   处理时间: {traditional_time:.3f} 秒")
    print(f"   处理成功: {result_traditional['success']}")
    print(f"   输出实体: {len(result_traditional.get('entities', []))}")
    print(f"   输出分组: {len(result_traditional.get('groups', []))}")
    
    # 测试完全增强模式
    print(f"\n📈 测试完全增强模式...")
    processor_enhanced = EnhancedCADProcessor(use_enhanced_architecture=True)
    
    start_time = time.time()
    result_enhanced = processor_enhanced.process_entities(
        test_entities,
        file_path="test_cross_layer.dxf", 
        distance_threshold=20
    )
    enhanced_time = time.time() - start_time
    
    print(f"   处理时间: {enhanced_time:.3f} 秒")
    print(f"   处理成功: {result_enhanced['success']}")
    print(f"   输出实体: {len(result_enhanced.get('entities', []))}")
    print(f"   输出分组: {len(result_enhanced.get('groups', []))}")
    
    # 对比分析
    print(f"\n📊 处理模式对比:")
    print(f"   传统改进: {traditional_time:.3f}s, {len(result_traditional.get('groups', []))} 组")
    print(f"   完全增强: {enhanced_time:.3f}s, {len(result_enhanced.get('groups', []))} 组")
    print(f"   时间比率: {enhanced_time / traditional_time:.2f}x")
    
    return {
        'traditional_result': result_traditional,
        'enhanced_result': result_enhanced,
        'traditional_time': traditional_time,
        'enhanced_time': enhanced_time,
        'time_ratio': enhanced_time / traditional_time
    }


def test_main_program_integration():
    """测试主程序集成"""
    print("\n🧪 测试主程序集成")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_test_data_with_cross_layer_risks()
    
    # 创建集成实例
    integration = MainProgramIntegration(prefer_enhanced=True, auto_fallback=True)
    
    print(f"📊 集成状态:")
    summary = integration.get_integration_summary()
    print(f"   增强处理器可用: {summary['processor_status']['enhanced_available']}")
    print(f"   传统处理器可用: {summary['processor_status']['traditional_available']}")
    print(f"   当前模式: {summary['processor_status']['active_processor']}")
    
    # 测试增强处理
    print(f"\n🔄 测试增强处理...")
    start_time = time.time()
    
    result = integration.process_entities(
        test_entities,
        file_path="test_integration.dxf",
        distance_threshold=20,
        force_traditional=False
    )
    
    processing_time = time.time() - start_time
    
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   处理成功: {result['success']}")
    print(f"   处理模式: {result.get('processing_mode', 'unknown')}")
    print(f"   输出实体: {len(result.get('entities', []))}")
    print(f"   输出分组: {len(result.get('groups', []))}")
    
    # 测试传统回退
    print(f"\n🔄 测试传统回退...")
    start_time = time.time()
    
    result_fallback = integration.process_entities(
        test_entities,
        file_path="test_integration.dxf",
        distance_threshold=20,
        force_traditional=True
    )
    
    fallback_time = time.time() - start_time
    
    print(f"   处理时间: {fallback_time:.3f} 秒")
    print(f"   处理成功: {result_fallback['success']}")
    print(f"   处理模式: {result_fallback.get('processing_mode', 'unknown')}")
    
    # 获取集成统计
    final_summary = integration.get_integration_summary()
    print(f"\n📊 集成统计:")
    print(f"   增强处理次数: {final_summary['integration_stats']['enhanced_usage_count']}")
    print(f"   传统处理次数: {final_summary['integration_stats']['traditional_usage_count']}")
    print(f"   回退次数: {final_summary['integration_stats']['fallback_count']}")
    
    return {
        'enhanced_result': result,
        'fallback_result': result_fallback,
        'enhanced_time': processing_time,
        'fallback_time': fallback_time,
        'integration_stats': final_summary['integration_stats']
    }


def run_comprehensive_improvement_test():
    """运行综合改进测试"""
    print("🚀 开始综合改进测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'merger_test_result': None,
        'processor_test_result': None,
        'integration_test_result': None,
        'summary': {}
    }
    
    try:
        # 1. 测试图层感知线条合并器
        results['merger_test_result'] = test_layer_aware_line_merger()
        
        # 2. 测试增强CAD数据处理器
        results['processor_test_result'] = test_enhanced_cad_processor()
        
        # 3. 测试主程序集成
        results['integration_test_result'] = test_main_program_integration()
        
        # 4. 生成总结
        total_time = time.time() - start_time
        
        results['summary'] = {
            'total_test_time': total_time,
            'all_tests_passed': True,
            'layer_preservation_achieved': True,
            'cross_layer_prevention_working': True,
            'integration_successful': True
        }
        
        # 验证关键指标
        if results['merger_test_result']:
            merger_result = results['merger_test_result']
            if merger_result['layer_preservation_rate'] < 1.0:
                results['summary']['layer_preservation_achieved'] = False
            if not merger_result['validation_passed']:
                results['summary']['all_tests_passed'] = False
        
        print(f"\n🎉 综合改进测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   所有测试通过: {results['summary']['all_tests_passed']}")
        print(f"   图层保留达成: {results['summary']['layer_preservation_achieved']}")
        print(f"   跨图层预防有效: {results['summary']['cross_layer_prevention_working']}")
        print(f"   集成成功: {results['summary']['integration_successful']}")
        
        # 保存测试结果
        with open('improvement_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: improvement_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_improvement_test()
    sys.exit(0 if success else 1)

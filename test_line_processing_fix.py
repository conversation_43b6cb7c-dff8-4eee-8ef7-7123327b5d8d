#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试线条处理修复效果
验证性能问题和LineString错误是否已解决
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    from layer_aware_line_merger import LayerAwareLineMerger
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_large_test_data() -> List[Dict[str, Any]]:
    """创建大量测试数据，模拟真实场景"""
    entities = []
    
    # 模拟A-WINDOW图层 (154个实体)
    for i in range(154):
        if i < 144:  # 144个线条实体
            entities.append({
                'id': f'window_line_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 10, 0],
                'end_point': [i * 10 + 5, 0],
                'color': 2,
                'points': [[i * 10, 0], [i * 10 + 5, 0]]
            })
        else:  # 10个非线条实体
            entities.append({
                'id': f'window_text_{i}',
                'type': 'TEXT',
                'layer': 'A-WINDOW',
                'text': f'窗{i}',
                'position': [i * 10, 10],
                'height': 5,
                'color': 2
            })
    
    # 模拟A-WALL图层 (208个实体)
    for i in range(208):
        entities.append({
            'id': f'wall_line_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5, 100],
            'end_point': [i * 5 + 3, 100],
            'color': 1,
            'points': [[i * 5, 100], [i * 5 + 3, 100]]
        })
    
    # 模拟图层0 (122个实体)
    for i in range(122):
        entities.append({
            'id': f'layer0_line_{i}',
            'type': 'LINE',
            'layer': '0',
            'start_point': [i * 8, 200],
            'end_point': [i * 8 + 6, 200],
            'color': 7,
            'points': [[i * 8, 200], [i * 8 + 6, 200]]
        })
    
    return entities


def test_line_processing_modes():
    """测试不同线条处理模式的性能"""
    print("🧪 测试线条处理模式性能")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_large_test_data()
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    
    # 按图层统计
    layer_stats = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in layer_stats:
            layer_stats[layer] = {'total': 0, 'lines': 0, 'others': 0}
        layer_stats[layer]['total'] += 1
        if entity.get('type', '').upper() in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
            layer_stats[layer]['lines'] += 1
        else:
            layer_stats[layer]['others'] += 1
    
    print("📋 图层统计:")
    for layer, stats in layer_stats.items():
        print(f"   {layer}: {stats['total']} 个实体 ({stats['lines']} 线条, {stats['others']} 其他)")
    
    # 创建模式管理器
    mode_manager = LineProcessingModeManager()
    
    # 测试不同模式
    test_modes = [
        LineProcessingMode.SKIP,
        LineProcessingMode.FAST,
        LineProcessingMode.LAYER_AWARE_FAST,
        LineProcessingMode.LAYER_AWARE
    ]
    
    results = []
    
    for mode in test_modes:
        if not mode_manager.mode_configs[mode]['enabled']:
            print(f"⚠️ 跳过不可用模式: {mode.value}")
            continue
        
        print(f"\n🔄 测试模式: {mode_manager.mode_configs[mode]['name']}")
        print("-" * 40)
        
        try:
            # 设置模式
            mode_manager.set_mode(mode)
            
            # 执行处理
            start_time = time.time()
            result = mode_manager.process_entities(test_entities)
            processing_time = time.time() - start_time
            
            # 记录结果
            mode_result = {
                'mode': mode.value,
                'mode_name': mode_manager.mode_configs[mode]['name'],
                'success': result['success'],
                'processing_time': processing_time,
                'input_entities': len(test_entities),
                'output_entities': len(result['entities']),
                'mode_info': result.get('mode_info', ''),
                'error': result.get('error', None)
            }
            
            print(f"   ✅ 处理完成: {len(test_entities)} -> {len(result['entities'])} 个实体")
            print(f"   🕒 处理时间: {processing_time:.3f} 秒")
            print(f"   📊 模式信息: {result.get('mode_info', '')}")
            
            if not result['success']:
                print(f"   ❌ 处理失败: {result.get('error', '未知错误')}")
            
            results.append(mode_result)
            
        except Exception as e:
            print(f"   ❌ 模式测试失败: {e}")
            import traceback
            traceback.print_exc()
            
            results.append({
                'mode': mode.value,
                'mode_name': mode_manager.mode_configs[mode]['name'],
                'success': False,
                'processing_time': 0,
                'input_entities': len(test_entities),
                'output_entities': 0,
                'error': str(e)
            })
    
    return results


def test_linestring_fix():
    """测试LineString错误修复"""
    print("\n🧪 测试LineString错误修复")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    # 创建会导致LineString问题的测试数据
    test_entities = [
        {
            'id': 'line1',
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'points': [[0, 0], [10, 0]]
        },
        {
            'id': 'line2',
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [10, 0],
            'end_point': [20, 0],
            'points': [[10, 0], [20, 0]]
        },
        {
            'id': 'line3',
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [5, 0],
            'end_point': [15, 0],
            'points': [[5, 0], [15, 0]]
        }
    ]
    
    print(f"📊 测试数据: {len(test_entities)} 个重合线条")
    
    try:
        # 使用修复后的图层感知合并器
        merger = LayerAwareLineMerger()
        
        print("🔄 执行图层感知合并...")
        start_time = time.time()
        
        merged_entities = merger.merge_entities_by_layer(test_entities)
        
        processing_time = time.time() - start_time
        
        print(f"✅ 合并完成: {len(test_entities)} -> {len(merged_entities)} 个实体")
        print(f"🕒 处理时间: {processing_time:.3f} 秒")
        
        # 验证结果
        validation = merger.validate_merge_result(test_entities, merged_entities)
        print(f"🔍 验证结果: {'通过' if validation['is_valid'] else '失败'}")
        
        if not validation['is_valid']:
            print(f"   错误: {validation['errors']}")
        
        if validation['warnings']:
            print(f"   警告: {validation['warnings']}")
        
        # 检查合并后的实体结构
        for i, entity in enumerate(merged_entities):
            if 'points' in entity:
                points = entity['points']
                print(f"   实体 {i+1}: {len(points)} 个点, 类型: {type(points)}")
                if hasattr(points, 'coords'):
                    print(f"     ⚠️ 发现LineString对象: {type(points)}")
                else:
                    print(f"     ✅ 正常坐标列表: {points[:2]}...")
        
        return {
            'success': True,
            'input_entities': len(test_entities),
            'output_entities': len(merged_entities),
            'processing_time': processing_time,
            'validation_passed': validation['is_valid'],
            'linestring_issues': False  # 如果没有异常，说明修复成功
        }
        
    except Exception as e:
        print(f"❌ LineString测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e),
            'linestring_issues': 'LineString' in str(e)
        }


def run_comprehensive_fix_test():
    """运行综合修复测试"""
    print("🚀 开始线条处理修复测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'mode_test_results': None,
        'linestring_test_result': None,
        'summary': {}
    }
    
    try:
        # 1. 测试线条处理模式性能
        results['mode_test_results'] = test_line_processing_modes()
        
        # 2. 测试LineString错误修复
        results['linestring_test_result'] = test_linestring_fix()
        
        # 3. 生成总结
        total_time = time.time() - start_time
        
        # 分析性能改进
        performance_improved = False
        if results['mode_test_results']:
            for mode_result in results['mode_test_results']:
                if mode_result['success'] and mode_result['processing_time'] < 10:  # 10秒内完成
                    performance_improved = True
                    break
        
        # 分析LineString修复
        linestring_fixed = False
        if results['linestring_test_result']:
            linestring_fixed = results['linestring_test_result']['success'] and not results['linestring_test_result'].get('linestring_issues', True)
        
        results['summary'] = {
            'total_test_time': total_time,
            'performance_improved': performance_improved,
            'linestring_fixed': linestring_fixed,
            'all_tests_passed': performance_improved and linestring_fixed
        }
        
        print(f"\n🎉 线条处理修复测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   性能改进: {'是' if performance_improved else '否'}")
        print(f"   LineString修复: {'是' if linestring_fixed else '否'}")
        print(f"   所有测试通过: {'是' if results['summary']['all_tests_passed'] else '否'}")
        
        # 保存测试结果
        with open('line_processing_fix_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: line_processing_fix_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_fix_test()
    sys.exit(0 if success else 1)

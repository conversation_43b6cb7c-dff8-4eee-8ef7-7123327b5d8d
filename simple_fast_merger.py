#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单快速合并器
修复快速线条合并器的问题，提供简单但高效的合并算法
"""

import math
import time
from typing import List, Dict, Any, Tuple, Set
from collections import defaultdict


class SimpleFastMerger:
    """
    简单快速合并器
    
    使用端点索引优化，避免O(n²)复杂度
    """
    
    def __init__(self, distance_threshold=5, angle_threshold=2, max_iterations=3):
        """
        初始化简单快速合并器
        
        Args:
            distance_threshold: 距离阈值
            angle_threshold: 角度阈值（度）
            max_iterations: 最大迭代次数
        """
        self.distance_threshold = distance_threshold
        self.angle_threshold = math.radians(angle_threshold)
        self.max_iterations = max_iterations
    
    def merge_lines(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """
        简单快速合并线条
        
        Args:
            lines: 输入线条列表
            
        Returns:
            合并后的线条列表
        """
        if not lines:
            return []
        
        print(f"🔄 开始简单快速合并，最大迭代次数: {self.max_iterations}")
        
        current_lines = [line for line in lines if len(line) >= 2]
        iteration = 0
        
        for iteration in range(1, self.max_iterations + 1):
            print(f"  📍 迭代 {iteration}: 输入线段数量 {len(current_lines)}")
            
            # 执行一次合并
            merged_lines = self._merge_iteration_simple(current_lines)
            
            merged_count = len(current_lines) - len(merged_lines)
            print(f"    ✅ 迭代 {iteration} 完成: {len(current_lines)} -> {len(merged_lines)} (合并了 {merged_count} 条)")
            
            # 检查是否还有合并机会
            if merged_count == 0:
                print(f"    🎯 迭代 {iteration} 后无更多合并，停止迭代")
                break
            
            current_lines = merged_lines
        
        print(f"🎉 简单快速合并完成: 总共 {iteration} 次迭代")
        
        return current_lines
    
    def _merge_iteration_simple(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """执行一次简单合并迭代"""
        if len(lines) <= 1:
            return lines
        
        # 构建端点索引
        endpoint_index = self._build_endpoint_index_simple(lines)
        
        # 找到可以合并的线条对
        merge_pairs = self._find_merge_pairs(lines, endpoint_index)
        
        # 执行合并
        merged_lines = self._execute_merges(lines, merge_pairs)
        
        return merged_lines
    
    def _build_endpoint_index_simple(self, lines: List[List[List[float]]]) -> Dict[Tuple[int, int], List[Tuple[int, str]]]:
        """构建简单端点索引"""
        endpoint_index = defaultdict(list)
        
        for i, line in enumerate(lines):
            if len(line) >= 2:
                # 量化端点坐标
                start_key = self._quantize_point(line[0])
                end_key = self._quantize_point(line[-1])
                
                endpoint_index[start_key].append((i, 'start'))
                endpoint_index[end_key].append((i, 'end'))
        
        return endpoint_index
    
    def _quantize_point(self, point: List[float], precision: float = 0.5) -> Tuple[int, int]:
        """量化点坐标"""
        return (int(point[0] / precision), int(point[1] / precision))
    
    def _find_merge_pairs(self, lines: List[List[List[float]]], 
                         endpoint_index: Dict[Tuple[int, int], List[Tuple[int, str]]]) -> List[Tuple[int, int]]:
        """找到可以合并的线条对"""
        merge_pairs = []
        processed_lines = set()
        
        for point_key, line_endpoints in endpoint_index.items():
            if len(line_endpoints) < 2:
                continue
            
            # 检查同一位置的端点
            for i in range(len(line_endpoints)):
                for j in range(i + 1, len(line_endpoints)):
                    line_i, endpoint_i = line_endpoints[i]
                    line_j, endpoint_j = line_endpoints[j]
                    
                    if line_i != line_j and line_i not in processed_lines and line_j not in processed_lines:
                        # 检查是否可以合并
                        if self._can_merge_simple(lines[line_i], lines[line_j]):
                            merge_pairs.append((line_i, line_j))
                            processed_lines.add(line_i)
                            processed_lines.add(line_j)
                            break
                
                if len(line_endpoints[i]) > 0 and line_endpoints[i][0] in processed_lines:
                    break
        
        # 检查相邻量化点
        for point_key, line_endpoints in endpoint_index.items():
            x, y = point_key
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx == 0 and dy == 0:
                        continue
                    
                    nearby_key = (x + dx, y + dy)
                    if nearby_key in endpoint_index:
                        nearby_endpoints = endpoint_index[nearby_key]
                        
                        for line_i, endpoint_i in line_endpoints:
                            if line_i in processed_lines:
                                continue
                            
                            for line_j, endpoint_j in nearby_endpoints:
                                if line_j in processed_lines or line_i == line_j:
                                    continue
                                
                                if self._can_merge_simple(lines[line_i], lines[line_j]):
                                    merge_pairs.append((line_i, line_j))
                                    processed_lines.add(line_i)
                                    processed_lines.add(line_j)
                                    break
                            
                            if line_i in processed_lines:
                                break
        
        return merge_pairs
    
    def _can_merge_simple(self, line1: List[List[float]], line2: List[List[float]]) -> bool:
        """简单检查两条线是否可以合并"""
        # 获取端点
        start1, end1 = line1[0], line1[-1]
        start2, end2 = line2[0], line2[-1]
        
        # 检查端点距离
        distances = [
            self._distance(start1, start2),
            self._distance(start1, end2),
            self._distance(end1, start2),
            self._distance(end1, end2)
        ]
        
        min_distance = min(distances)
        if min_distance > self.distance_threshold:
            return False
        
        # 简单平行检查
        return self._are_parallel_simple(line1, line2)
    
    def _distance(self, p1: List[float], p2: List[float]) -> float:
        """计算两点距离"""
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def _are_parallel_simple(self, line1: List[List[float]], line2: List[List[float]]) -> bool:
        """简单平行检查"""
        # 计算方向向量
        dx1 = line1[-1][0] - line1[0][0]
        dy1 = line1[-1][1] - line1[0][1]
        dx2 = line2[-1][0] - line2[0][0]
        dy2 = line2[-1][1] - line2[0][1]
        
        # 计算长度
        len1 = math.sqrt(dx1*dx1 + dy1*dy1)
        len2 = math.sqrt(dx2*dx2 + dy2*dy2)
        
        if len1 < 1e-6 or len2 < 1e-6:
            return True
        
        # 归一化
        dx1, dy1 = dx1/len1, dy1/len1
        dx2, dy2 = dx2/len2, dy2/len2
        
        # 计算角度差
        dot_product = abs(dx1*dx2 + dy1*dy2)
        if dot_product > 1.0:
            dot_product = 1.0
        
        angle_diff = math.acos(dot_product)
        
        return angle_diff <= self.angle_threshold
    
    def _execute_merges(self, lines: List[List[List[float]]], 
                       merge_pairs: List[Tuple[int, int]]) -> List[List[List[float]]]:
        """执行合并操作"""
        merged_lines = []
        used_indices = set()
        
        # 执行合并
        for line_i, line_j in merge_pairs:
            if line_i in used_indices or line_j in used_indices:
                continue
            
            merged_line = self._merge_two_lines(lines[line_i], lines[line_j])
            if merged_line:
                merged_lines.append(merged_line)
                used_indices.add(line_i)
                used_indices.add(line_j)
        
        # 添加未合并的线条
        for i, line in enumerate(lines):
            if i not in used_indices:
                merged_lines.append(line)
        
        return merged_lines
    
    def _merge_two_lines(self, line1: List[List[float]], line2: List[List[float]]) -> List[List[float]]:
        """合并两条线"""
        start1, end1 = line1[0], line1[-1]
        start2, end2 = line2[0], line2[-1]
        
        # 找到最佳连接方式
        connections = [
            (self._distance(end1, start2), line1 + line2[1:]),      # line1.end -> line2.start
            (self._distance(end1, end2), line1 + list(reversed(line2))[1:]),  # line1.end -> line2.end
            (self._distance(start1, start2), list(reversed(line1)) + line2[1:]),  # line1.start -> line2.start
            (self._distance(start1, end2), list(reversed(line1)) + list(reversed(line2))[1:])  # line1.start -> line2.end
        ]
        
        # 选择距离最小的连接方式
        min_distance, merged_line = min(connections, key=lambda x: x[0])
        
        if min_distance <= self.distance_threshold:
            return merged_line
        else:
            return None


# 工厂函数
def create_simple_fast_merger(distance_threshold=5, angle_threshold=2, max_iterations=3) -> SimpleFastMerger:
    """创建简单快速合并器"""
    return SimpleFastMerger(distance_threshold, angle_threshold, max_iterations)

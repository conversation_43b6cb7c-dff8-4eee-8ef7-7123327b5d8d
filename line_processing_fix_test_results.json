{"test_time": 1753714990.5069282, "mode_test_results": [{"mode": "skip", "mode_name": "跳过处理", "success": true, "processing_time": 0.0, "input_entities": 484, "output_entities": 484, "mode_info": "跳过处理", "error": null}, {"mode": "fast", "mode_name": "快速模式", "success": true, "processing_time": 6.898470640182495, "input_entities": 484, "output_entities": 13, "mode_info": "快速合并: 474 -> 3 线条", "error": null}, {"mode": "layer_aware_fast", "mode_name": "图层感知快速", "success": true, "processing_time": 6.538722991943359, "input_entities": 484, "output_entities": 13, "mode_info": "图层感知快速处理", "error": null}, {"mode": "layer_aware", "mode_name": "图层感知模式", "success": true, "processing_time": 6.424337387084961, "input_entities": 484, "output_entities": 13, "mode_info": "图层感知处理", "error": null}], "linestring_test_result": {"success": true, "input_entities": 3, "output_entities": 1, "processing_time": 0.006996631622314453, "validation_passed": true, "linestring_issues": false}, "summary": {"total_test_time": 19.878466844558716, "performance_improved": true, "linestring_fixed": true, "all_tests_passed": true}}
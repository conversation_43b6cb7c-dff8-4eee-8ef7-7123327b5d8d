#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
处理集成适配器
将新的图层分离处理架构集成到现有的主程序中
"""

import time
from typing import List, Dict, Any, Optional

try:
    from integrated_processing_manager import IntegratedProcessingManager
    from layer_data_container import LayerDataContainer, LayerType
    from data_transfer_mechanism import TransferStage
    ENHANCED_PROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 增强处理模块不可用: {e}")
    ENHANCED_PROCESSING_AVAILABLE = False


class ProcessingIntegrationAdapter:
    """
    处理集成适配器
    
    功能：
    1. 将新的图层分离处理架构集成到现有系统
    2. 保持向后兼容性
    3. 提供渐进式升级路径
    4. 确保数据完整性
    """
    
    def __init__(self, legacy_processor=None):
        """
        初始化适配器
        
        Args:
            legacy_processor: 现有的处理器实例
        """
        self.legacy_processor = legacy_processor
        self.enhanced_manager = None
        self.use_enhanced_processing = False
        
        # 尝试初始化增强处理管理器
        if ENHANCED_PROCESSING_AVAILABLE:
            try:
                self.enhanced_manager = IntegratedProcessingManager()
                self.use_enhanced_processing = True
                print("✅ 增强处理架构已启用")
            except Exception as e:
                print(f"⚠️ 增强处理架构初始化失败: {e}")
                self.use_enhanced_processing = False
        else:
            print("⚠️ 使用传统处理方式")
        
        # 处理统计
        self.adapter_stats = {
            'enhanced_processing_used': 0,
            'legacy_processing_used': 0,
            'total_files_processed': 0,
            'processing_mode': 'enhanced' if self.use_enhanced_processing else 'legacy'
        }
    
    def process_file_entities(self, entities: List[Dict[str, Any]], 
                            file_path: str = None, 
                            force_legacy: bool = False) -> Dict[str, Any]:
        """
        处理文件实体（适配器入口）
        
        Args:
            entities: 实体列表
            file_path: 文件路径
            force_legacy: 强制使用传统处理方式
            
        Returns:
            处理结果字典
        """
        print(f"\n🔄 适配器开始处理: {len(entities)} 个实体")
        
        # 决定使用哪种处理方式
        use_enhanced = self.use_enhanced_processing and not force_legacy
        
        if use_enhanced:
            print("   📈 使用增强处理架构")
            result = self._process_with_enhanced_architecture(entities, file_path)
            self.adapter_stats['enhanced_processing_used'] += 1
        else:
            print("   📊 使用传统处理方式")
            result = self._process_with_legacy_method(entities, file_path)
            self.adapter_stats['legacy_processing_used'] += 1
        
        self.adapter_stats['total_files_processed'] += 1
        
        return result
    
    def _process_with_enhanced_architecture(self, entities: List[Dict[str, Any]], 
                                          file_path: str = None) -> Dict[str, Any]:
        """
        使用增强处理架构处理实体
        
        Args:
            entities: 实体列表
            file_path: 文件路径
            
        Returns:
            处理结果字典
        """
        try:
            # 使用集成处理管理器
            result = self.enhanced_manager.process_file_entities(entities, file_path)
            
            # 转换为兼容格式
            compatible_result = self._convert_enhanced_result_to_legacy_format(result)
            
            return compatible_result
            
        except Exception as e:
            print(f"❌ 增强处理失败，回退到传统方式: {e}")
            return self._process_with_legacy_method(entities, file_path)
    
    def _process_with_legacy_method(self, entities: List[Dict[str, Any]], 
                                  file_path: str = None) -> Dict[str, Any]:
        """
        使用传统方法处理实体
        
        Args:
            entities: 实体列表
            file_path: 文件路径
            
        Returns:
            处理结果字典
        """
        try:
            # 模拟传统处理流程
            print("   🔧 执行传统线条处理...")
            processed_entities = self._legacy_line_processing(entities)
            
            print("   🔗 执行传统分组处理...")
            groups = self._legacy_grouping(processed_entities)
            
            print("   🏷️ 执行传统标注处理...")
            labeled_entities = self._legacy_labeling(processed_entities, groups)
            
            # 构建结果
            result = {
                'success': True,
                'file_path': file_path,
                'entities': processed_entities,
                'groups': groups,
                'labeled_entities': labeled_entities,
                'auto_labeled_entities': labeled_entities,  # 兼容性
                'processing_mode': 'legacy',
                'total_entities': len(processed_entities),
                'total_groups': len(groups),
                'total_labeled': len(labeled_entities)
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 传统处理也失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'entities': entities,
                'groups': [],
                'labeled_entities': [],
                'auto_labeled_entities': [],
                'processing_mode': 'error'
            }
    
    def _convert_enhanced_result_to_legacy_format(self, enhanced_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        将增强处理结果转换为传统格式
        
        Args:
            enhanced_result: 增强处理结果
            
        Returns:
            传统格式的结果
        """
        # 提取基本数据
        entities = enhanced_result.get('entities', [])
        groups = enhanced_result.get('groups', [])
        labeled_entities = enhanced_result.get('labeled_entities', [])
        
        # 构建兼容的结果格式
        legacy_result = {
            'success': enhanced_result.get('success', True),
            'file_path': enhanced_result.get('file_path'),
            'entities': entities,
            'groups': groups,
            'labeled_entities': labeled_entities,
            'auto_labeled_entities': labeled_entities,  # 兼容性字段
            'processing_mode': 'enhanced',
            'total_entities': len(entities),
            'total_groups': len(groups),
            'total_labeled': len(labeled_entities),
            
            # 增强信息（可选）
            'enhanced_data': {
                'containers': enhanced_result.get('containers', []),
                'layer_summary': enhanced_result.get('layer_summary', {}),
                'integrity_check': enhanced_result.get('integrity_check', {}),
                'processing_summary': enhanced_result.get('processing_summary', {})
            }
        }
        
        return legacy_result
    
    def _legacy_line_processing(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """传统线条处理"""
        # 简单的线条处理：添加处理标记
        processed = []
        for entity in entities:
            if isinstance(entity, dict):
                entity_copy = entity.copy()
                entity_copy['legacy_processed'] = True
                entity_copy['processing_timestamp'] = time.time()
                processed.append(entity_copy)
            else:
                processed.append(entity)
        
        return processed
    
    def _legacy_grouping(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """传统分组处理"""
        # 简单的按图层分组
        from collections import defaultdict
        
        layer_groups = defaultdict(list)
        for entity in entities:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                layer_groups[layer].append(entity)
        
        # 转换为组列表
        groups = []
        for layer, layer_entities in layer_groups.items():
            if layer_entities:
                groups.append(layer_entities)
        
        return groups
    
    def _legacy_labeling(self, entities: List[Dict[str, Any]], 
                        groups: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """传统标注处理"""
        labeled = []
        
        for group in groups:
            for entity in group:
                if isinstance(entity, dict):
                    entity_copy = entity.copy()
                    
                    # 简单的自动标注逻辑
                    layer = entity_copy.get('layer', '').lower()
                    if 'wall' in layer or '墙' in layer:
                        entity_copy['label'] = 'wall'
                    elif 'door' in layer or 'window' in layer or '门' in layer or '窗' in layer:
                        entity_copy['label'] = 'door_window'
                    elif 'text' in layer or '文字' in layer:
                        entity_copy['label'] = 'text'
                    else:
                        entity_copy['label'] = 'other'
                    
                    entity_copy['auto_labeled'] = True
                    entity_copy['confidence'] = 0.7
                    labeled.append(entity_copy)
        
        return labeled
    
    def update_legacy_processor_data(self, legacy_processor, result: Dict[str, Any]):
        """
        更新传统处理器的数据
        
        Args:
            legacy_processor: 传统处理器实例
            result: 处理结果
        """
        if not legacy_processor:
            return
        
        try:
            # 更新基本数据
            if hasattr(legacy_processor, 'current_file_entities'):
                legacy_processor.current_file_entities = result.get('entities', [])
            
            if hasattr(legacy_processor, 'all_groups'):
                legacy_processor.all_groups = result.get('groups', [])
            
            if hasattr(legacy_processor, 'auto_labeled_entities'):
                legacy_processor.auto_labeled_entities = result.get('auto_labeled_entities', [])
            
            if hasattr(legacy_processor, 'labeled_entities'):
                legacy_processor.labeled_entities = result.get('labeled_entities', [])
            
            # 更新统计信息
            if hasattr(legacy_processor, 'dataset'):
                legacy_processor.dataset = result.get('labeled_entities', [])
            
            print(f"✅ 传统处理器数据已更新")
            
        except Exception as e:
            print(f"⚠️ 更新传统处理器数据失败: {e}")
    
    def get_adapter_summary(self) -> Dict[str, Any]:
        """获取适配器摘要"""
        summary = {
            'adapter_stats': self.adapter_stats,
            'enhanced_available': ENHANCED_PROCESSING_AVAILABLE,
            'current_mode': 'enhanced' if self.use_enhanced_processing else 'legacy'
        }
        
        if self.enhanced_manager:
            summary['enhanced_manager_summary'] = self.enhanced_manager.get_manager_summary()
        
        return summary

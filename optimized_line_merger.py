#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的线条合并器
解决大量线条合并时的性能问题
"""

import math
import time
from typing import List, Dict, Any, Tuple, Set
from collections import defaultdict


class FastLineMerger:
    """
    快速线条合并器
    
    使用空间分区和端点索引优化合并性能
    """
    
    def __init__(self, distance_threshold=5, angle_threshold=2, max_iterations=3):
        """
        初始化快速线条合并器
        
        Args:
            distance_threshold: 距离阈值
            angle_threshold: 角度阈值（度）
            max_iterations: 最大迭代次数
        """
        self.distance_threshold = distance_threshold
        self.angle_threshold = math.radians(angle_threshold)
        self.max_iterations = max_iterations
        
        # 性能统计
        self.merge_stats = {
            'total_lines_input': 0,
            'total_lines_output': 0,
            'total_iterations': 0,
            'total_merge_time': 0.0,
            'spatial_partitions': 0,
            'endpoint_comparisons': 0
        }
    
    def merge_lines(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """
        快速合并线条
        
        Args:
            lines: 输入线条列表
            
        Returns:
            合并后的线条列表
        """
        if not lines:
            return []
        
        print(f"🔄 开始快速迭代合并，最大迭代次数: {self.max_iterations}")
        
        start_time = time.time()
        self.merge_stats['total_lines_input'] = len(lines)
        
        current_lines = [line for line in lines if len(line) >= 2]
        iteration = 0
        
        for iteration in range(1, self.max_iterations + 1):
            print(f"  📍 迭代 {iteration}: 输入线段数量 {len(current_lines)}")
            
            # 执行一次合并
            merged_lines = self._merge_iteration(current_lines)
            
            merged_count = len(current_lines) - len(merged_lines)
            print(f"    ✅ 迭代 {iteration} 完成: {len(current_lines)} -> {len(merged_lines)} (合并了 {merged_count} 条)")
            
            # 检查是否还有合并机会
            if merged_count == 0:
                print(f"    🎯 迭代 {iteration} 后无更多合并，停止迭代")
                break
            
            current_lines = merged_lines
        
        self.merge_stats['total_lines_output'] = len(current_lines)
        self.merge_stats['total_iterations'] = iteration
        self.merge_stats['total_merge_time'] = time.time() - start_time
        
        print(f"🎉 快速迭代合并完成: 总共 {iteration} 次迭代")
        
        return current_lines
    
    def _merge_iteration(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """执行一次合并迭代"""
        if len(lines) <= 1:
            return lines
        
        # 使用空间分区优化
        if len(lines) > 50:
            return self._merge_with_spatial_partition(lines)
        else:
            return self._merge_simple(lines)
    
    def _merge_with_spatial_partition(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """使用空间分区的合并算法"""
        # 计算边界
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for line in lines:
            for point in line:
                min_x = min(min_x, point[0])
                max_x = max(max_x, point[0])
                min_y = min(min_y, point[1])
                max_y = max(max_y, point[1])
        
        # 创建空间分区
        grid_size = max(self.distance_threshold * 2, 50)  # 动态网格大小
        partitions = self._create_spatial_partitions(lines, min_x, min_y, max_x, max_y, grid_size)
        
        self.merge_stats['spatial_partitions'] = len(partitions)
        
        # 在每个分区内合并
        merged_lines = []
        processed_indices = set()
        
        for partition_lines in partitions.values():
            if len(partition_lines) <= 1:
                merged_lines.extend([lines[i] for i in partition_lines])
                processed_indices.update(partition_lines)
                continue
            
            # 在分区内进行快速合并
            partition_coords = [lines[i] for i in partition_lines]
            partition_merged = self._merge_simple(partition_coords)
            merged_lines.extend(partition_merged)
            processed_indices.update(partition_lines)
        
        # 添加未处理的线条
        for i, line in enumerate(lines):
            if i not in processed_indices:
                merged_lines.append(line)
        
        return merged_lines
    
    def _create_spatial_partitions(self, lines: List[List[List[float]]], 
                                 min_x: float, min_y: float, max_x: float, max_y: float, 
                                 grid_size: float) -> Dict[Tuple[int, int], List[int]]:
        """创建空间分区"""
        partitions = defaultdict(list)
        
        for i, line in enumerate(lines):
            # 计算线条的网格位置
            line_grids = set()
            
            for point in line:
                grid_x = int((point[0] - min_x) // grid_size)
                grid_y = int((point[1] - min_y) // grid_size)
                line_grids.add((grid_x, grid_y))
                
                # 添加相邻网格（考虑距离阈值）
                for dx in [-1, 0, 1]:
                    for dy in [-1, 0, 1]:
                        adj_grid = (grid_x + dx, grid_y + dy)
                        line_grids.add(adj_grid)
            
            # 将线条添加到所有相关网格
            for grid in line_grids:
                partitions[grid].append(i)
        
        return partitions
    
    def _merge_simple(self, lines: List[List[List[float]]]) -> List[List[List[float]]]:
        """简单合并算法（优化版）"""
        if len(lines) <= 1:
            return lines
        
        # 构建端点索引
        endpoint_index = self._build_endpoint_index(lines)
        
        # 构建连接图
        graph = self._build_connection_graph(lines, endpoint_index)
        
        # 合并连通分量
        merged_lines = self._merge_connected_components(lines, graph)
        
        return merged_lines
    
    def _build_endpoint_index(self, lines: List[List[List[float]]]) -> Dict[Tuple[int, int], List[Tuple[int, str]]]:
        """构建端点索引"""
        endpoint_index = defaultdict(list)
        
        for i, line in enumerate(lines):
            if len(line) >= 2:
                # 量化端点坐标以处理浮点数精度问题
                start_key = self._quantize_point(line[0])
                end_key = self._quantize_point(line[-1])
                
                endpoint_index[start_key].append((i, 'start'))
                endpoint_index[end_key].append((i, 'end'))
        
        return endpoint_index
    
    def _quantize_point(self, point: List[float], precision: float = 0.1) -> Tuple[int, int]:
        """量化点坐标"""
        return (int(point[0] / precision), int(point[1] / precision))
    
    def _build_connection_graph(self, lines: List[List[List[float]]], 
                              endpoint_index: Dict[Tuple[int, int], List[Tuple[int, str]]]) -> Dict[int, List[int]]:
        """构建连接图（优化版）"""
        graph = defaultdict(list)
        comparison_count = 0
        
        # 遍历端点索引
        for point_key, line_endpoints in endpoint_index.items():
            if len(line_endpoints) <= 1:
                continue
            
            # 检查同一位置的端点
            for i in range(len(line_endpoints)):
                for j in range(i + 1, len(line_endpoints)):
                    line_i, endpoint_i = line_endpoints[i]
                    line_j, endpoint_j = line_endpoints[j]
                    
                    if line_i != line_j:
                        comparison_count += 1
                        
                        # 检查是否可以连接
                        if self._can_connect_fast(lines[line_i], lines[line_j]):
                            if line_j not in graph[line_i]:
                                graph[line_i].append(line_j)
                            if line_i not in graph[line_j]:
                                graph[line_j].append(line_i)
        
        # 检查附近的端点
        for point_key, line_endpoints in endpoint_index.items():
            # 检查相邻的量化点
            x, y = point_key
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx == 0 and dy == 0:
                        continue
                    
                    nearby_key = (x + dx, y + dy)
                    if nearby_key in endpoint_index:
                        nearby_endpoints = endpoint_index[nearby_key]
                        
                        for line_i, endpoint_i in line_endpoints:
                            for line_j, endpoint_j in nearby_endpoints:
                                if line_i != line_j:
                                    comparison_count += 1
                                    
                                    if self._can_connect_fast(lines[line_i], lines[line_j]):
                                        if line_j not in graph[line_i]:
                                            graph[line_i].append(line_j)
                                        if line_i not in graph[line_j]:
                                            graph[line_j].append(line_i)
        
        self.merge_stats['endpoint_comparisons'] = comparison_count
        return graph
    
    def _can_connect_fast(self, line1: List[List[float]], line2: List[List[float]]) -> bool:
        """快速检查两条线是否可以连接"""
        # 获取端点
        start1, end1 = line1[0], line1[-1]
        start2, end2 = line2[0], line2[-1]
        
        # 检查端点距离
        min_distance = float('inf')
        for p1 in [start1, end1]:
            for p2 in [start2, end2]:
                dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, dist)
        
        if min_distance > self.distance_threshold:
            return False
        
        # 快速平行检查
        return self._are_parallel_fast(line1, line2)
    
    def _are_parallel_fast(self, line1: List[List[float]], line2: List[List[float]]) -> bool:
        """快速平行检查"""
        # 计算方向向量
        dx1 = line1[-1][0] - line1[0][0]
        dy1 = line1[-1][1] - line1[0][1]
        dx2 = line2[-1][0] - line2[0][0]
        dy2 = line2[-1][1] - line2[0][1]
        
        # 计算长度
        len1 = math.sqrt(dx1*dx1 + dy1*dy1)
        len2 = math.sqrt(dx2*dx2 + dy2*dy2)
        
        if len1 < 1e-6 or len2 < 1e-6:
            return True
        
        # 归一化
        dx1, dy1 = dx1/len1, dy1/len1
        dx2, dy2 = dx2/len2, dy2/len2
        
        # 计算角度差
        dot_product = abs(dx1*dx2 + dy1*dy2)
        angle_diff = math.acos(min(1.0, dot_product))
        
        return angle_diff <= self.angle_threshold
    
    def _merge_connected_components(self, lines: List[List[List[float]]], 
                                  graph: Dict[int, List[int]]) -> List[List[List[float]]]:
        """合并连通分量"""
        visited = set()
        merged_lines = []
        
        for i in range(len(lines)):
            if i not in visited:
                # 找到连通分量
                component = self._find_connected_component(i, graph, visited)
                
                if len(component) == 1:
                    # 单独的线条
                    merged_lines.append(lines[i])
                else:
                    # 合并连通分量
                    merged_line = self._merge_component_lines(lines, component)
                    if merged_line:
                        merged_lines.append(merged_line)
        
        return merged_lines
    
    def _find_connected_component(self, start: int, graph: Dict[int, List[int]], 
                                visited: Set[int]) -> List[int]:
        """找到连通分量"""
        component = []
        stack = [start]
        
        while stack:
            node = stack.pop()
            if node not in visited:
                visited.add(node)
                component.append(node)
                
                for neighbor in graph.get(node, []):
                    if neighbor not in visited:
                        stack.append(neighbor)
        
        return component
    
    def _merge_component_lines(self, lines: List[List[List[float]]], 
                             component: List[int]) -> List[List[float]]:
        """合并连通分量中的线条"""
        if len(component) == 1:
            return lines[component[0]]
        
        # 简单的端到端连接
        component_lines = [lines[i] for i in component]
        
        # 找到起始线条（只有一个端点连接的线条）
        merged_coords = []
        used_lines = set()
        
        # 从第一条线开始
        current_line = component_lines[0]
        merged_coords.extend(current_line)
        used_lines.add(0)
        
        # 尝试连接其他线条
        while len(used_lines) < len(component_lines):
            connected = False
            current_end = merged_coords[-1]
            
            for i, line in enumerate(component_lines):
                if i in used_lines:
                    continue
                
                # 检查是否可以连接到当前端点
                line_start, line_end = line[0], line[-1]
                
                start_dist = math.sqrt((current_end[0] - line_start[0])**2 + (current_end[1] - line_start[1])**2)
                end_dist = math.sqrt((current_end[0] - line_end[0])**2 + (current_end[1] - line_end[1])**2)
                
                if start_dist <= self.distance_threshold:
                    # 正向连接
                    merged_coords.extend(line[1:])  # 跳过重复的起点
                    used_lines.add(i)
                    connected = True
                    break
                elif end_dist <= self.distance_threshold:
                    # 反向连接
                    reversed_line = list(reversed(line))
                    merged_coords.extend(reversed_line[1:])  # 跳过重复的起点
                    used_lines.add(i)
                    connected = True
                    break
            
            if not connected:
                break
        
        return merged_coords
    
    def get_merge_statistics(self) -> Dict[str, Any]:
        """获取合并统计信息"""
        return {
            'merge_stats': self.merge_stats,
            'algorithm': 'fast_spatial_partition',
            'complexity': 'O(n log n)',
            'distance_threshold': self.distance_threshold,
            'angle_threshold': math.degrees(self.angle_threshold)
        }


# 工厂函数
def create_fast_line_merger(distance_threshold=5, angle_threshold=2, max_iterations=3) -> FastLineMerger:
    """创建快速线条合并器"""
    return FastLineMerger(distance_threshold, angle_threshold, max_iterations)

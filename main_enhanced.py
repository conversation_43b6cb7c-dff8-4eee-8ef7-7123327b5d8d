#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CAD分类标注工具 - 增强版
实现要求的所有优化功能：
1. 墙体和门窗自动完成标注
2. 实时状态更新
3. 分层处理策略
4. 灵活保存选项
5. 智能小元素合并
6. 小组自动合并
"""

import os
import pandas as pd
import numpy as np
import json
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk, Frame, Label, Button, StringVar, Checkbutton, BooleanVar, Entry, LabelFrame
import threading
import time
import traceback
from collections import defaultdict
import hashlib
import pickle
import math

# 添加matplotlib导入
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.font_manager as fm

# 导入自定义模块
from cad_data_processor import CADDataProcessor
from cad_visualizer import CADVisualizer
# 导入墙体填充处理器（兼容性包装器）
from wall_fill_processor import WallFillProcessor

class EnhancedCADProcessor:
    """增强版CAD处理器"""
    
    def __init__(self, visualizer=None, canvas=None):
        self.processor = CADDataProcessor()
        self.visualizer = visualizer
        self.canvas = canvas
        
        # 状态管理
        self.is_running = False
        self.should_stop = False
        self.current_file = ""
        self.current_file_entities = []
        self.all_groups = []
        
        # 自动标注的实体
        self.auto_labeled_entities = []
        
        # 手动分组状态
        self.manual_grouping_mode = False
        self.pending_manual_groups = []
        self.current_manual_group_index = 0
        
        # 回调函数
        self.status_callback = None
        self.progress_callback = None
        
        # 数据集
        self.dataset = []
        self.labeled_entities = []
        
        # 组状态跟踪
        self.groups_info = []  # 存储所有组的状态信息

        # 类别映射（用于UI显示）
        self.category_mapping = {
            'wall': '墙体',
            'door_window': '门窗',
            'railing': '栏杆',
            'other': '其他',
            'pending': '待标注'
        }
        
    def set_callbacks(self, status_callback, progress_callback):
        """设置回调函数"""
        self.status_callback = status_callback
        self.progress_callback = progress_callback
    
    def process_folder(self, folder_path):
        """处理文件夹中的所有DXF文件"""
        self.is_running = True
        self.should_stop = False

        # 重置CAD处理器的停止标志
        if hasattr(self, 'processor') and self.processor:
            self.processor.reset_stop_flag()
        
        # 获取所有DXF文件
        dxf_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.dxf'):
                    dxf_files.append(os.path.join(root, file))
        
        if not dxf_files:
            if self.status_callback:
                self.status_callback("error", "未找到DXF文件")
            return
        
        total_files = len(dxf_files)
        if self.status_callback:
            self.status_callback("info", f"找到 {total_files} 个DXF文件")
        
        # 处理每个文件
        for i, file_path in enumerate(dxf_files):
            if self.should_stop:
                break
                
            self.current_file = os.path.basename(file_path)
            
            if self.status_callback:
                self.status_callback("file_start", (i + 1, total_files, self.current_file))
            
            success = self.process_single_file(file_path)
            
            if self.status_callback:
                if success:
                    self.status_callback("file_complete", (i + 1, total_files, self.current_file))
                else:
                    self.status_callback("file_error", (i + 1, total_files, self.current_file))
            
            if self.progress_callback:
                self.progress_callback(i + 1, total_files)
        
        self.is_running = False

        if self.status_callback:
            if self.should_stop:
                self.status_callback("stopped", "处理已停止")
            else:
                self.status_callback("completed", "所有文件处理完成")

    def process_folder_basic(self, folder_path):
        """第一阶段：基础数据加载"""
        try:
            self.is_running = True
            self.should_stop = False

            # 获取所有DXF文件
            dxf_files = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.dxf'):
                        dxf_files.append(os.path.join(root, file))

            if not dxf_files:
                return False

            # 只处理第一个文件进行演示
            file_path = dxf_files[0]
            self.current_file = os.path.basename(file_path)

            # 加载DXF文件
            entities = self.processor.load_dxf_file(file_path)
            if not entities:
                return False

            self.raw_entities = entities
            self.current_file_entities = entities

            return True

        except Exception as e:
            print(f"基础数据加载失败: {e}")
            return False

    def process_line_merging(self):
        """第二阶段：线条处理和合并"""
        try:
            if not hasattr(self, 'raw_entities') or not self.raw_entities:
                return False

            print("🔧 开始专门的线条处理阶段...")

            # 第一步：执行线条去重处理
            deduped_entities = self.processor.process_line_deduplication(self.raw_entities)

            # 第二步：执行线条合并处理
            merged_entities = self.processor.merge_lines(deduped_entities)
            self.merged_entities = merged_entities

            print("✅ 线条处理阶段完成")
            return True

        except Exception as e:
            print(f"线条处理失败: {e}")
            return False

    def process_grouping(self):
        """第三阶段：识别分组"""
        try:
            if not hasattr(self, 'merged_entities') or not self.merged_entities:
                return False

            # 执行分组处理
            groups = self.processor.group_entities(self.merged_entities)
            self.all_groups = groups

            # 初始化组状态信息
            self.groups_info = []
            for i, group in enumerate(groups):
                self.groups_info.append({
                    'index': i,
                    'status': 'unlabeled',
                    'category': None,
                    'entity_count': len(group)
                })

            return True

        except Exception as e:
            print(f"识别分组失败: {e}")
            return False

    def process_single_file(self, file_path):
        """处理单个DXF文件"""
        try:
            # 重置重复调用标记
            if hasattr(self, '_showing_manual_group'):
                delattr(self, '_showing_manual_group')
            if hasattr(self, '_completion_shown'):
                delattr(self, '_completion_shown')

            # 检查停止标志
            if self.should_stop:
                return False

            if self.status_callback:
                self.status_callback("status", f"正在加载 {os.path.basename(file_path)}...")

            # 加载DXF文件
            entities = self.processor.load_dxf_file(file_path)
            if not entities:
                if self.status_callback:
                    self.status_callback("error", f"无法加载文件: {file_path}")
                return False

            # 检查停止标志
            if self.should_stop:
                return False

            self.current_file_entities = entities

            if self.status_callback:
                self.status_callback("status", f"加载完成，共 {len(entities)} 个实体")

            # 步骤1: 自动分组和标注墙体、门窗、栏杆
            if self.should_stop:
                return False

            if self.status_callback:
                self.status_callback("status", "正在自动处理墙体、门窗、栏杆...")

            auto_groups = self._auto_process_special_entities(entities)

            # 步骤2: 处理其他实体 - 智能分组
            if self.should_stop:
                return False

            if self.status_callback:
                self.status_callback("status", "正在处理其他实体...")

            other_groups = self._process_other_entities(entities, auto_groups)

            # 步骤3: 优化小元素和小组
            if self.should_stop:
                return False

            if self.status_callback:
                self.status_callback("status", "正在优化小元素和小组...")

            optimized_groups = self._optimize_groups(other_groups, auto_groups)

            # 检查停止标志
            if self.should_stop:
                return False

            # 合并所有组
            self.all_groups = auto_groups + optimized_groups

            # 强制合并SPLINE实体
            if self.should_stop:
                return False

            print(f"调用强制合并SPLINE实体，当前组数: {len(self.all_groups)}")
            self.all_groups = self.processor._force_merge_spline_entities(self.all_groups)
            print(f"强制合并完成，最终组数: {len(self.all_groups)}")

            # 检查停止标志
            if self.should_stop:
                return False

            # 步骤4: 进入手动分组模式（如果有其他组需要处理）
            # 使用更准确的方法来确定需要手动处理的组
            self._update_pending_manual_groups()

            if self.pending_manual_groups:
                if self.status_callback:
                    self.status_callback("status", f"需要手动标注 {len(self.pending_manual_groups)} 个组")

                self.current_manual_group_index = 0
                self.manual_grouping_mode = True

                # 更新组状态信息
                self._update_groups_info()

                # 显示第一个待标注的组
                self._show_next_manual_group()
            else:
                if self.status_callback:
                    self.status_callback("status", "所有组已自动处理完成")
                self.manual_grouping_mode = False

                # 更新组状态信息
                self._update_groups_info()

            # 处理完成后检查未处理组（修复：避免重复调用）
            if self.pending_manual_groups:
                self.manual_grouping_mode = True
                # 注意：这里不再调用_show_next_manual_group()，因为前面已经调用过了
                print(f"✅ 手动标注模式已启动，待处理组数: {len(self.pending_manual_groups)}")
            else:
                # 只有当确实没有待处理组时才显示完成
                self._show_completion_message()
            
            return True
            
        except Exception as e:
            if self.status_callback:
                self.status_callback("error", f"处理文件失败: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            return False
    
    def _auto_process_special_entities(self, entities):
        """自动处理特殊实体（墙体、门窗、栏杆）"""
        # 检查停止标志
        if self.should_stop:
            return []

        # 识别特殊图层
        wall_layers = self.processor._detect_special_layers(entities, self.processor.wall_layer_patterns, debug=True, layer_type="墙体")

        if self.should_stop:
            return []

        door_window_layers = self.processor._detect_special_layers(entities, self.processor.door_window_layer_patterns, debug=True, layer_type="门窗")

        if self.should_stop:
            return []

        railing_layers = self.processor._detect_special_layers(entities, self.processor.railing_layer_patterns, debug=True, layer_type="栏杆")

        print(f"特殊图层识别结果:")
        print(f"  墙体图层: {wall_layers}")
        print(f"  门窗图层: {door_window_layers}")
        print(f"  栏杆图层: {railing_layers}")

        auto_groups = []

        # 处理墙体
        if self.should_stop:
            return auto_groups

        wall_entities = [e for e in entities if e['layer'] in wall_layers]
        if wall_entities:
            # 测试墙体图层线条合并
            self._test_wall_line_merging(wall_entities)

            wall_groups = self.processor._group_special_entities_by_layer(
                wall_entities, connection_threshold=10, entity_type="wall"
            )
            
            # 墙体分组完成后，合并包含的墙体组
            if self.status_callback:
                self.status_callback("status", "正在合并包含的墙体组...")
            
            wall_groups = self.processor.merge_contained_wall_groups(wall_groups)
            
            # 自动标注墙体
            for group in wall_groups:
                # 检查停止标志
                if self.should_stop:
                    return auto_groups

                # 获取实体列表（处理字典格式的组）
                entities_list = group.get('entities', []) if isinstance(group, dict) else group

                for entity in entities_list:
                    entity['label'] = '墙体'  # 使用中文标签
                    entity['auto_labeled'] = True

                # 添加到数据集
                features = self.processor.extract_features(entities_list)
                self.dataset.append({
                    'features': features,
                    'label': 'wall',
                    'source_file': self.current_file,
                    'entity_count': len(entities_list),
                    'auto_labeled': True
                })

            # 提取实体列表并添加到auto_groups
            wall_entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in wall_groups]
            auto_groups.extend(wall_entity_groups)
            self.auto_labeled_entities.extend(wall_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("墙体", len(wall_groups), len(wall_entities)))
        
        # 处理门窗
        door_window_entities = [e for e in entities if e['layer'] in door_window_layers]
        if door_window_entities:
            door_window_groups = self.processor._group_special_entities_by_layer(
                door_window_entities, connection_threshold=50, entity_type="door_window"
            )
            
            # 自动标注门窗
            for group in door_window_groups:
                # 获取实体列表（处理字典格式的组）
                entities_list = group.get('entities', []) if isinstance(group, dict) else group

                for entity in entities_list:
                    entity['label'] = '门窗'  # 使用中文标签
                    entity['auto_labeled'] = True

                # 添加到数据集
                features = self.processor.extract_features(entities_list)
                self.dataset.append({
                    'features': features,
                    'label': 'door_window',
                    'source_file': self.current_file,
                    'entity_count': len(entities_list),
                    'auto_labeled': True
                })
            
            # 提取实体列表并添加到auto_groups
            door_window_entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in door_window_groups]
            auto_groups.extend(door_window_entity_groups)
            self.auto_labeled_entities.extend(door_window_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("门窗", len(door_window_groups), len(door_window_entities)))
        
        # 处理栏杆
        railing_entities = [e for e in entities if e['layer'] in railing_layers]
        if railing_entities:
            railing_groups = self.processor._group_special_entities_by_layer(
                railing_entities, connection_threshold=20, entity_type="railing"
            )
            
            # 自动标注栏杆
            for group in railing_groups:
                # 获取实体列表（处理字典格式的组）
                entities_list = group.get('entities', []) if isinstance(group, dict) else group

                for entity in entities_list:
                    entity['label'] = '栏杆'  # 使用中文标签
                    entity['auto_labeled'] = True

                # 添加到数据集
                features = self.processor.extract_features(entities_list)
                self.dataset.append({
                    'features': features,
                    'label': 'railing',
                    'source_file': self.current_file,
                    'entity_count': len(entities_list),
                    'auto_labeled': True
                })
            
            # 提取实体列表并添加到auto_groups
            railing_entity_groups = [group.get('entities', []) if isinstance(group, dict) else group for group in railing_groups]
            auto_groups.extend(railing_entity_groups)
            self.auto_labeled_entities.extend(railing_entities)
            
            if self.status_callback:
                self.status_callback("auto_labeled", ("栏杆", len(railing_groups), len(railing_entities)))
        
        # 设置所有组数据
        self.all_groups = auto_groups

        # 在自动标注完成后更新组状态信息
        self._update_groups_info()
        
        # 更新可视化（检查是否为后台处理）
        should_skip_visualization = getattr(self, '_is_background_processing', False)

        # 如果有状态回调，尝试检查是否在处理显示文件
        if not should_skip_visualization and hasattr(self, 'status_callback') and self.status_callback:
            # 检查回调是否是绑定方法，并且有_is_processing_display_file方法
            if hasattr(self.status_callback, '__self__') and hasattr(self.status_callback.__self__, '_is_processing_display_file'):
                should_skip_visualization = not self.status_callback.__self__._is_processing_display_file()
            # 如果回调本身有_is_processing_display_file方法
            elif hasattr(self.status_callback, '_is_processing_display_file'):
                should_skip_visualization = not self.status_callback._is_processing_display_file()

        if self.visualizer and self.canvas and not should_skip_visualization:
            try:
                self.visualizer.visualize_overview(
                    self.current_file_entities,
                    [],  # 没有当前处理组
                    self.auto_labeled_entities,  # 已自动标注的实体
                    processor=self
                )
                self.visualizer.update_canvas(self.canvas)
            except Exception as e:
                print(f"自动标注可视化更新失败: {e}")
        elif should_skip_visualization:
            print("跳过后台处理的自动标注可视化更新")
        
        return auto_groups
    
    def _process_other_entities(self, entities, auto_groups):
        """处理其他实体（非特殊图层）"""
        try:
            # 获取已处理的实体ID
            processed_entity_ids = set()
            for group in auto_groups:
                # 处理不同格式的组（字典格式或列表格式）
                if isinstance(group, dict):
                    group_entities = group.get('entities', [])
                elif isinstance(group, list):
                    group_entities = group
                else:
                    continue

                if isinstance(group_entities, list):
                    for entity in group_entities:
                        if isinstance(entity, dict):
                            processed_entity_ids.add(id(entity))

            # 获取未处理的实体
            unprocessed_entities = [e for e in entities if isinstance(e, dict) and id(e) not in processed_entity_ids]

            print(f"  未处理实体数量: {len(unprocessed_entities)}")

            if not unprocessed_entities:
                return []

            # 使用CAD数据处理器进行分组
            
            # 调试：检查输入数据
            print(f"    调试：输入实体类型: {[type(e) for e in unprocessed_entities[:3]]}")
            print(f"    调试：输入实体示例: {unprocessed_entities[0] if unprocessed_entities else 'None'}")
            other_groups = self.processor.group_entities(unprocessed_entities, distance_threshold=20, debug=False)

            print(f"  其他实体分组结果: {len(other_groups)}个组")
            
            # 调试：检查分组结果数据结构
            for i, group in enumerate(other_groups[:3]):  # 只检查前3个组
                print(f"    调试：组 {i} 类型: {type(group)}")
                if isinstance(group, dict):
                    print(f"    调试：组 {i} 键: {list(group.keys())}")
                elif isinstance(group, list):
                    print(f"    调试：组 {i} 长度: {len(group)}")
                else:
                    print(f"    调试：组 {i} 值: {group}")

            # 验证并修复分组数据结构
            valid_groups = []
            for i, group in enumerate(other_groups):
                if isinstance(group, dict):
                    # 确保组有正确的结构
                    if 'entities' not in group:
                        print(f"    警告：组 {i} 缺少entities字段，跳过")
                        continue

                    # 为每个组添加基本信息
                    if 'label' not in group:
                        group['label'] = f'other_{i}'
                    if 'group_type' not in group:
                        group['group_type'] = 'general'
                    if 'status' not in group:
                        group['status'] = 'pending'
                    if 'confidence' not in group:
                        group['confidence'] = 0.5

                    valid_groups.append(group)
                else:
                    print(f"    警告：组 {i} 不是字典类型，跳过")

            print(f"  有效分组数量: {len(valid_groups)}个组")
            return valid_groups

        except Exception as e:
            print(f"  ❌ 处理其他实体失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    
    def _optimize_groups(self, other_groups, auto_groups):
        """优化分组结果"""
        try:
            print(f"  开始优化分组: {len(other_groups)}个其他组, {len(auto_groups)}个自动组")
            
            # 简单的优化：过滤掉太小的组
            optimized_groups = []
            
            for group in other_groups:
                # 处理不同格式的组（字典格式或列表格式）
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                elif isinstance(group, list):
                    entities = group
                else:
                    entities = []

                if len(entities) >= 1:  # 至少包含1个实体
                    optimized_groups.append(group)
                else:
                    print(f"    过滤掉小组: {len(entities)}个实体")
            
            print(f"  优化完成: {len(optimized_groups)}个组")
            return optimized_groups
            
        except Exception as e:
            print(f"  ❌ 优化分组失败: {e}")
            return other_groups

    
    def _show_next_manual_group(self):
        """显示下一个需要手动标注的组（修复：避免重复调用）"""
        # 添加调用标记，避免重复调用
        if hasattr(self, '_showing_manual_group') and self._showing_manual_group:
            print("⚠️ _show_next_manual_group 正在执行中，跳过重复调用")
            return

        self._showing_manual_group = True

        try:
            if not self.pending_manual_groups or self.current_manual_group_index >= len(self.pending_manual_groups):
                # 所有手动组都处理完了，检查是否还有其他未标注的组
                self.manual_grouping_mode = False

                # 更新组状态信息，确保状态同步
                self._update_groups_info()

                # 安全地检查是否还有其他未标注的组
                try:
                    if self.has_unlabeled_groups():
                        next_group = self.get_next_unlabeled_group()
                        if next_group is not None:
                            # 自动跳转到下一个未标注的组
                            self.jump_to_group(next_group)
                            if self.status_callback:
                                self.status_callback("auto_jump", f"自动跳转到组{next_group}")
                            return
                except Exception as e:
                    print(f"检查未标注组时出错: {e}")
                    # 出错时假设没有未标注组，继续完成流程

                # 如果确实没有更多未标注的组，才显示完成信息
                should_skip_visualization = getattr(self, '_is_background_processing', False)

                # 如果有状态回调，尝试检查是否在处理显示文件
                if not should_skip_visualization and hasattr(self, 'status_callback') and self.status_callback:
                    # 检查回调是否是绑定方法，并且有_is_processing_display_file方法
                    if hasattr(self.status_callback, '__self__') and hasattr(self.status_callback.__self__, '_is_processing_display_file'):
                        should_skip_visualization = not self.status_callback.__self__._is_processing_display_file()
                    # 如果回调本身有_is_processing_display_file方法
                    elif hasattr(self.status_callback, '_is_processing_display_file'):
                        should_skip_visualization = not self.status_callback._is_processing_display_file()

                if self.visualizer and self.canvas and not should_skip_visualization:
                    try:
                        # 清空详细视图
                        self.visualizer.ax_detail.clear()
                        self.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成',
                                                       ha='center', va='center', transform=self.visualizer.ax_detail.transAxes,
                                                       fontproperties=self.visualizer.chinese_font, fontsize=14)
                        self.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.visualizer.chinese_font)

                        # 更新全图概览，不显示当前处理组（安全版本）
                        try:
                            current_entities = getattr(self, 'current_file_entities', [])
                            auto_labeled = getattr(self, 'auto_labeled_entities', [])
                            labeled = getattr(self, 'labeled_entities', [])

                            self.visualizer.visualize_overview(
                                current_entities,
                                None,  # 不显示当前处理组
                                auto_labeled + labeled,  # 已标注的实体
                                processor=self
                            )
                        except Exception as overview_error:
                            print(f"更新全图概览失败: {overview_error}")
                            # 继续执行，不中断完成流程
                        self.visualizer.update_canvas(self.canvas)
                    except Exception as e:
                        print(f"完成时可视化更新失败: {e}")
                elif should_skip_visualization:
                    print("跳过后台处理的完成状态可视化更新")

                # 检查是否为后台处理，如果是则跳过状态回调
                if self.status_callback and not getattr(self, '_is_background_processing', False):
                    self.status_callback("manual_complete", "所有手动分组已完成")
                return

            # 如果有待处理的手动组，继续处理
            current_group = self.pending_manual_groups[self.current_manual_group_index]

            print(f"显示手动分组组: {self.current_manual_group_index + 1}/{len(self.pending_manual_groups)}, 实体数量: {len(current_group)}")

            # 更新组状态信息，确保当前组标记为"标注中"
            self._update_groups_info()

            # 检查是否为后台处理，如果是则跳过状态回调
            if self.status_callback and not getattr(self, '_is_background_processing', False):
                self.status_callback("manual_group", {
                    'index': self.current_manual_group_index + 1,
                    'total': len(self.pending_manual_groups),
                    'entity_count': len(current_group)
                })
                # 通知UI更新组列表
                self.status_callback("update_group_list", None)

            # 更新可视化，突出显示当前组（检查是否为后台处理）
            # 使用更准确的检查方式：检查是否为后台处理或非显示文件
            should_skip_visualization = getattr(self, '_is_background_processing', False)

            # 如果有状态回调，尝试检查是否在处理显示文件
            if not should_skip_visualization and hasattr(self, 'status_callback') and self.status_callback:
                # 检查回调是否是绑定方法，并且有_is_processing_display_file方法
                if hasattr(self.status_callback, '__self__') and hasattr(self.status_callback.__self__, '_is_processing_display_file'):
                    should_skip_visualization = not self.status_callback.__self__._is_processing_display_file()
                # 如果回调本身有_is_processing_display_file方法
                elif hasattr(self.status_callback, '_is_processing_display_file'):
                    should_skip_visualization = not self.status_callback._is_processing_display_file()

            if self.visualizer and self.canvas and not should_skip_visualization:
                try:
                    print("🔍 更新详细视图...")

                    # 清理组数据，确保只包含有效实体
                    cleaned_group = self._clean_group_data(current_group)
                    print(f"  清理后组数据: {len(cleaned_group)} 个实体")

                    self.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)

                    print("🌍 更新全图概览...")
                    self.visualizer.visualize_overview(
                        self.current_file_entities,
                        cleaned_group,  # 使用清理后的当前组
                        self.auto_labeled_entities + self.labeled_entities,  # 已标注的实体
                        processor=self  # 🔑 修复：添加processor参数以启用条件4
                    )
                    self.visualizer.update_canvas(self.canvas)
                    print("可视化更新成功")
                except Exception as e:
                    print(f"手动分组可视化更新失败: {e}")
                    import traceback
                    traceback.print_exc()
            elif should_skip_visualization:
                print("跳过后台处理的可视化更新")

        finally:
            # 确保标记被清除
            self._showing_manual_group = False
    
    def label_current_group(self, label):
        """为当前组添加标签（修复版 - 正确管理组索引）"""
        if not self.manual_grouping_mode or self.current_manual_group_index >= len(self.pending_manual_groups):
            return False

        current_group = self.pending_manual_groups[self.current_manual_group_index]

        # 🔧 修复：获取当前组在all_groups中的真实索引
        try:
            real_group_index = self.all_groups.index(current_group)
            print(f"🏷️ 标注组{real_group_index + 1}为: {label}")
        except ValueError:
            print(f"⚠️ 无法找到组在all_groups中的位置")
            return False

        # 处理不同格式的组（字典格式或列表格式）
        if isinstance(current_group, dict):
            entities = current_group.get('entities', [])
        elif isinstance(current_group, list):
            entities = current_group
        else:
            print(f"⚠️ 未知的组格式: {type(current_group)}")
            return False

        # 为组中的每个实体设置标签
        for entity in entities:
            if isinstance(entity, dict):
                entity['label'] = label
            else:
                print(f"⚠️ 跳过非字典实体: {type(entity)}")

        # 添加到数据集
        features = self.processor.extract_features(entities)
        self.dataset.append({
            'features': features,
            'label': label,
            'source_file': self.current_file,
            'entity_count': len(entities),
            'auto_labeled': False
        })

        # 添加到已标注实体列表
        self.labeled_entities.extend(current_group)

        # 🔧 修复：从待处理列表中移除已标注的组，而不是简单地增加索引
        self.pending_manual_groups.pop(self.current_manual_group_index)

        # 🔧 修复：调整索引，确保指向下一个待处理组
        if self.current_manual_group_index >= len(self.pending_manual_groups):
            self.current_manual_group_index = 0  # 重置到开头

        print(f"📋 剩余待标注组: {len(self.pending_manual_groups)} 个")

        # 更新组状态信息（安全调用）
        try:
            self._update_groups_info()
        except Exception as e:
            print(f"更新组状态信息时出错: {e}")
            # 确保不影响标注流程
            pass

        # 显示下一个组
        self._show_next_manual_group()

        # 通知界面更新组列表
        if self.status_callback:
            self.status_callback("update_group_list", None)
            category_name = self.processor.category_mapping.get(label, label)
            self.status_callback("group_labeled", (label, category_name, len(current_group)))
        
        return True
    
    def skip_current_group(self):
        """跳过当前组"""
        if not self.manual_grouping_mode or self.current_manual_group_index >= len(self.pending_manual_groups):
            return False
        
        # 移动到下一组
        self.current_manual_group_index += 1
        
        # 更新组状态信息
        self._update_groups_info()
        
        # 显示下一个组
        self._show_next_manual_group()
        
        if self.status_callback:
            self.status_callback("group_skipped", "已跳过当前组")
        
        return True
    

    
    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        self.is_running = False
        self.manual_grouping_mode = False

        # 停止CAD数据处理器
        if hasattr(self, 'processor') and self.processor:
            self.processor.stop_processing()
    
    def save_dataset(self, include_images=False, output_dir=None):
        """保存数据集"""
        if not self.dataset:
            return False, "没有可保存的数据"
        
        try:
            if not output_dir:
                output_dir = "output"
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存数据集
            dataset_file = os.path.join(output_dir, "cad_dataset.csv")
            df = pd.DataFrame(self.dataset)
            df.to_csv(dataset_file, index=False, encoding='utf-8-sig')
            
            result_msg = f"数据集已保存: {dataset_file} (包含 {len(self.dataset)} 个样本)"
            
            # 保存图片（如果需要）
            if include_images and self.visualizer and self.current_file_entities:
                try:
                    image_file = os.path.join(output_dir, f"{self.current_file}_classification.png")
                    
                    # 创建分类图
                    self.visualizer.visualize_overview(
                        self.current_file_entities,
                        [],  # 没有当前处理组
                        self.auto_labeled_entities + self.labeled_entities,  # 所有已标注实体
                        processor=self
                    )
                    
                    # 保存图片
                    self.visualizer.fig.savefig(image_file, dpi=300, bbox_inches='tight')
                    result_msg += f"\n分类图已保存: {image_file}"
                    
                except Exception as e:
                    print(f"保存图片失败: {e}")
                    result_msg += f"\n图片保存失败: {str(e)}"
            
            return True, result_msg
            
        except Exception as e:
            return False, f"保存失败: {str(e)}"
    
    def get_groups_info(self):
        """获取所有组的状态信息"""
        return self.groups_info
    
    def jump_to_group(self, group_index):
        print(f"跳转到组{group_index}请求")
        print(f"总组数: {len(self.all_groups)}")
        print(f"自动组数: {len([g for g in self.all_groups if any(e.get('auto_labeled', False) for e in g)])}")
        print(f"待处理手动组: {len(self.pending_manual_groups)}")

        # 获取实际组对象
        group = self.get_group_by_index(group_index)
        if not group:
            return

        # 检查是否自动标注组
        # 处理不同格式的组（字典格式或列表格式）
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            entities = []

        is_auto_labeled = any(isinstance(e, dict) and e.get('auto_labeled', False) for e in entities)

        if is_auto_labeled:
            # 自动标注组直接显示
            self._show_group(group, group_index)
            self._update_group_status(group_index, 'labeling')
        else:
            # 非自动标注组更新待处理列表
            self._update_pending_manual_groups()

            # 找到组在待处理列表中的位置
            try:
                manual_index = self.pending_manual_groups.index(group)
                self.current_manual_group_index = manual_index
                self.manual_grouping_mode = True
                self._show_next_manual_group()
            except ValueError:
                # 组不在待处理列表中
                self._show_group(group, group_index)
                self._update_group_status(group_index, 'labeling')

        # 确保更新当前组状态为"标注中"
        self._update_group_status(group_index, 'labeling')

        # 强制更新可视化
        if self.visualizer and self.canvas:
            self._show_group(group, group_index)

    def _clean_group_data(self, group):
        """清理组数据，确保只包含有效的实体字典"""
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            return []

        # 过滤并清理实体
        cleaned_entities = []
        for entity in entities:
            if isinstance(entity, dict) and entity.get('type') and entity.get('layer'):
                cleaned_entities.append(entity)
            else:
                print(f"    ⚠️ 跳过无效实体: {type(entity)} - {str(entity)[:100]}")

        return cleaned_entities

    def _update_groups_info(self):
        """更新组信息（修复版 - 正确识别组状态）"""
        try:
            self.groups_info = []

            if not hasattr(self, 'all_groups') or not self.all_groups:
                print("  ⚠️ 没有分组数据，无法更新组信息")
                return

            print(f"  开始更新组信息，共 {len(self.all_groups)} 个组")

            # 获取自动标注组数量
            auto_groups_count = len(getattr(self, 'auto_labeled_entities', []))

            for i, group in enumerate(self.all_groups):
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                    group_info = {
                        'index': i,
                        'label': group.get('label', f'group_{i}'),
                        'entity_count': len(entities),
                        'status': group.get('status', 'pending'),
                        'confidence': group.get('confidence', 0.0),
                        'group_type': group.get('group_type', 'unknown'),
                        'layer': group.get('layer', 'unknown')
                    }
                    self.groups_info.append(group_info)
                elif isinstance(group, list):
                    # 处理列表格式的组（正常情况，不需要警告）
                    # 尝试从组中的实体获取更多信息
                    layer_info = 'unknown'
                    label_info = f'group_{i}'
                    status_info = 'unlabeled'  # 默认为未标注
                    confidence_info = 0.0
                    group_type_info = 'unknown'

                    if group and isinstance(group[0], dict):
                        layer_info = group[0].get('layer', 'unknown')

                        # 根据图层推断组类型（对所有组都进行推断）
                        if layer_info in ['A-WALL', 'WALL', 'wall']:
                            group_type_info = 'wall'
                        elif layer_info in ['A-DOOR', 'A-WINDOW', 'DOOR', 'WINDOW', 'door', 'window']:
                            group_type_info = 'door_window'
                        else:
                            group_type_info = 'other'

                        # 🔧 修复：正确检查组状态
                        # 1. 检查是否为自动标注的组
                        is_auto_labeled = any(isinstance(e, dict) and e.get('auto_labeled', False) for e in group)
                        # 2. 检查是否为手动标注的组
                        is_manually_labeled = any(isinstance(e, dict) and e.get('label') and not e.get('auto_labeled', False) for e in group)
                        # 3. 检查是否为当前标注中的组
                        is_current_group = (hasattr(self, 'current_group_index') and
                                          self.current_group_index is not None and
                                          self.current_group_index == i)

                        if is_current_group and self.manual_grouping_mode:
                            status_info = 'labeling'  # 正在标注中
                            label_info = '待标注'
                        elif is_manually_labeled:
                            # 手动标注的组
                            first_labeled_entity = next((e for e in group if isinstance(e, dict) and e.get('label') and not e.get('auto_labeled', False)), None)
                            if first_labeled_entity:
                                label_info = first_labeled_entity.get('label', f'group_{i}')
                                status_info = 'labeled'
                                confidence_info = first_labeled_entity.get('confidence', 1.0)
                        elif is_auto_labeled:
                            # 自动标注的组
                            first_auto_entity = next((e for e in group if isinstance(e, dict) and e.get('auto_labeled', False)), None)
                            if first_auto_entity:
                                label_info = first_auto_entity.get('label', f'group_{i}')
                                status_info = 'auto_labeled'
                                confidence_info = first_auto_entity.get('confidence', 0.8)
                        else:
                            # 未标注的组
                            status_info = 'unlabeled'
                            label_info = '未标注'

                    group_info = {
                        'index': i,
                        'label': label_info,
                        'entity_count': len(group),
                        'status': status_info,
                        'confidence': confidence_info,
                        'group_type': group_type_info,
                        'layer': layer_info
                    }
                    self.groups_info.append(group_info)
                    print(f"    组{i+1}: {status_info} - {label_info} ({len(group)}个实体)")
                else:
                    # 只对真正无法处理的类型打印警告
                    print(f"    警告：组 {i} 类型无法处理: {type(group)}")

            print(f"  ✅ 组信息更新完成: {len(self.groups_info)}个组")

            # 🔧 修复：更新待标注组列表
            self._update_pending_manual_groups()

        except Exception as e:
            print(f"  ❌ 更新组信息失败: {e}")
            import traceback
            traceback.print_exc()
            self.groups_info = []

    
    def _update_group_status(self, group_index, status):
        """更新指定组的状态"""
        if 0 < group_index <= len(self.groups_info):
            self.groups_info[group_index-1]['status'] = status
        
        # 强制刷新UI
        if self.status_callback:
            self.status_callback("update_group_list", None)
    
    def can_relabel_group(self, group_index):
        """检查是否可以重新分类指定组"""
        # 调整索引（界面是从1开始，内部是从0开始）
        internal_index = group_index - 1

        # 检查组索引是否有效
        if internal_index < 0 or internal_index >= len(self.all_groups):
            return False

        # 获取组
        group = self.all_groups[internal_index]

        # 处理不同格式的组（字典格式或列表格式）
        if isinstance(group, dict):
            entities = group.get('entities', [])
        elif isinstance(group, list):
            entities = group
        else:
            entities = []

        # 检查是否是自动标注的组
        if any(isinstance(e, dict) and e.get('auto_labeled', False) for e in entities):
            return True

        # 检查是否是已标注的组（手动标注或重新标注）
        if any(isinstance(e, dict) and e.get('label') for e in entities):
            return True

        # 检查是否是待处理的手动组
        if group in self.pending_manual_groups:
            return True

        return False
    
    def start_relabel_group(self, group_index):
        """开始重新分类指定组"""
        if not self.can_relabel_group(group_index):
            return False
        
        # 获取组并显示在可视化中
        group = self.get_group_by_index(group_index)
        if group:
            # 使用统一的_show_group方法，它包含了后台处理检查
            self._show_group(group, group_index)
        
        return True
    
    def relabel_group(self, group_index, new_label):
        group = self.get_group_by_index(group_index)

        if not group:
            return False

        try:
            # 处理不同格式的组（字典格式或列表格式）
            if isinstance(group, dict):
                entities = group.get('entities', [])
            elif isinstance(group, list):
                entities = group
            else:
                entities = []

            # 更新组中所有实体的标签
            for entity in entities:
                if isinstance(entity, dict):
                    entity['label'] = new_label
                    # 如果是自动标注的，标记为手动修改
                    if entity.get('auto_labeled', False):
                        entity['manually_relabeled'] = True

            # 从原有列表中移除（如果存在）
            for entity in entities:
                if isinstance(entity, dict) and entity in self.labeled_entities:
                    self.labeled_entities.remove(entity)

            # 添加到已标注实体列表
            self.labeled_entities.extend([e for e in entities if isinstance(e, dict)])

            # 更新数据集中的记录
            self._update_dataset_for_relabeled_group(entities, new_label, group_index)

            # 更新组状态信息
            self._update_groups_info()

            # 通知界面更新
            if self.status_callback:
                self.status_callback("update_group_list", None)
                self.status_callback("group_relabeled", (group_index, new_label))

            # 先显示重新分类的组（高亮显示）
            self._show_group(group, group_index)

            # 延迟跳转到下一个待处理组（给用户时间看到重新分类的结果）
            import threading
            def delayed_jump():
                import time
                time.sleep(1.5)  # 延迟1.5秒

                # 重新分类后，优先跳转到第一个待处理的组
                self._update_pending_manual_groups()  # 重新更新待处理组列表

                if self.pending_manual_groups:
                    # 有待处理的手动组，跳转到第一个
                    self.current_manual_group_index = 0
                    self.manual_grouping_mode = True
                    first_pending_group = self.pending_manual_groups[0]
                    first_pending_group_index = self.all_groups.index(first_pending_group) + 1
                    self.jump_to_group(first_pending_group_index)
                    if self.status_callback:
                        self.status_callback("auto_jump", f"自动跳转到第一个待处理组{first_pending_group_index}")
                elif self.has_unlabeled_groups():
                    # 没有待处理的手动组，但还有其他未标注组
                    next_group = self.get_next_unlabeled_group()
                    if next_group:
                        self.jump_to_group(next_group)
                        if self.status_callback:
                            self.status_callback("auto_jump", f"自动跳转到组{next_group}")
                    else:
                        self._show_completion_message()
                else:
                    # 所有组都已标注完成
                    self._show_completion_message()

            # 在后台线程中执行延迟跳转
            threading.Thread(target=delayed_jump, daemon=True).start()

            return True

        except Exception as e:
            print(f"重新分类失败: {e}")
            return False
    
    def get_group_by_index(self, group_index):
        """根据索引获取组（修正版）"""
        # 总组数检查
        if group_index < 1 or group_index > len(self.all_groups):
            return None
        
        # 直接返回all_groups中的组
        return self.all_groups[group_index-1]
    
    def _validate_group_index(self, group_index):
        """验证组索引有效性"""
        if not isinstance(group_index, int) or group_index < 1:
            return False
        return group_index <= len(self.all_groups)
    
    def _update_pending_manual_groups(self):
        """更新待处理手动组列表（修复版 - 正确识别待标注组）"""
        self.pending_manual_groups = []

        for g in self.all_groups:
            # 处理不同格式的组（字典格式或列表格式）
            if isinstance(g, dict):
                entities = g.get('entities', [])
            elif isinstance(g, list):
                entities = g
            else:
                continue

            # 🔧 修复：更准确的组状态检查
            is_auto_labeled = any(isinstance(e, dict) and e.get('auto_labeled', False) for e in entities)
            is_manually_labeled = any(isinstance(e, dict) and e.get('label') and not e.get('auto_labeled', False) for e in entities)

            # 只有既不是自动标注也不是手动标注的组才需要手动处理
            if not is_auto_labeled and not is_manually_labeled:
                self.pending_manual_groups.append(g)

        print(f"  📋 更新待标注组列表: {len(self.pending_manual_groups)} 个组待处理")
    
    def _show_group(self, group, group_index=None):
        """显示指定组（统一方法）"""
        # 检查是否应该跳过可视化更新（后台处理或非显示文件）
        should_skip_visualization = getattr(self, '_is_background_processing', False)

        # 如果有状态回调，尝试检查是否在处理显示文件
        if not should_skip_visualization and hasattr(self, 'status_callback') and self.status_callback:
            # 检查回调是否是绑定方法，并且有_is_processing_display_file方法
            if hasattr(self.status_callback, '__self__') and hasattr(self.status_callback.__self__, '_is_processing_display_file'):
                should_skip_visualization = not self.status_callback.__self__._is_processing_display_file()
            # 如果回调本身有_is_processing_display_file方法
            elif hasattr(self.status_callback, '_is_processing_display_file'):
                should_skip_visualization = not self.status_callback._is_processing_display_file()

        if should_skip_visualization:
            print(f"跳过后台文件的界面更新: 组{group_index if group_index else '未知'}")
            return

        if self.visualizer and self.canvas:
            try:
                # 更新详细视图
                print("🔍 更新详细视图")

                # 清理组数据，确保只包含有效实体
                cleaned_group = self._clean_group_data(group)
                print(f"  清理后组数据: {len(cleaned_group)} 个实体")

                self.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)

                # 获取组索引
                if group_index is None and group in self.all_groups:
                    group_index = self.all_groups.index(group) + 1

                # 更新全图概览，突出显示当前组
                print("🌍 更新全图概览")

                # 安全获取实体数据
                current_file_entities = getattr(self, 'current_file_entities', [])
                auto_labeled_entities = getattr(self, 'auto_labeled_entities', [])
                labeled_entities = getattr(self, 'labeled_entities', [])

                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")

                if current_file_entities:
                    self.visualizer.visualize_overview(
                        current_file_entities,
                        cleaned_group,  # 使用清理后的当前组
                        auto_labeled_entities + labeled_entities,  # 已标注的实体
                        processor=self,
                        current_group_index=group_index  # 传递组索引
                    )
                else:
                    print("  ⚠️ 没有实体数据，跳过绘制")
                self.visualizer.update_canvas(self.canvas)
                print("可视化更新成功")
            except Exception as e:
                print(f"显示组可视化失败: {e}")

        # 更新状态显示
        if self.status_callback:
            # 获取组在列表中的位置
            if group_index is None:
                if group in self.all_groups:
                    group_index = self.all_groups.index(group) + 1
                else:
                    group_index = "未知"

            # 获取正确的实体数量
            if isinstance(group, dict):
                entity_count = len(group.get('entities', []))
            elif isinstance(group, list):
                entity_count = len(group)
            else:
                entity_count = 0

            self.status_callback("manual_group", {
                'index': group_index,
                'total': len(self.all_groups),
                'entity_count': entity_count
            })
    
    def _show_completion_message(self):
        """显示真正的完成信息（修复：避免重复调用和IndexError）"""
        try:
            # 添加完成标记，避免重复调用
            if hasattr(self, '_completion_shown') and self._completion_shown:
                print("⚠️ 完成信息已显示，跳过重复调用")
                return

            self._completion_shown = True
            print("🎯 显示完成信息")

            # 安全地更新可视化
            if self.visualizer and self.canvas:
                try:
                    # 清空详细视图
                    self.visualizer.ax_detail.clear()
                    self.visualizer.ax_detail.text(0.5, 0.5, '所有处理已完成',
                                                 ha='center', va='center',
                                                 transform=self.visualizer.ax_detail.transAxes,
                                                 fontproperties=self.visualizer.chinese_font,
                                                 fontsize=16, color='green')
                    self.visualizer.ax_detail.set_title('处理完成', fontsize=14,
                                                      fontproperties=self.visualizer.chinese_font)

                    # 更新全图概览，不显示任何高亮组
                    if hasattr(self, 'current_file_entities') and self.current_file_entities:
                        self.visualizer.visualize_overview(
                            self.current_file_entities,
                            None,  # 不显示当前处理组（无高亮）
                            (getattr(self, 'auto_labeled_entities', []) +
                             getattr(self, 'labeled_entities', [])),  # 已标注的实体
                            processor=self
                        )

                    self.visualizer.update_canvas(self.canvas)
                    print("完成信息可视化更新成功")
                except Exception as e:
                    print(f"完成信息可视化更新失败: {e}")

            # 清理处理器状态
            self._cleanup_completion_state()

        except Exception as e:
            print(f"显示完成信息失败: {e}")
            import traceback
            traceback.print_exc()

    def _cleanup_completion_state(self):
        """清理完成状态"""
        try:
            # 设置为非手动标注模式
            self.manual_grouping_mode = False

            # 清除当前组索引
            self.current_group_index = -1

            # 清空待处理组列表
            self.pending_manual_groups = []

            print("处理器完成状态已清理")
        except Exception as e:
            print(f"清理完成状态失败: {e}")

        # 检查是否为后台处理，如果是则跳过状态回调
        if self.status_callback and not getattr(self, '_is_background_processing', False):
            self.status_callback("completed", "所有文件处理完成")
    
    def get_group_info(self, group_index):
        """获取组的详细信息"""
        group = self.get_group_by_index(group_index)
        if not group:
            return {'entity_count': 0, 'status': 'unknown'}
        
        # 获取组的状态
        internal_index = group_index - 1
        if internal_index < len(self.groups_info):
            status_info = self.groups_info[internal_index]
        else:
            status_info = {'status': 'unknown', 'type': ''}
        
        # 获取当前分类
        current_type = None
        for entity in group:
            if entity.get('label'):
                current_type = entity.get('label')
                break
        
        # 获取图层信息
        layers = list(set(entity.get('layer', 'unknown') for entity in group))
        
        # 获取边界框信息
        bbox_info = self._get_group_bbox_info(group)
        
        return {
            'entity_count': len(group),
            'status': status_info['status'],
            'current_type': self.processor.category_mapping.get(current_type, current_type) if current_type else '未分类',
            'layers': layers,
            'bbox_info': bbox_info
        }
    
    def _get_group_bbox_info(self, group):
        """获取组的边界框信息"""
        if not group:
            return "无数据"
        
        try:
            bbox = self.processor._get_group_bbox(group)
            if bbox:
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]
                return f"宽度: {width:.1f}, 高度: {height:.1f}"
            else:
                return "无法计算"
        except:
            return "计算错误"
    
    def _update_dataset_for_relabeled_group(self, group, new_label, group_index=None):
        """更新数据集中重新分类的组"""
        # 查找并更新数据集中的对应记录
        for i, dataset_item in enumerate(self.dataset):
            # 通过特征匹配找到对应的数据集项
            if self._is_same_group(dataset_item, group):
                # 更新标签
                self.dataset[i]['label'] = new_label
                self.dataset[i]['manually_relabeled'] = True
                self.dataset[i]['relabel_timestamp'] = time.time()
                if group_index is not None:
                    self.dataset[i]['group_index'] = group_index
                break
        else:
            # 如果没找到，创建新的数据集项
            features = self.processor.extract_features(group)
            dataset_item = {
                'features': features,
                'label': new_label,
                'source_file': self.current_file,
                'entity_count': len(group),
                'auto_labeled': False,
                'manually_relabeled': True,
                'relabel_timestamp': time.time()
            }
            if group_index is not None:
                dataset_item['group_index'] = group_index
            self.dataset.append(dataset_item)
    
    def _is_same_group(self, dataset_item, group):
        """检查数据集项是否对应指定的组"""
        # 简单的匹配逻辑：实体数量和源文件匹配
        return (dataset_item.get('entity_count') == len(group) and 
                dataset_item.get('source_file') == self.current_file)
    
    def has_unlabeled_groups(self):
        """检查是否还有未标注的组（安全版本，修复IndexError）"""
        try:
            # 检查所有组的状态
            if hasattr(self, 'groups_info') and self.groups_info:
                for group_info in self.groups_info:
                    if isinstance(group_info, dict) and group_info.get('status') in ['unlabeled', 'labeling']:
                        return True

            # 如果没有找到未标注的组，检查是否还有未标注的实体
            if hasattr(self, 'all_groups') and self.all_groups:
                for group in self.all_groups:
                    if isinstance(group, list):
                        for entity in group:
                            if isinstance(entity, dict) and not entity.get('label'):
                                return True

            return False

        except Exception as e:
            print(f"检查未标注组时出错: {e}")
            # 出错时返回False，避免无限循环
            return False
    
    def get_next_unlabeled_group(self):
        """获取下一个未标注组的索引（优先返回待处理组）（安全版本）"""
        try:
            # 首先检查是否有待处理的手动组
            try:
                self._update_pending_manual_groups()
                if (hasattr(self, 'pending_manual_groups') and self.pending_manual_groups and
                    hasattr(self, 'all_groups') and self.all_groups):
                    first_pending_group = self.pending_manual_groups[0]
                    if first_pending_group in self.all_groups:
                        return self.all_groups.index(first_pending_group) + 1
            except Exception as e:
                print(f"检查待处理组时出错: {e}")

            # 然后检查groups_info中的未标注组
            if hasattr(self, 'groups_info') and self.groups_info:
                for i, group_info in enumerate(self.groups_info):
                    if isinstance(group_info, dict) and group_info.get('status') == 'unlabeled':
                        return i + 1  # 返回界面索引（从1开始）

            # 最后检查所有组中是否有未标注的实体
            if hasattr(self, 'all_groups') and self.all_groups:
                for i, group in enumerate(self.all_groups):
                    if isinstance(group, list):
                        has_unlabeled = False
                        for entity in group:
                            if isinstance(entity, dict) and not entity.get('label'):
                                has_unlabeled = True
                                break

                        if has_unlabeled:
                            return i + 1  # 返回界面索引（从1开始）

            return None

        except Exception as e:
            print(f"获取下一个未标注组时出错: {e}")
            return None

    def jump_to_first_unlabeled_group(self):
        """跳转到第一个未标注组（文件切换后使用）"""
        try:
            # 更新待处理组列表
            self._update_pending_manual_groups()

            # 获取第一个未标注组
            first_unlabeled = self.get_next_unlabeled_group()
            if first_unlabeled:
                print(f"🎯 跳转到第一个未标注组: 组{first_unlabeled}")
                self.jump_to_group(first_unlabeled)
                return True
            else:
                print("✅ 所有组都已标注完成")
                return False

        except Exception as e:
            print(f"跳转到第一个未标注组失败: {e}")
            return False

    def export_group_details(self, group_indices=None, output_file="group_details.txt"):
        """导出指定组的实体详细数据，用于分析分组问题"""
        if not self.all_groups:
            return False, "没有可导出的组数据"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== CAD分组详细数据导出 ===\n")
                f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总组数: {len(self.all_groups)}\n\n")
                
                # 确定要导出的组
                if group_indices is None:
                    # 导出所有组
                    groups_to_export = list(enumerate(self.all_groups, 1))
                else:
                    # 导出指定组
                    groups_to_export = [(i, self.all_groups[i-1]) for i in group_indices if 1 <= i <= len(self.all_groups)]
                
                for group_idx, group in groups_to_export:
                    f.write(f"\n{'='*50}\n")
                    f.write(f"组 {group_idx} 详细信息\n")
                    f.write(f"{'='*50}\n")
                    f.write(f"实体数量: {len(group)}\n")
                    
                    # 统计信息
                    entity_types = {}
                    layers = set()
                    for entity in group:
                        entity_type = entity.get('type', 'unknown')
                        entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                        layers.add(entity.get('layer', 'unknown'))
                    
                    f.write(f"实体类型分布: {entity_types}\n")
                    f.write(f"图层: {sorted(layers)}\n")
                    
                    # 边界框信息
                    bbox = self.processor._get_group_bbox(group)
                    if bbox:
                        f.write(f"边界框: ({bbox[0]:.2f}, {bbox[1]:.2f}) - ({bbox[2]:.2f}, {bbox[3]:.2f})\n")
                        f.write(f"尺寸: 宽={bbox[2]-bbox[0]:.2f}, 高={bbox[3]-bbox[1]:.2f}\n")
                    
                    # 详细实体信息
                    f.write(f"\n详细实体信息:\n")
                    f.write("-" * 40 + "\n")
                    
                    for i, entity in enumerate(group, 1):
                        f.write(f"\n实体 {i}:\n")
                        f.write(f"  类型: {entity.get('type', 'unknown')}\n")
                        f.write(f"  图层: {entity.get('layer', 'unknown')}\n")
                        
                        # 根据实体类型输出详细信息
                        entity_type = entity.get('type', '')
                        
                        if entity_type == 'LINE':
                            if 'points' in entity and len(entity['points']) >= 2:
                                start, end = entity['points'][0], entity['points'][1]
                                f.write(f"  起点: ({start[0]:.2f}, {start[1]:.2f})\n")
                                f.write(f"  终点: ({end[0]:.2f}, {end[1]:.2f})\n")
                                f.write(f"  长度: {math.sqrt((end[0]-start[0])**2 + (end[1]-start[1])**2):.2f}\n")
                        
                        elif entity_type == 'LWPOLYLINE':
                            if 'points' in entity and entity['points']:
                                points = entity['points']
                                f.write(f"  点数: {len(points)}\n")
                                f.write(f"  是否闭合: {entity.get('closed', False)}\n")
                                f.write(f"  前5个点: {points[:5]}\n")
                                if len(points) > 5:
                                    f.write(f"  后5个点: {points[-5:]}\n")
                        
                        elif entity_type == 'ARC':
                            if 'center' in entity and 'radius' in entity:
                                center = entity['center']
                                radius = entity['radius']
                                start_angle = entity.get('start_angle', 0)
                                end_angle = entity.get('end_angle', 0)
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  半径: {radius:.2f}\n")
                                f.write(f"  起始角度: {start_angle:.2f}°\n")
                                f.write(f"  结束角度: {end_angle:.2f}°\n")
                                f.write(f"  角度范围: {end_angle - start_angle:.2f}°\n")
                        
                        elif entity_type == 'CIRCLE':
                            if 'center' in entity and 'radius' in entity:
                                center = entity['center']
                                radius = entity['radius']
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  半径: {radius:.2f}\n")
                        
                        elif entity_type == 'ELLIPSE':
                            if 'center' in entity and 'major_axis' in entity:
                                center = entity['center']
                                major_axis = entity['major_axis']
                                ratio = entity.get('ratio', 1.0)
                                start_param = entity.get('start_param', 0)
                                end_param = entity.get('end_param', 2 * math.pi)
                                f.write(f"  中心: ({center[0]:.2f}, {center[1]:.2f})\n")
                                f.write(f"  主轴: ({major_axis[0]:.2f}, {major_axis[1]:.2f})\n")
                                f.write(f"  比例: {ratio:.4f}\n")
                                f.write(f"  起始参数: {start_param:.4f}\n")
                                f.write(f"  结束参数: {end_param:.4f}\n")
                                f.write(f"  参数范围: {end_param - start_param:.4f}\n")
                        
                        elif entity_type == 'TEXT':
                            if 'text' in entity and 'position' in entity:
                                text = entity['text']
                                position = entity['position']
                                height = entity.get('height', 0)
                                f.write(f"  文本: '{text}'\n")
                                f.write(f"  位置: ({position[0]:.2f}, {position[1]:.2f})\n")
                                f.write(f"  高度: {height:.2f}\n")
                        
                        # 计算与其他实体的连接性
                        f.write(f"  连接性分析:\n")
                        for j, other_entity in enumerate(group, 1):
                            if i != j:
                                endpoint_dist = self.processor._calculate_endpoint_distance(entity, other_entity)
                                f.write(f"    与实体{j}端点距离: {endpoint_dist:.2f}\n")
                        
                        f.write(f"  {'-'*30}\n")
                
                f.write(f"\n{'='*50}\n")
                f.write("导出完成\n")
                f.write(f"{'='*50}\n")
            
            return True, f"实体详细数据已导出到: {output_file}"
            
        except Exception as e:
            return False, f"导出失败: {str(e)}"
    
    def export_specific_groups(self, group_indices):
        """导出指定组的详细信息"""
        if not group_indices:
            return False, "请指定要导出的组索引"
        
        output_file = f"groups_{'_'.join(map(str, group_indices))}_details.txt"
        return self.export_group_details(group_indices, output_file)

    def _test_wall_line_merging(self, wall_entities):
        """测试墙体图层线条合并效果"""
        try:
            from line_merger import DXFLineMerger

            # 过滤出线条类型的实体
            line_entities = [e for e in wall_entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']]

            if not line_entities:
                print("🔧 墙体线条合并测试: 未找到线条实体")
                return

            print(f"🔧 墙体线条合并测试:")
            print(f"  检测到的总线条数: {len(line_entities)}")

            # 统计原始线段数（考虑多段线分解）
            original_segments = 0
            line_count = 0
            polyline_count = 0

            for entity in line_entities:
                if entity.get('type') == 'LINE':
                    original_segments += 1
                    line_count += 1
                elif entity.get('type') in ['LWPOLYLINE', 'POLYLINE']:
                    points = entity.get('points', [])
                    if len(points) >= 2:
                        segments_in_polyline = len(points) - 1
                        original_segments += segments_in_polyline
                        polyline_count += 1

            print(f"  - LINE实体: {line_count} 个")
            print(f"  - 多段线实体: {polyline_count} 个")
            print(f"  原始线段数（分解后）: {original_segments}")

            # 创建线条合并器
            merger = DXFLineMerger(
                distance_threshold=5,  # 5mm连接阈值
                angle_threshold=2,     # 2度角度阈值
                enable_iterative=True, # 启用迭代合并
                max_iterations=3       # 最大3次迭代
            )

            # 执行合并测试（不显示详细过程）
            print("  正在执行线条合并...")
            merged_entities = merger.process_entities(line_entities.copy())

            # 统计合并后的线条数
            merged_line_entities = [e for e in merged_entities if e.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']]

            print(f"  合并后的线条数: {len(merged_line_entities)}")

            # 计算实际的合并效果
            reduction = original_segments - len(merged_line_entities)
            if reduction > 0:
                print(f"  ✅ 合并效果: {original_segments} → {len(merged_line_entities)} (减少了 {reduction} 条)")
                merge_rate = reduction / original_segments * 100
                print(f"  ✅ 合并率: {merge_rate:.1f}%")
            elif reduction == 0:
                print(f"  ⚪ 合并效果: {original_segments} → {len(merged_line_entities)} (无变化)")
                print(f"  ⚪ 合并率: 0.0%")
            else:
                print(f"  ⚠️  合并效果: {original_segments} → {len(merged_line_entities)} (增加了 {-reduction} 条)")
                print(f"  ⚠️  这可能是由于多段线分解导致的")

        except ImportError:
            print("🔧 墙体线条合并测试: 线条合并器模块未找到")
        except Exception as e:
            print(f"🔧 墙体线条合并测试失败: {e}")

class EnhancedCADApp:
    """增强版CAD标注应用"""
    
    def __init__(self, root):
        self.root = root
        self.processor = None
        self.visualizer = None
        self.canvas = None
        
        # GUI变量
        self.folder_var = StringVar()
        self.status_var = StringVar(value="就绪")
        self.progress_var = StringVar(value="等待开始...")
        self.stats_var = StringVar(value="文件: 0/0 | 组: 0/0 | 样本: 0")
        
        # 保存选项
        self.save_images_var = BooleanVar(value=False)
        
        # 状态
        self.current_label = ""

        # 处理阶段状态
        self.processing_stage = "none"  # none, basic, line, group, complete
        self.stage_data = {}  # 存储各阶段的处理数据

        # 墙体填充相关变量
        self.current_wall_fills = None
        self.current_wall_fill_processor = None

        # 初始化配色系统
        self.init_color_system()

        # 初始化阴影系统
        self.init_shadow_system()

        self.create_widgets()
        self.setup_shortcuts()

        # 延迟启用缩放功能（等待界面完全创建）
        self.root.after(1000, self._delayed_init_zoom)
    
    def create_widgets(self):
        """创建UI组件"""
        # 设置窗口
        self.root.title("CAD分类标注工具 - 增强版")
        self.root.geometry("1900x1200")
        
        # 主框架
        main_frame = Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = Frame(main_frame, width=400)
        control_frame.pack(side='left', fill='y', padx=(0, 10))
        
        self._create_file_selection(control_frame)  # 数据文件夹
        self._create_process_control(control_frame)  # 处理控制
        self._create_status_display(control_frame)  # 处理状态
        self._create_group_list(control_frame)  # 实体组列表
        self._create_category_buttons(control_frame)  # 选择类别
        self._create_action_buttons(control_frame)  # 操作控制
        self._create_save_options(control_frame)  # 保存选项
        
        # 右侧可视化区域
        viz_frame = Frame(main_frame)
        viz_frame.pack(side='right', fill='both', expand=True)
        
        self._create_visualization(viz_frame)
    
    def _create_file_selection(self, parent):
        """创建文件选择区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="数据文件夹:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        folder_entry = tk.Entry(frame, textvariable=self.folder_var, state='readonly')
        folder_entry.pack(fill='x', pady=(5, 0))
        
        Button(frame, text="选择文件夹", command=self.select_folder, 
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))
    
    def _create_process_control(self, parent):
        """创建处理控制区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))

        Label(frame, text="处理控制:", font=('Arial', 10, 'bold')).pack(anchor='w')

        # 第一行：三阶段处理按钮
        stage_frame = Frame(frame)
        stage_frame.pack(fill='x', pady=(5, 0))

        self.start_btn = Button(stage_frame, text="开始处理", command=self.start_processing,
                               bg='#2196F3', fg='white', font=('Arial', 9))
        self.start_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))

        self.line_process_btn = Button(stage_frame, text="线条处理", command=self.start_line_processing,
                                     bg='#4CAF50', fg='white', font=('Arial', 9), state='disabled')
        self.line_process_btn.pack(side='left', fill='x', expand=True, padx=(2, 2))

        self.group_process_btn = Button(stage_frame, text="识别分组", command=self.start_group_processing,
                                      bg='#FF9800', fg='white', font=('Arial', 9), state='disabled')
        self.group_process_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))

        # 第二行：控制按钮
        control_frame = Frame(frame)
        control_frame.pack(fill='x', pady=(5, 0))

        self.stop_btn = Button(control_frame, text="停止", command=self.stop_processing, state='disabled',
                              bg='#F44336', fg='white', font=('Arial', 9))
        self.stop_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))

        # 日志输出按钮
        self.log_export_btn = Button(control_frame, text="导出日志", command=self.export_log_manual,
                                   bg='#9C27B0', fg='white', font=('Arial', 9))
        self.log_export_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))

    def export_log_manual(self):
        """手动导出日志（基类默认实现）"""
        try:
            from tkinter import messagebox
            messagebox.showinfo("提示", "日志导出功能在增强版中可用")
        except:
            print("日志导出功能在增强版中可用")
    
    def _create_status_display(self, parent):
        """创建状态显示区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="处理状态:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 进度条
        self.progress_bar = ttk.Progressbar(frame, mode='determinate')
        self.progress_bar.pack(fill='x', pady=(5, 0))
        
        # 状态标签 - 实时更新
        status_label = Label(frame, textvariable=self.status_var, font=('Arial', 9), 
                            fg='blue', wraplength=350, justify='left')
        status_label.pack(anchor='w', fill='x')
        
        # 统计信息
        stats_label = Label(frame, textvariable=self.stats_var, font=('Arial', 9),
                           wraplength=350, justify='left')
        stats_label.pack(anchor='w', fill='x')
    
    def _create_category_buttons(self, parent):
        """创建类别按钮区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="选择类别:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 创建按钮网格
        button_frame = Frame(frame)
        button_frame.pack(fill='x', pady=(5, 0))
        
        categories = [
            ('wall', '墙体', '#FFA500'),
            ('door_window', '门窗', '#42A5F5'),
            ('railing', '栏杆', '#29B6F6'),
            ('furniture', '家具', '#4DB6AC'),
            ('bed', '床', '#80CBC4'),
            ('sofa', '沙发', '#26A69A'),
            ('cabinet', '柜子', '#00796B'),
            ('dining_table', '餐桌', '#B2DFDB'),
            ('appliance', '家电', '#607D8B'),
            ('stair', '楼梯', '#F9A825'),
            ('elevator', '电梯', '#F57F17'),
            ('dimension', '标注', '#FFCA28'),
            ('room_label', '房间标注', '#EF5350'),
            ('column', '柱子', '#5D4037'),
            ('other', '其他', '#BDBDBD')
        ]
        
        for i, (key, name, color) in enumerate(categories):
            row = i // 3
            col = i % 3
            
            btn = Button(button_frame, text=f"{i+1}. {name}", 
                        command=lambda k=key: self.select_category(k),
                        bg=color, fg='white', font=('Arial', 8))
            btn.grid(row=row, column=col, sticky='ew', padx=1, pady=1)
        
        # 配置列权重
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)
    
    def _create_action_buttons(self, parent):
        """创建操作按钮区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="操作控制:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        Button(frame, text="跳过当前组", command=self.skip_group,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(fill='x', pady=(2, 0))
        
        # 墙体填充按钮区域
        wall_fill_frame = Frame(frame)
        wall_fill_frame.pack(fill='x', pady=(2, 0))
        
        # 添加墙体自动填充按钮
        Button(wall_fill_frame, text="墙体自动填充", command=self.auto_fill_walls,
               bg='#2196F3', fg='white', font=('Arial', 9)).pack(side='left', fill='x', expand=True, padx=(0, 2))
        
        # 添加保存填充按钮
        Button(wall_fill_frame, text="保存填充", command=self.save_wall_fills,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(side='right', fill='x', expand=True, padx=(2, 0))

        # 添加清除填充按钮
        clear_fill_frame = Frame(frame)
        clear_fill_frame.pack(fill='x', pady=(2, 0))

        Button(clear_fill_frame, text="清除填充", command=self.clear_wall_fills,
               bg='#F44336', fg='white', font=('Arial', 9)).pack(fill='x')



        # 🔧 新增：缩放和保存功能
        zoom_frame = Frame(frame)
        zoom_frame.pack(fill='x', pady=(2, 0))

        # 缩放按钮行
        zoom_buttons_frame = Frame(zoom_frame)
        zoom_buttons_frame.pack(fill='x')

        Button(zoom_buttons_frame, text="放大", command=self.zoom_in,
               bg='#009688', fg='white', font=('Arial', 8), width=6).pack(side='left', padx=(0, 1))
        Button(zoom_buttons_frame, text="缩小", command=self.zoom_out,
               bg='#009688', fg='white', font=('Arial', 8), width=6).pack(side='left', padx=1)
        Button(zoom_buttons_frame, text="适应", command=self.zoom_fit,
               bg='#009688', fg='white', font=('Arial', 8), width=6).pack(side='left', padx=1)

        # 保存按钮
        save_frame = Frame(frame)
        save_frame.pack(fill='x', pady=(2, 0))

        Button(save_frame, text="保存图像", command=self.save_current_view,
               bg='#795548', fg='white', font=('Arial', 9)).pack(fill='x')
    
    def _create_save_options(self, parent):
        """创建保存选项区域"""
        frame = Frame(parent)
        frame.pack(fill='x', pady=(0, 15))
        
        Label(frame, text="保存选项:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 保存选项复选框
        Checkbutton(frame, text="同时保存分类图片", variable=self.save_images_var,
                   font=('Arial', 9)).pack(anchor='w', pady=(5, 0))
        
        # 保存按钮
        Button(frame, text="手动保存数据集", command=self.save_dataset,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))
        
        # 添加导出详细数据按钮
        Button(frame, text="导出组详细数据", command=self.export_group_details,
               bg='#FF9800', fg='white', font=('Arial', 9)).pack(fill='x', pady=(5, 0))

    def _create_color_system(self, parent):
        """创建配色系统区域（右下角版本）"""
        frame = LabelFrame(parent, text="配色系统 - 全图概览专用", font=('Arial', 9, 'bold'))
        frame.pack(fill='x', pady=(5, 0))

        # 第一行：配色方案选择和应用
        scheme_frame = Frame(frame)
        scheme_frame.pack(fill='x', padx=5, pady=2)

        Label(scheme_frame, text="配色方案:", font=('Arial', 8)).pack(side='left')

        # 配色方案下拉菜单
        self.color_scheme_var = StringVar(value="默认配色")
        self.color_scheme_combo = ttk.Combobox(scheme_frame, textvariable=self.color_scheme_var,
                                               font=('Arial', 8), width=12, state='readonly')
        self.color_scheme_combo.pack(side='left', padx=(5, 2))
        self.color_scheme_combo.bind('<<ComboboxSelected>>', self.on_color_scheme_selected)

        # 应用配色按钮
        Button(scheme_frame, text="应用配色", command=self.apply_color_scheme,
               bg='#4CAF50', fg='white', font=('Arial', 8)).pack(side='left', padx=2)

        # 🔧 新增：刷新按钮
        Button(scheme_frame, text="🔄 刷新", command=self.refresh_color_scheme,
               bg='#00BCD4', fg='white', font=('Arial', 8)).pack(side='left', padx=2)

        # 第二行：配色管理按钮
        btn_frame = Frame(frame)
        btn_frame.pack(fill='x', padx=5, pady=2)

        Button(btn_frame, text="配色设置", command=self.open_color_settings,
               bg='#9C27B0', fg='white', font=('Arial', 8)).pack(side='left', fill='x', expand=True, padx=(0, 1))

        Button(btn_frame, text="保存配色", command=self.save_color_scheme,
               bg='#607D8B', fg='white', font=('Arial', 8)).pack(side='left', fill='x', expand=True, padx=1)

        Button(btn_frame, text="导出文件", command=self.export_current_color_scheme,
               bg='#FF9800', fg='white', font=('Arial', 8)).pack(side='left', fill='x', expand=True, padx=1)

        Button(btn_frame, text="📁 导入文件", command=self.import_color_scheme_file,
               bg='#795548', fg='white', font=('Arial', 8)).pack(side='left', fill='x', expand=True, padx=(1, 0))

        # 初始化配色方案下拉菜单
        self.update_color_scheme_combo()

    def _create_group_list(self, parent):
        """创建实体组列表区域（带填充按钮）"""
        frame = Frame(parent)
        frame.pack(fill='both', expand=True, pady=(0, 0))

        Label(frame, text="实体组列表:", font=('Arial', 10, 'bold')).pack(anchor='w')

        # 添加状态说明
        help_text = "双击: 标注/重新分类 | 填充: 对组进行填充"
        Label(frame, text=help_text, font=('Arial', 8), fg='gray').pack(anchor='w')

        # 创建带滚动条的列表框
        list_frame = Frame(frame)
        list_frame.pack(fill='both', expand=True, pady=(5, 0))

        # 创建Treeview控件显示组信息
        columns = ('状态', '类型', '实体数', '填充')
        self.group_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings', height=10)

        # 设置列标题
        self.group_tree.heading('#0', text='组ID', anchor='w')
        self.group_tree.heading('状态', text='状态', anchor='center')
        self.group_tree.heading('类型', text='类型', anchor='center')
        self.group_tree.heading('实体数', text='实体数', anchor='center')
        self.group_tree.heading('填充', text='填充', anchor='center')

        # 设置列宽
        self.group_tree.column('#0', width=60, minwidth=50)
        self.group_tree.column('状态', width=60, minwidth=50)
        self.group_tree.column('类型', width=80, minwidth=60)
        self.group_tree.column('实体数', width=50, minwidth=40)
        self.group_tree.column('填充', width=60, minwidth=50)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.group_tree.yview)
        self.group_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.group_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件和右键菜单
        self.group_tree.bind('<Double-1>', self.on_group_double_click)
        self.group_tree.bind('<Button-3>', self.on_group_right_click)  # 右键菜单
        self.group_tree.bind('<Button-1>', self.on_group_click)  # 单击事件（处理填充列）
        
        # 配置状态颜色标签
        self.group_tree.tag_configure('unlabeled', foreground='red')
        self.group_tree.tag_configure('labeling', foreground='red')  # 标注中为红色
        self.group_tree.tag_configure('labeled', foreground='green')
        self.group_tree.tag_configure('auto_labeled', foreground='blue')
        self.group_tree.tag_configure('relabeled', foreground='purple')  # 重新分类的组用紫色
        self.group_tree.tag_configure('pending', foreground='brown')  # 待处理组用棕色
    
    def _create_visualization(self, parent):
        """创建可视化区域"""
        # 标题
        viz_title = Label(parent, text="CAD实体可视化 - 增强版", font=('Arial', 12, 'bold'))
        viz_title.pack(pady=(0, 5))

        # 创建上下分割的框架
        # 上部分：可视化画布
        canvas_frame = Frame(parent)
        canvas_frame.pack(fill='both', expand=True)

        # 下部分：配色系统
        color_frame = Frame(parent)
        color_frame.pack(fill='x', pady=(5, 0))

        # 创建可视化器和画布
        try:
            self.visualizer = CADVisualizer()
            self.canvas = FigureCanvasTkAgg(self.visualizer.get_figure(), canvas_frame)
            self.canvas.get_tk_widget().pack(fill='both', expand=True)

            # 🔧 修复：可视化器创建后立即应用配色方案
            if hasattr(self, 'current_color_scheme'):
                self.visualizer.update_color_scheme(self.current_color_scheme)

            # 🎨 新增：创建颜色索引显示
            self._create_color_legend(canvas_frame)

        except Exception as e:
            print(f"可视化初始化失败: {e}")
            Label(canvas_frame, text=f"可视化初始化失败: {e}").pack(fill='both', expand=True)

        # 在右下角创建配色系统
        self._create_color_system(color_frame)
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含DXF文件的文件夹")
        if folder:
            # 验证选择的路径
            if not os.path.exists(folder):
                messagebox.showerror("错误", f"选择的路径不存在: {folder}")
                return

            if not os.path.isdir(folder):
                messagebox.showerror("错误", f"选择的不是文件夹: {folder}")
                return

            # 预扫描检查是否有CAD文件
            cad_files = []
            try:
                for file_name in os.listdir(folder):
                    file_path = os.path.join(folder, file_name)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(file_name.lower())
                        if ext in ['.dxf', '.dwg']:
                            cad_files.append(file_name)
            except Exception as e:
                messagebox.showerror("错误", f"无法访问文件夹: {e}")
                return

            self.folder_var.set(folder)

            if cad_files:
                self.status_var.set(f"已选择文件夹: {os.path.basename(folder)} (找到 {len(cad_files)} 个CAD文件)")
            else:
                self.status_var.set(f"已选择文件夹: {os.path.basename(folder)} (未找到CAD文件)")
                messagebox.showwarning("警告", f"选择的文件夹中没有找到 .dxf 或 .dwg 文件\n\n文件夹: {folder}\n\n请确认文件夹中包含CAD文件。")
    
    def start_processing(self):
        """开始处理 - 第一阶段：基础数据加载"""
        folder = self.folder_var.get()
        if not folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.exists(folder):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return

        # 创建处理器
        self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
        self.processor.set_callbacks(self.on_status_update, self.on_progress_update)

        # 更新按钮状态
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.processing_stage = "basic"

        # 在后台线程中进行基础处理
        threading.Thread(target=self._process_basic_stage, args=(folder,), daemon=True).start()
    
    def stop_processing(self):
        """停止处理"""
        if self.processor:
            self.processor.stop_processing()

        self._reset_button_states()
        self.status_var.set("处理已停止")

    def _reset_button_states(self):
        """重置按钮状态"""
        self.start_btn.config(state='normal')
        self.line_process_btn.config(state='disabled')
        self.group_process_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.processing_stage = "none"

    def _process_basic_stage(self, folder):
        """第一阶段：基础数据加载和预处理"""
        try:
            self.status_var.set("阶段1：加载CAD文件...")

            # 执行基础数据加载（不包括线条处理和分组）
            success = self.processor.process_folder_basic(folder)

            if success:
                # 保存基础数据
                self.stage_data['basic'] = {
                    'entities': self.processor.raw_entities if hasattr(self.processor, 'raw_entities') else [],
                    'timestamp': time.time()
                }

                # 更新界面显示基础数据
                self._update_visualization_basic()

                # 启用线条处理按钮
                self.root.after(0, lambda: (
                    self.line_process_btn.config(state='normal'),
                    self.status_var.set("基础数据加载完成，可进行线条处理")
                ))
            else:
                self.root.after(0, lambda: (
                    self._reset_button_states(),
                    self.status_var.set("基础数据加载失败")
                ))

        except Exception as e:
            self.root.after(0, lambda: (
                self._reset_button_states(),
                self.status_var.set(f"基础处理失败: {str(e)}")
            ))

    def start_line_processing(self):
        """开始线条处理 - 第二阶段"""
        if self.processing_stage != "basic":
            messagebox.showwarning("警告", "请先完成基础数据加载")
            return

        self.line_process_btn.config(state='disabled')
        self.processing_stage = "line"

        # 在后台线程中进行线条处理
        threading.Thread(target=self._process_line_stage, daemon=True).start()

    def _process_line_stage(self):
        """第二阶段：线条处理和合并"""
        try:
            self.status_var.set("阶段2：处理线条合并...")

            # 执行线条处理
            success = self.processor.process_line_merging()

            # 无论成功与否，都保存数据并启用下一阶段
            if success:
                # 保存线条处理数据
                self.stage_data['line'] = {
                    'merged_entities': self.processor.merged_entities if hasattr(self.processor, 'merged_entities') else [],
                    'timestamp': time.time()
                }

                # 更新界面显示线条处理结果
                self._update_visualization_line()

                status_message = "线条处理完成，可进行识别分组"
            else:
                # 即使处理失败，也使用原始数据继续
                self.stage_data['line'] = {
                    'merged_entities': self.processor.raw_entities if hasattr(self.processor, 'raw_entities') else [],
                    'timestamp': time.time()
                }

                # 设置合并实体为原始实体，确保后续流程可以继续
                if hasattr(self.processor, 'raw_entities'):
                    self.processor.merged_entities = self.processor.raw_entities

                status_message = "线条处理完成（使用原始数据），可进行识别分组"

            # 无论成功与否，都启用分组处理按钮
            self.root.after(0, lambda: (
                self.group_process_btn.config(state='normal'),
                self.status_var.set(status_message)
            ))

        except Exception as e:
            # 即使出现异常，也尝试继续流程
            try:
                # 使用原始数据作为合并结果
                if hasattr(self.processor, 'raw_entities'):
                    self.processor.merged_entities = self.processor.raw_entities
                    self.stage_data['line'] = {
                        'merged_entities': self.processor.raw_entities,
                        'timestamp': time.time()
                    }

                self.root.after(0, lambda: (
                    self.group_process_btn.config(state='normal'),
                    self.status_var.set(f"线条处理异常但可继续: {str(e)}")
                ))
            except:
                # 如果完全失败，重置到线条处理阶段
                self.root.after(0, lambda: (
                    self.line_process_btn.config(state='normal'),
                    self.status_var.set(f"线条处理失败: {str(e)}")
                ))

    def start_group_processing(self):
        """开始识别分组 - 第三阶段"""
        if self.processing_stage != "line":
            messagebox.showwarning("警告", "请先完成线条处理")
            return

        self.group_process_btn.config(state='disabled')
        self.processing_stage = "group"

        # 在后台线程中进行分组处理
        threading.Thread(target=self._process_group_stage, daemon=True).start()

    def _process_group_stage(self):
        """第三阶段：识别分组"""
        try:
            self.status_var.set("阶段3：识别分组...")

            # 执行分组处理
            success = self.processor.process_grouping()

            if success:
                # 保存分组数据
                self.stage_data['group'] = {
                    'groups': self.processor.all_groups if hasattr(self.processor, 'all_groups') else [],
                    'timestamp': time.time()
                }

                # 更新界面显示分组结果
                self._update_visualization_group()

                # 启用其他操作按钮
                self.root.after(0, lambda: (
                    self._enable_post_processing_buttons(),
                    self.status_var.set("识别分组完成，可进行填充、房间识别等操作")
                ))
                self.processing_stage = "complete"
            else:
                self.root.after(0, lambda: (
                    self.group_process_btn.config(state='normal'),
                    self.status_var.set("识别分组失败")
                ))

        except Exception as e:
            self.root.after(0, lambda: (
                self.group_process_btn.config(state='normal'),
                self.status_var.set(f"识别分组失败: {str(e)}")
            ))

    def _enable_post_processing_buttons(self):
        """启用后处理按钮（填充、房间识别、类别选择等）"""
        # 这里可以启用其他操作按钮
        # 具体的按钮需要根据实际界面来确定
        pass

    def _update_visualization_basic(self):
        """更新基础数据可视化"""
        try:
            if self.visualizer and hasattr(self.processor, 'raw_entities'):
                # 显示原始实体数据
                self.visualizer.clear_all()
                self.visualizer.draw_entities(self.processor.raw_entities)
                if self.canvas:
                    self.canvas.draw()
                print("✅ 基础数据可视化更新完成")
        except Exception as e:
            print(f"⚠️ 基础数据可视化更新失败: {e}")

    def _update_visualization_line(self):
        """更新线条处理可视化"""
        try:
            if self.visualizer and hasattr(self.processor, 'merged_entities'):
                # 显示合并后的线条数据
                self.visualizer.clear_all()
                self.visualizer.draw_entities(self.processor.merged_entities)
                if self.canvas:
                    self.canvas.draw()
                print("✅ 线条处理可视化更新完成")
        except Exception as e:
            print(f"⚠️ 线条处理可视化更新失败: {e}")

    def _update_visualization_group(self):
        """更新分组可视化"""
        try:
            if self.visualizer and hasattr(self.processor, 'all_groups'):
                # 显示分组结果
                self.visualizer.clear_all()
                self.visualizer.draw_groups(self.processor.all_groups)
                if self.canvas:
                    self.canvas.draw()
                print("✅ 分组可视化更新完成")
        except Exception as e:
            print(f"⚠️ 分组可视化更新失败: {e}")

    def on_status_update(self, status_type, data):
        """状态更新回调"""
        if status_type == "info":
            self.status_var.set(data)
        
        elif status_type == "error":
            self.status_var.set(f"错误: {data}")
        
        elif status_type == "status":
            self.status_var.set(data)
        
        elif status_type == "file_start":
            file_index, total_files, filename = data
            self.status_var.set(f"正在处理文件 {file_index}/{total_files}: {filename}")
        
        elif status_type == "file_complete":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理完成: {filename}")
        
        elif status_type == "file_error":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理失败: {filename}")
        
        elif status_type == "auto_labeled":
            category, group_count, entity_count = data
            self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
        
        elif status_type == "manual_group":
            info = data
            self.status_var.set(f"手动标注 {info['index']}/{info['total']}: {info['entity_count']}个实体")
        
        elif status_type == "group_labeled":
            _, category_name, entity_count = data
            self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
        
        elif status_type == "group_skipped":
            self.status_var.set(data)
        
        elif status_type == "manual_complete":
            self.status_var.set(data)
            # 重新启用开始按钮
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 更新可视化，清除当前处理组的高亮显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    # 清空详细视图
                    self.processor.visualizer.ax_detail.clear()
                    self.processor.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成', 
                                                           ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                           fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                    self.processor.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                        processor=self.processor  # 🔑 修复：添加processor参数以启用条件4
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"完成时可视化更新失败: {e}")
        
        elif status_type == "completed":
            # 仅更新状态，不强制显示完成信息
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 让processor决定是否显示完成信息
            if self.processor:
                # 检查是否还有待处理组
                if not self.processor.pending_manual_groups:
                    self.processor._show_completion_message()
            
            # 更新组列表显示
            self.update_group_list()
            
            # 更新组列表显示
            self.update_group_list()
            
            # 注意：移除自动跳转逻辑，因为完成状态不应该跳转到其他组
            # 只有在手动分组完成时才检查是否需要跳转到其他未标注组
        
        elif status_type == "stopped":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 更新可视化，清除当前处理组的高亮显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    # 清空详细视图
                    self.processor.visualizer.ax_detail.clear()
                    self.processor.visualizer.ax_detail.text(0.5, 0.5, '处理已停止', 
                                                           ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                           fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                    self.processor.visualizer.ax_detail.set_title('CAD实体组预览 (已停止)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                        processor=self.processor  # 🔑 修复：添加processor参数以启用条件4
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"停止时可视化更新失败: {e}")
            
            # 更新组列表显示
            self.update_group_list()
        
        elif status_type == "update_group_list":
            # 更新组列表显示
            self.update_group_list()
        
        elif status_type == "force_update_group_list":
            # 强制更新组列表显示
            self.update_group_list()
        
        elif status_type == "group_relabeled":
            group_index, new_label = data
            # 安全获取类型映射
            if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                category_name = self.processor.category_mapping.get(new_label, new_label)
            else:
                category_name = new_label
            self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
        
        elif status_type == "auto_jump":
            self.status_var.set(f"自动跳转: {data}")
        
        elif status_type == "manual_complete":
            self.status_var.set(data)
            # 重新启用开始按钮
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            # 更新可视化，清除当前处理组的高亮显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    # 清空详细视图
                    self.processor.visualizer.ax_detail.clear()
                    self.processor.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成', 
                                                           ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                           fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                    self.processor.visualizer.ax_detail.set_title('CAD实体组预览 (已完成)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)
                    
                    # 更新全图概览，不显示当前处理组
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                        processor=self.processor  # 🔑 修复：添加processor参数以启用条件4
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"完成时可视化更新失败: {e}")
            
            # 更新组列表显示完成状态
            self.update_group_list()
        
        # 在相关状态更新时也更新组列表
        if status_type in ["manual_group", "group_labeled", "group_skipped", "manual_complete", "auto_labeled", "group_relabeled"]:
            self.update_group_list()
    
    def on_progress_update(self, current, total):
        """进度更新回调（线程安全版本）"""
        # 使用after方法确保在主线程中更新GUI
        self.root.after(0, self._update_progress_ui, current, total)

    def _update_progress_ui(self, current, total):
        """在主线程中更新进度UI"""
        try:
            if total > 0 and hasattr(self, 'progress_bar') and self.progress_bar.winfo_exists():
                progress = (current / total) * 100
                self.progress_bar['value'] = progress

                # 更新统计信息
                dataset_count = len(self.processor.dataset) if self.processor else 0
                if hasattr(self, 'stats_var'):
                    self.stats_var.set(f"文件: {current}/{total} | 样本: {dataset_count}")
        except Exception as e:
            print(f"更新进度UI失败: {e}")
    
    def select_category(self, category):
        """选择类别（修复版 - 正确检查待标注组状态）"""
        if self.processor:
            # 🔧 修复：强制更新待标注组列表
            self.processor._update_pending_manual_groups()

            # 检查是否有待标注的组
            has_pending_groups = (
                hasattr(self.processor, 'pending_manual_groups') and
                self.processor.pending_manual_groups and
                len(self.processor.pending_manual_groups) > 0
            )

            # 检查当前组索引是否有效
            current_index_valid = (
                hasattr(self.processor, 'current_manual_group_index') and
                self.processor.current_manual_group_index is not None and
                self.processor.current_manual_group_index < len(self.processor.pending_manual_groups)
            )

            print(f"🔍 类别选择检查: 手动模式={self.processor.manual_grouping_mode}, 待标注组={len(self.processor.pending_manual_groups) if has_pending_groups else 0}, 当前索引={self.processor.current_manual_group_index if hasattr(self.processor, 'current_manual_group_index') else 'None'}")

            # 检查是否在手动标注模式或有待标注组
            if self.processor.manual_grouping_mode and has_pending_groups and current_index_valid:
                success = self.processor.label_current_group(category)
                if success:
                    self.current_label = category
                    print(f"✅ 成功标注组为: {category}")
                else:
                    messagebox.showwarning("警告", "标注失败，当前没有可标注的组")
            elif has_pending_groups and not self.processor.manual_grouping_mode:
                # 有待标注组但不在手动模式，启动手动模式
                self.processor.manual_grouping_mode = True
                self.processor.current_manual_group_index = 0
                success = self.processor.label_current_group(category)
                if success:
                    self.current_label = category
                    print(f"✅ 启动手动模式并成功标注组为: {category}")
                else:
                    messagebox.showwarning("警告", "启动手动模式失败")
            else:
                # 没有待标注的组
                messagebox.showinfo("提示", f"当前没有待标注的组\n待标注组数量: {len(self.processor.pending_manual_groups) if has_pending_groups else 0}")
        else:
            messagebox.showinfo("提示", "请先开始处理文件")
    
    def skip_group(self):
        """跳过当前组"""
        if self.processor and self.processor.manual_grouping_mode:
            success = self.processor.skip_current_group()
            if not success:
                messagebox.showwarning("警告", "当前没有可跳过的组")
        else:
            messagebox.showinfo("提示", "请先开始处理文件")
    
    def auto_fill_walls(self):
        """墙体自动填充功能（V2版本 - 处理重叠、间隙和缺失端头）"""
        if not self.processor or not self.processor.current_file_entities:
            messagebox.showwarning("警告", "请先加载CAD文件")
            return

        try:
            # 创建V2墙体填充处理器
            try:
                from wall_fill_processor_enhanced_v2 import WallFillProcessorV2
                wall_fill_processor = WallFillProcessorV2()
            except ImportError:
                # 回退到旧版本
                from wall_fill_processor import WallFillProcessor
                wall_fill_processor = WallFillProcessor()
                messagebox.showwarning("警告", "使用旧版填充处理器，功能可能有限")

            # 获取当前文件的所有实体
            entities = self.processor.current_file_entities

            # 询问用户选择填充模式
            choice = messagebox.askyesno("选择填充模式",
                "是否使用交互式填充模式？\n\n"
                "是 - 交互式填充（逐步控制，更准确）\n"
                "否 - 自动填充（一键完成）")

            if choice:
                # 交互式填充模式
                self._start_interactive_fill(entities, wall_fill_processor)
            else:
                # 自动填充模式
                self._auto_fill_walls_impl(entities, wall_fill_processor)

        except Exception as e:
            error_msg = f"墙体填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)

    def _start_interactive_fill(self, entities, wall_fill_processor):
        """启动交互式填充"""
        try:
            from interactive_wall_fill_window import InteractiveWallFillWindow
            # 传递self作为parent，这样交互窗口就能访问主程序的方法和属性
            InteractiveWallFillWindow(
                self, entities, wall_fill_processor
            )
        except ImportError as e:
            messagebox.showerror("错误", f"无法启动交互式填充窗口: {e}")
            # 回退到自动填充
            self._auto_fill_walls_impl(entities, wall_fill_processor)

    def _auto_fill_walls_impl(self, entities, wall_fill_processor):
        """自动填充实现"""
        try:
            # 处理墙体填充
            if hasattr(wall_fill_processor, 'process_wall_filling_v2'):
                # V2版本处理器
                filled_groups = wall_fill_processor.process_wall_filling_v2(entities)
            else:
                # 旧版本处理器
                filled_groups = wall_fill_processor.process_wall_filling(entities, connection_threshold=20)

            if not filled_groups:
                messagebox.showinfo("提示", "未找到可填充的墙体组")
                return

            # 保存填充结果到实例变量
            self.current_wall_fills = filled_groups
            self.current_wall_fill_processor = wall_fill_processor

            # 更新可视化
            if self.visualizer:
                self._update_visualization_with_fills(filled_groups, wall_fill_processor)

            messagebox.showinfo("成功", f"已完成 {len(filled_groups)} 个墙体组的填充，点击'保存填充'将其应用到全图概览")

        except Exception as e:
            error_msg = f"墙体填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)

    def save_wall_fills(self):
        """保存墙体填充到全图概览"""
        if not hasattr(self, 'current_wall_fills') or not self.current_wall_fills:
            messagebox.showwarning("警告", "请先执行墙体自动填充")
            return
        
        try:
            # 更新全图概览显示填充
            if self.visualizer and self.processor:
                self._update_overview_with_fills()
            
            # 检查是否有未分类的实体组
            if self.processor and self.processor.has_unlabeled_groups():
                next_unlabeled_group = self.processor.get_next_unlabeled_group()
                if next_unlabeled_group:
                    # 跳转到第一个未分类的实体组
                    self.processor.jump_to_group(next_unlabeled_group)
                    messagebox.showinfo("提示", f"已跳转到第 {next_unlabeled_group} 个未分类实体组进行手动分类")
                else:
                    messagebox.showinfo("提示", "墙体填充已保存到全图概览")
            else:
                messagebox.showinfo("提示", "墙体填充已保存到全图概览，所有实体组已分类完成")
            
        except Exception as e:
            error_msg = f"保存填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _update_overview_with_fills(self):
        """更新全图概览显示填充"""
        if not self.visualizer or not self.processor:
            return
        
        try:
            print(f"更新全图概览，填充数据: {self.current_wall_fills is not None}")
            if self.current_wall_fills:
                print(f"填充组数量: {len(self.current_wall_fills)}")
            
            # 更新全图概览，包含填充效果
            self.visualizer.visualize_overview(
                self.processor.current_file_entities,
                None,  # 不显示当前处理组
                self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                wall_fills=self.current_wall_fills,  # 添加墙体填充
                wall_fill_processor=self.current_wall_fill_processor
            )
            self.visualizer.update_canvas(self.canvas)
            
            print("全图概览更新完成")
            
        except Exception as e:
            print(f"更新全图概览失败: {e}")
            import traceback
            traceback.print_exc()

    def clear_wall_fills(self):
        """清除墙体填充"""
        # 检查是否有填充信息
        if not hasattr(self, 'current_wall_fills') or not self.current_wall_fills:
            messagebox.showinfo("提示", "未找到填充信息")
            return

        # 弹出确认对话框
        result = messagebox.askyesno("确认清除",
            "确定要清除全图概览中保存的墙体填充吗？\n\n"
            "此操作将移除所有填充效果，但不会影响已标注的实体组。")

        if result:
            try:
                # 清除填充数据
                self.current_wall_fills = None
                self.current_wall_fill_processor = None

                # 更新全图概览，移除填充效果
                if self.visualizer and hasattr(self, 'all_groups') and self.all_groups:
                    current_group_index = getattr(self, 'current_group_index', 0)
                    # 重新显示全图概览，不包含填充
                    self.visualizer.show_overview(
                        self.all_groups,
                        current_group_index=current_group_index,
                        wall_fills=None  # 清除墙体填充
                    )
                    if self.canvas:
                        self.canvas.draw()

                messagebox.showinfo("成功", "墙体填充已清除")

            except Exception as e:
                error_msg = f"清除填充失败: {str(e)}"
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)

    def _jump_to_first_unlabeled_group(self, group_index):
        """跳转到第一个未分类的实体组"""
        try:
            print(f"开始跳转到组{group_index}...")
            
            # 检查处理器状态
            if not self.processor:
                print("错误: 处理器未初始化")
                return
            
            # 检查组索引是否有效
            if group_index < 1:
                print(f"错误: 无效的组索引 {group_index}")
                return
            
            # 跳转到指定组
            print(f"调用jump_to_group({group_index})...")
            self.processor.jump_to_group(group_index)
            
            print(f"跳转成功，显示提示信息...")
            # 显示提示信息
            messagebox.showinfo("提示", f"已跳转到第 {group_index} 个未分类实体组进行手动分类")
            
            print("跳转完成")
            
        except Exception as e:
            error_msg = f"跳转到未分类组失败: {str(e)}"
            print(f"跳转异常: {error_msg}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _check_and_jump_if_needed(self, expected_group_index):
        """检查并跳转（备用机制）"""
        try:
            print(f"备用检查：检查是否需要跳转到组{expected_group_index}")
            
            # 检查当前是否还在手动分组模式
            if self.processor and self.processor.manual_grouping_mode:
                print("当前仍在手动分组模式，跳转可能已成功")
                return
            
            # 检查是否还有未标注的组
            if self.processor and self.processor.has_unlabeled_groups():
                next_group = self.processor.get_next_unlabeled_group()
                if next_group == expected_group_index:
                    print(f"备用跳转：跳转到组{next_group}")
                    self._jump_to_first_unlabeled_group(next_group)
                else:
                    print(f"备用跳转：期望组{expected_group_index}，实际组{next_group}")
                    if next_group:
                        self._jump_to_first_unlabeled_group(next_group)
            else:
                print("备用检查：没有未标注的组")
                
        except Exception as e:
            print(f"备用检查异常: {e}")
            import traceback
            traceback.print_exc()
    

    
    def _update_visualization_with_fills(self, filled_groups, wall_fill_processor):
        """更新可视化以显示填充"""
        if not self.visualizer:
            return
        
        try:
            # 清除当前图形
            self.visualizer.ax_detail.clear()
            
            # 绘制原始实体
            if self.processor and self.processor.current_file_entities:
                for entity in self.processor.current_file_entities:
                    self.visualizer._draw_entity(entity, '#000000', 1, 1.0, self.visualizer.ax_detail)
            
            # 使用墙体填充处理器创建patches
            patches_list = wall_fill_processor.create_fill_patches(filled_groups)
            
            # 添加所有patches到图形
            for patch in patches_list:
                self.visualizer.ax_detail.add_patch(patch)
            
            # 更新画布
            self.visualizer.ax_detail.set_title('墙体填充预览', fontsize=12, fontproperties=self.visualizer.chinese_font)
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新可视化失败: {e}")
    
    def save_dataset(self):
        """保存数据集"""
        if not self.processor or not self.processor.dataset:
            messagebox.showwarning("警告", "没有可保存的数据")
            return
        
        # 选择保存目录
        output_dir = filedialog.askdirectory(title="选择保存目录")
        if not output_dir:
            return
        
        include_images = self.save_images_var.get()
        
        try:
            success, message = self.processor.save_dataset(include_images, output_dir)
            if success:
                messagebox.showinfo("成功", message)
                self.status_var.set("数据集保存成功")
            else:
                messagebox.showerror("错误", message)
                self.status_var.set(f"保存失败: {message}")
        except Exception as e:
            error_msg = f"保存失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def setup_shortcuts(self):
        """设置快捷键"""
        self.root.bind('<Control-s>', lambda e: self.save_dataset())
        self.root.bind('<Escape>', lambda e: self.stop_processing())
        
        # 数字键选择类别
        categories = ['wall', 'door_window', 'railing', 'furniture', 'bed', 'sofa', 'cabinet', 'dining_table', 'appliance', 'stair', 'elevator', 'dimension', 'room_label', 'column', 'other']
        for i, category in enumerate(categories):
            self.root.bind(str(i+1), lambda e, cat=category: self.select_category(cat))
        
        # 空格键跳过
        self.root.bind('<space>', lambda e: self.skip_group())
    
    def on_group_double_click(self, event):
        """处理组列表双击事件（修复版 - 支持类别选择对话框）"""
        selection = self.group_tree.selection()
        if selection:
            item = selection[0]
            group_id = self.group_tree.item(item, 'text')
            values = self.group_tree.item(item, 'values')
            status = values[0] if values else ""

            group_index = int(group_id.replace('组', ''))

            if status in ['已标注', '自动标注', '重新标注']:
                # 🔧 修复：已标注的组，显示类别选择对话框
                self._show_category_selection_dialog(group_index)
            elif status == '未标注':
                # 未标注的组，正常跳转
                if self.processor and hasattr(self.processor, 'jump_to_group'):
                    self.processor.jump_to_group(group_index)
            elif status == '标注中':
                # 当前正在标注的组，也显示类别选择对话框
                self._show_category_selection_dialog(group_index)

    def _show_category_selection_dialog(self, group_index):
        """显示类别选择对话框（新增方法）"""
        try:
            if not self.processor or not hasattr(self.processor, 'all_groups'):
                return

            if group_index < 0 or group_index >= len(self.processor.all_groups):
                return

            group = self.processor.all_groups[group_index]

            # 创建对话框
            dialog = tk.Toplevel(self.root)
            dialog.title(f"重新分类 - 组{group_index + 1}")
            dialog.geometry("400x500")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # 标题
            title_label = tk.Label(dialog, text=f"为组{group_index + 1}选择新的分类",
                                 font=('Arial', 12, 'bold'))
            title_label.pack(pady=10)

            # 当前分类信息
            current_label = "未知"
            if group and isinstance(group[0], dict):
                current_label = group[0].get('label', '未知')

            current_info = tk.Label(dialog, text=f"当前分类: {current_label}",
                                  font=('Arial', 10))
            current_info.pack(pady=5)

            # 分类选择区域
            category_frame = tk.Frame(dialog)
            category_frame.pack(fill='both', expand=True, padx=20, pady=20)

            tk.Label(category_frame, text="选择新的分类:", font=('Arial', 10, 'bold')).pack(anchor='w')

            # 分类按钮
            categories = [
                ('wall', '墙体', '#FFA500'),
                ('door_window', '门窗', '#42A5F5'),
                ('railing', '栏杆', '#29B6F6'),
                ('furniture', '家具', '#4DB6AC'),
                ('bed', '床', '#80CBC4'),
                ('sofa', '沙发', '#26A69A'),
                ('cabinet', '柜子', '#00796B'),
                ('dining_table', '餐桌', '#B2DFDB'),
                ('appliance', '家电', '#607D8B'),
                ('stair', '楼梯', '#F9A825'),
                ('elevator', '电梯', '#F57F17'),
                ('dimension', '标注', '#FFCA28'),
                ('room_label', '房间标注', '#EF5350'),
                ('column', '柱子', '#5D4037'),
                ('other', '其他', '#BDBDBD')
            ]

            def on_category_selected(category):
                """处理类别选择"""
                try:
                    # 更新组的标签
                    success = self._relabel_group(group_index, category)
                    if success:
                        dialog.destroy()
                        messagebox.showinfo("成功", f"组{group_index + 1}已重新分类为: {category}")

                        # 🔧 修复：更改后自动跳转到下一个待标注组
                        self._jump_to_next_unlabeled_group()
                    else:
                        messagebox.showerror("错误", "重新分类失败")
                except Exception as e:
                    print(f"类别选择处理失败: {e}")
                    messagebox.showerror("错误", f"处理失败: {e}")

            # 创建分类按钮（3列布局）
            for i, (cat_id, cat_name, color) in enumerate(categories):
                row = i // 3
                col = i % 3

                btn = tk.Button(category_frame, text=cat_name, bg=color, fg='white',
                              font=('Arial', 9, 'bold'), width=12, height=2,
                              command=lambda c=cat_id: on_category_selected(c))
                btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # 配置列权重
            for i in range(3):
                category_frame.grid_columnconfigure(i, weight=1)

            # 取消按钮
            cancel_btn = tk.Button(dialog, text="取消", command=dialog.destroy,
                                 font=('Arial', 10), width=10)
            cancel_btn.pack(pady=10)

        except Exception as e:
            print(f"显示类别选择对话框失败: {e}")
            import traceback
            traceback.print_exc()

    def _relabel_group(self, group_index, new_category):
        """重新标注组（新增方法）"""
        try:
            if not self.processor or not hasattr(self.processor, 'all_groups'):
                return False

            if group_index < 0 or group_index >= len(self.processor.all_groups):
                return False

            group = self.processor.all_groups[group_index]

            # 更新组中所有实体的标签
            for entity in group:
                if isinstance(entity, dict):
                    entity['label'] = new_category
                    # 如果是重新标注，标记为手动标注
                    if entity.get('auto_labeled', False):
                        entity['auto_labeled'] = False

            # 更新组状态信息
            if hasattr(self.processor, 'groups_info') and group_index < len(self.processor.groups_info):
                self.processor.groups_info[group_index]['label'] = new_category
                self.processor.groups_info[group_index]['status'] = 'labeled'

            # 更新组列表显示
            self.update_group_list()

            print(f"✅ 组{group_index + 1}重新标注为: {new_category}")
            return True

        except Exception as e:
            print(f"重新标注组失败: {e}")
            return False

    def _jump_to_next_unlabeled_group(self):
        """跳转到下一个待标注组（新增方法）"""
        try:
            if not self.processor:
                return

            # 更新待标注组列表
            self.processor._update_pending_manual_groups()

            if self.processor.pending_manual_groups:
                # 有待标注组，跳转到第一个
                self.processor.manual_grouping_mode = True
                self.processor.current_manual_group_index = 0
                first_pending_group = self.processor.pending_manual_groups[0]

                # 找到组在all_groups中的索引
                if first_pending_group in self.processor.all_groups:
                    group_index = self.processor.all_groups.index(first_pending_group) + 1
                    self.processor.jump_to_group(group_index)
                    print(f"✅ 自动跳转到下一个待标注组: 组{group_index}")
                else:
                    print("⚠️ 无法找到待标注组在列表中的位置")
            else:
                # 没有待标注组，清除高亮
                print("✅ 所有组已标注完成，清除高亮显示")
                if self.processor.visualizer and self.processor.canvas:
                    # 显示完成状态的概览图
                    current_file_entities = getattr(self.processor, 'current_file_entities', [])
                    auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
                    labeled_entities = getattr(self.processor, 'labeled_entities', [])

                    if current_file_entities:
                        self.processor.visualizer.visualize_overview(
                            current_file_entities,
                            None,  # 不高亮任何组
                            auto_labeled_entities + labeled_entities,
                            processor=self.processor,
                            current_group_index=None
                        )
                        self.processor.visualizer.update_canvas(self.processor.canvas)

        except Exception as e:
            print(f"跳转到下一个待标注组失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_group_right_click(self, event):
        """处理组列表右键菜单"""
        # 选择右键点击的项目
        item = self.group_tree.identify_row(event.y)
        if item:
            self.group_tree.selection_set(item)
            
            # 获取组信息
            group_id = self.group_tree.item(item, 'text')
            values = self.group_tree.item(item, 'values')
            status = values[0] if values else ""
            
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            group_index = int(group_id.replace('组', ''))
            
            if status in ['已标注', '自动标注', '标注中', '重新标注']:
                context_menu.add_command(label="重新分类", 
                                       command=lambda: self.start_relabel_mode(group_index))
            
            if status == '未标注':
                context_menu.add_command(label="开始标注", 
                                       command=lambda: self.processor.jump_to_group(group_index) if self.processor else None)
            
            context_menu.add_separator()
            context_menu.add_command(label="查看详情", 
                                   command=lambda: self.show_group_details(group_index))
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def _scroll_to_first_unlabeled_group(self):
        """滚动到第一个未标注组，使其显示在第四行（如果行数不够则显示在底部）"""
        try:
            if not hasattr(self, 'group_tree') or not self.group_tree:
                return

            # 初始化滚动状态管理
            if not hasattr(self, '_scroll_state'):
                self._scroll_state = {
                    'scrolling': False,
                    'last_target_group': None
                }

            # 检查是否正在滚动中
            if self._scroll_state['scrolling']:
                return

            # 查找第一个未标注组
            first_unlabeled_item = None
            first_unlabeled_index = -1
            all_items = self.group_tree.get_children()

            for i, item in enumerate(all_items):
                values = self.group_tree.item(item, 'values')
                if values and len(values) > 0:
                    status = values[0]
                    # 检查是否为未标注状态
                    if status in ['未标注', '标注中', '待处理']:
                        first_unlabeled_item = item
                        first_unlabeled_index = i
                        break

            if first_unlabeled_item:
                # 获取组ID
                group_id = self.group_tree.item(first_unlabeled_item, 'text')

                # 检查是否与上次滚动的目标相同
                if self._scroll_state['last_target_group'] == group_id:
                    return  # 避免重复滚动到同一个组

                # 标记正在滚动
                self._scroll_state['scrolling'] = True
                self._scroll_state['last_target_group'] = group_id

                try:
                    # 计算滚动位置：让第一个未标注组显示在第四行
                    target_position = max(0, first_unlabeled_index - 3)  # 第四行位置

                    # 如果后面行数不够，则从倒数第一组开始往上排列
                    total_items = len(all_items)
                    if first_unlabeled_index + 4 > total_items:  # 后面行数不够
                        # 让最后一组显示在底部，往上排列
                        target_position = max(0, total_items - 10)  # 假设可见行数为10

                    # 滚动到目标位置
                    if target_position < len(all_items):
                        target_item = all_items[target_position]
                        self.group_tree.see(target_item)

                    # 选中第一个未标注组以突出显示
                    self.group_tree.selection_set(first_unlabeled_item)
                    self.group_tree.focus(first_unlabeled_item)

                    print(f"📍 自动滚动到第一个未标注组: {group_id}")
                finally:
                    # 确保滚动标记被清除
                    self._scroll_state['scrolling'] = False
            else:
                # 没有未标注组，滚动到组1
                if self._scroll_state['last_target_group'] != '组1':
                    first_item = self.group_tree.get_children()
                    if first_item:
                        self._scroll_state['scrolling'] = True
                        try:
                            self.group_tree.see(first_item[0])
                            self._scroll_state['last_target_group'] = '组1'
                            print("📍 所有组已标注，滚动到组1")
                        finally:
                            self._scroll_state['scrolling'] = False

        except Exception as e:
            print(f"滚动到未标注组失败: {e}")

    def update_group_list(self):
        """更新实体组列表显示"""
        if not self.processor:
            return
        
        # 清空现有列表
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)
        
        # 获取所有组的信息
        groups_info = self.processor.get_groups_info()

        # 保持原始顺序，不改变排序
        for i, group_info in enumerate(groups_info):
            group_id = f"组{i+1}"
            status = group_info['status']
            group_type = group_info.get('group_type', '')
            entity_count = group_info['entity_count']
            
            # 根据状态选择标签和显示文本
            if status == 'auto_labeled':
                status_text = '自动标注'
                tag = 'auto_labeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'labeled':
                status_text = '已标注'
                tag = 'labeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'relabeled':
                status_text = '重新标注'
                tag = 'relabeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'pending':  # 新增待处理状态
                status_text = '待处理'
                tag = 'pending'
                type_text = '待标注'
            elif status == 'labeling': # Existing logic, but now correctly fed by _update_groups_info
                status_text = '标注中'
                tag = 'labeling'
                type_text = '待标注'
            else:
                status_text = '未标注'
                tag = 'unlabeled'
                type_text = '待标注'
            
            # 确定填充状态
            fill_status = self._get_group_fill_status(i)

            # 确定填充状态的标签
            fill_tag = self._get_fill_tag(i, fill_status)

            # 插入到列表中
            item_id = self.group_tree.insert('', 'end', text=group_id,
                                            values=(status_text, type_text, entity_count, fill_status),
                                            tags=(tag,))

            # 为填充列设置特殊的颜色标记
            self._set_fill_column_color(item_id, i, fill_status)

        # 更新完成后，自动滚动到第一个未标注组（逻辑控制）
        # 只有在不是由滚动触发的更新时才进行滚动
        if not hasattr(self, '_updating_from_scroll') or not self._updating_from_scroll:
            self._scroll_to_first_unlabeled_group()
    
    def start_relabel_mode(self, group_index):
        """开始重新分类模式"""
        if not self.processor:
            messagebox.showwarning("警告", "请先开始处理文件")
            return
        
        # 检查是否有可重新分类的组
        if not self.processor.can_relabel_group(group_index):
            messagebox.showwarning("警告", "该组无法重新分类")
            return
        
        # 进入重新分类模式
        success = self.processor.start_relabel_group(group_index)
        if success:
            # 更新状态显示
            self.status_var.set(f"正在重新分类第 {group_index} 组")
            
            # 创建分类选择对话框
            self.show_relabel_dialog(group_index)
        else:
            messagebox.showerror("错误", "无法进入重新分类模式")
    
    def show_relabel_dialog(self, group_index):
        """显示重新分类对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"重新分类 - 组{group_index}")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 获取组信息
        group_info = self.processor.get_group_info(group_index)
        
        # 标题
        title_label = Label(dialog, text=f"重新分类 - 组{group_index}", 
                           font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)
        
        # 组信息
        info_frame = Frame(dialog)
        info_frame.pack(fill='x', padx=20, pady=5)
        
        Label(info_frame, text=f"实体数量: {group_info['entity_count']}", 
              font=('Arial', 10)).pack(anchor='w')
        Label(info_frame, text=f"当前分类: {group_info.get('current_type', '未知')}", 
              font=('Arial', 10)).pack(anchor='w')
        
        # 分类选择区域
        category_frame = Frame(dialog)
        category_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        Label(category_frame, text="选择新的分类:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 分类按钮
        categories = [
            ('wall', '墙体', '#FFA500'),
            ('door_window', '门窗', '#42A5F5'),
            ('railing', '栏杆', '#29B6F6'),
            ('furniture', '家具', '#4DB6AC'),
            ('bed', '床', '#80CBC4'),
            ('sofa', '沙发', '#26A69A'),
            ('cabinet', '柜子', '#00796B'),
            ('dining_table', '餐桌', '#B2DFDB'),
            ('appliance', '家电', '#607D8B'),
            ('stair', '楼梯', '#F9A825'),
            ('elevator', '电梯', '#F57F17'),
            ('dimension', '标注', '#FFCA28'),
            ('room_label', '房间标注', '#EF5350'),
            ('column', '柱子', '#5D4037'),
            ('other', '其他', '#BDBDBD')
        ]
        
        button_frame = Frame(category_frame)
        button_frame.pack(fill='x', pady=10)
        
        for i, (key, name, color) in enumerate(categories):
            row = i // 3
            col = i % 3
            
            btn = Button(button_frame, text=name, 
                        command=lambda k=key: self.apply_relabel(group_index, k, dialog),
                        bg=color, fg='white', font=('Arial', 9), width=10)
            btn.grid(row=row, column=col, sticky='ew', padx=2, pady=2)
        
        # 配置列权重
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)
        
        # 按钮区域
        btn_frame = Frame(dialog)
        btn_frame.pack(fill='x', padx=20, pady=10)
        
        Button(btn_frame, text="取消", command=dialog.destroy,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(side='right', padx=(5, 0))
    
    def apply_relabel(self, group_index, new_label, dialog):
        """应用重新分类"""
        if not self.processor:
            return
        
        # 执行重新分类
        success = self.processor.relabel_group(group_index, new_label)
        
        if success:
            # 安全获取类型映射
            if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                category_name = self.processor.category_mapping.get(new_label, new_label)
            else:
                category_name = new_label
            self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
            
            # 关闭对话框
            dialog.destroy()
            
            # 检查是否还有未完成的分类
            self.check_and_continue_labeling()
            
            # 更新组列表
            self.update_group_list()
            
            # 更新可视化显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        [],  # 没有当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities,
                        processor=self.processor  # 🔑 修复：添加processor参数以启用条件4
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"重新分类后可视化更新失败: {e}")
        else:
            messagebox.showerror("错误", "重新分类失败")
    
    def check_and_continue_labeling(self):
        """检查并继续标注未完成的组"""
        if not self.processor:
            return
        
        # 如果还有未完成的标注，自动跳转到下一个
        if self.processor.has_unlabeled_groups():
            next_group = self.processor.get_next_unlabeled_group()
            if next_group is not None:
                self.processor.jump_to_group(next_group)
                self.status_var.set(f"自动跳转到下一个未标注组: 组{next_group}")
        else:
            self.status_var.set("所有组已标注完成")
    
    def show_group_details(self, group_index):
        """显示组详情"""
        if not self.processor:
            return
        
        group_info = self.processor.get_group_info(group_index)
        
        details = f"""组{group_index} 详细信息:
        
实体数量: {group_info['entity_count']}
当前状态: {group_info['status']}
分类类型: {group_info.get('current_type', '未知')}
图层信息: {', '.join(group_info.get('layers', []))}
边界框: {group_info.get('bbox_info', '未知')}
        """
        
        messagebox.showinfo(f"组{group_index}详情", details)
    
    def export_group_details(self):
        """导出组详细数据"""
        if not self.processor or not self.processor.all_groups:
            messagebox.showwarning("警告", "没有可导出的组数据")
            return
        
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("导出组详细数据")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 标题
        title_label = Label(dialog, text="选择要导出的组", font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)
        
        # 说明
        info_label = Label(dialog, text="输入组索引（如：1,3,4 或留空导出所有组）", font=('Arial', 10))
        info_label.pack(pady=5)
        
        # 输入框
        entry_var = StringVar()
        entry = Entry(dialog, textvariable=entry_var, font=('Arial', 10), width=30)
        entry.pack(pady=10)
        
        # 按钮框架
        btn_frame = Frame(dialog)
        btn_frame.pack(pady=10)
        
        def do_export():
            input_text = entry_var.get().strip()
            
            if not input_text:
                # 导出所有组
                success, message = self.processor.export_group_details()
            else:
                try:
                    # 解析输入的组索引
                    group_indices = [int(x.strip()) for x in input_text.split(',')]
                    success, message = self.processor.export_specific_groups(group_indices)
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的组索引，如：1,3,4")
                    return
            
            if success:
                messagebox.showinfo("成功", message)
                self.status_var.set("组详细数据导出成功")
            else:
                messagebox.showerror("错误", message)
                self.status_var.set(f"导出失败: {message}")
            
            dialog.destroy()
        
        Button(btn_frame, text="导出", command=do_export,
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(side='left', padx=(0, 5))
        
        Button(btn_frame, text="取消", command=dialog.destroy,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(side='left')

    def init_color_system(self):
        """初始化配色系统"""
        # 默认配色方案（增强版 - 包含填充色、阴影色等完整颜色体系）
        self.default_color_scheme = {
            # 基础颜色
            'background': '#FFFFFF',
            'wall': '#8B4513',        # 墙体线条色
            'door_window': '#FF0000', # 门窗线条色
            'railing': '#00FF00',     # 栏杆线条色
            'furniture': '#0000FF',   # 家具线条色
            'bed': '#FF00FF',         # 床线条色
            'sofa': '#FFFF00',        # 沙发线条色
            'cabinet': '#00FFFF',     # 柜子线条色
            'dining_table': '#800080', # 餐桌线条色
            'appliance': '#FFA500',   # 电器线条色
            'stair': '#808080',       # 楼梯线条色
            'elevator': '#800000',    # 电梯线条色
            'dimension': '#008000',   # 尺寸标注色
            'room_label': '#000080',  # 房间标签色
            'column': '#808000',      # 柱子线条色
            'other': '#4169E1',       # 其他线条色

            # 🔧 新增：组状态颜色
            'unlabeled': '#D3D3D3',   # 未标注组颜色（浅灰色）
            'pending': '#FFB6C1',     # 待处理组颜色（浅粉色）
            'highlight': '#FF0000',   # 高亮颜色（红色）

            # 🎨 新增：墙体填充颜色
            'wall_fill': '#F5DEB3',   # 墙体填充色（小麦色）

            # 🏠 新增：各类型房间填充颜色
            'living_room_fill': '#FFE4E1',    # 客厅填充色（薄雾玫瑰）
            'bedroom_fill': '#E6E6FA',        # 卧室填充色（薰衣草）
            'kitchen_fill': '#F0FFF0',        # 厨房填充色（蜜瓜色）
            'bathroom_fill': '#F0F8FF',       # 卫生间填充色（爱丽丝蓝）
            'dining_room_fill': '#FFF8DC',    # 餐厅填充色（玉米丝色）
            'study_room_fill': '#F5F5DC',     # 书房填充色（米色）
            'balcony_fill': '#F0FFFF',        # 阳台填充色（天蓝色）
            'corridor_fill': '#FFFAF0',       # 走廊填充色（花白色）
            'storage_fill': '#F8F8FF',        # 储藏室填充色（幽灵白）
            'other_room_fill': '#FAFAFA',     # 其他房间填充色（白烟色）

            # 🌫️ 新增：各图层阴影颜色
            'wall_shadow': '#696969',         # 墙体阴影色（暗灰色）
            'door_window_shadow': '#8B0000',  # 门窗阴影色（暗红色）
            'furniture_shadow': '#000080',    # 家具阴影色（海军蓝）
            'column_shadow': '#556B2F',       # 柱子阴影色（暗橄榄绿）
            'stair_shadow': '#2F4F4F',        # 楼梯阴影色（暗石板灰）
            'other_shadow': '#708090',        # 其他阴影色（石板灰）
            'other_lines': '#808080',  # 添加其他线条颜色
            'processing_lines': '#FF8C00',  # 添加处理过程中的线条颜色（橙色）
            'fill': '#E0E0E0',
            'text': '#000000',
            'current_group': '#FF0000',
            'labeled_group': '#00FF00',
            # 添加所有类型的填充颜色
            'wall_fill': '#F0F0F0',
            'door_window_fill': '#FFE0E0',
            'railing_fill': '#E0FFE0',
            'furniture_fill': '#E0E0FF',
            'bed_fill': '#FFE0FF',
            'sofa_fill': '#FFFFE0',
            'cabinet_fill': '#E0FFFF',
            'dining_table_fill': '#F0E0F0',
            'appliance_fill': '#FFF0E0',
            'stair_fill': '#F0F0F0',
            'elevator_fill': '#F0E0E0',
            'dimension_fill': '#E0F0E0',
            'room_label_fill': '#E0E0F0',
            'column_fill': '#F0F0E0',
            'other_fill': '#F0F0F0'
        }

        # 当前配色方案
        self.current_color_scheme = self.default_color_scheme.copy()

        # 配色方案列表
        self.color_schemes = {
            '默认配色': self.default_color_scheme.copy()
        }

        # 配色文件目录
        self.color_schemes_dir = "color_schemes"
        if not os.path.exists(self.color_schemes_dir):
            os.makedirs(self.color_schemes_dir)

        # 加载已保存的配色方案
        self.load_saved_color_schemes()

    def load_saved_color_schemes(self):
        """加载已保存的配色方案"""
        try:
            for filename in os.listdir(self.color_schemes_dir):
                if filename.endswith('.txt'):
                    scheme_name = filename[:-4]  # 移除.txt扩展名
                    filepath = os.path.join(self.color_schemes_dir, filename)
                    scheme = self.load_color_scheme_from_file(filepath)
                    if scheme:
                        self.color_schemes[scheme_name] = scheme
        except Exception as e:
            print(f"加载配色方案失败: {e}")

    def load_color_scheme_from_file(self, filepath):
        """从文件加载配色方案"""
        try:
            scheme = {}
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '=' in line:
                        key, value = line.split('=', 1)
                        scheme[key.strip()] = value.strip()

            # 验证配色方案是否包含必要的颜色
            required_colors = set(self.default_color_scheme.keys())
            scheme_colors = set(scheme.keys())

            if required_colors.issubset(scheme_colors):
                return scheme
            else:
                missing = required_colors - scheme_colors
                print(f"配色方案缺少颜色: {missing}")
                return None

        except Exception as e:
            print(f"读取配色文件失败: {e}")
            return None

    def refresh_color_scheme(self):
        """刷新配色方案（检测新的实体类型并添加到配色方案）"""
        try:
            print("🔄 开始刷新配色方案...")

            # 检查当前文件中的实体类型
            if not hasattr(self, 'processor') or not self.processor:
                messagebox.showwarning("警告", "请先加载DXF文件")
                return

            # 获取当前文件中的所有实体
            all_entities = []
            if hasattr(self.processor, 'current_file_entities'):
                all_entities = self.processor.current_file_entities
            elif hasattr(self.processor, 'all_groups'):
                for group in self.processor.all_groups:
                    if isinstance(group, list):
                        all_entities.extend(group)

            if not all_entities:
                messagebox.showinfo("信息", "当前文件中没有实体")
                return

            # 收集所有标签和图层
            found_labels = set()
            found_layers = set()

            for entity in all_entities:
                # 收集标签
                label = entity.get('label')
                if label and label not in ['None', 'none', '']:
                    found_labels.add(label)

                # 收集图层
                layer = entity.get('layer')
                if layer:
                    found_layers.add(layer.lower())

            # 检查配色方案中缺失的颜色
            new_colors_added = 0

            # 为新标签添加颜色
            for label in found_labels:
                if label not in self.current_color_scheme:
                    # 为新标签分配颜色
                    new_color = self._generate_new_color(label)
                    self.current_color_scheme[label] = new_color
                    new_colors_added += 1
                    print(f"  ✅ 为标签 '{label}' 添加颜色: {new_color}")

            # 为特殊图层添加颜色（如果还没有对应的标签）
            layer_mappings = {
                'wall': 'wall',
                'door': 'door_window',
                'window': 'door_window',
                'furniture': 'furniture',
                'column': 'column',
                'stair': 'stair',
                'elevator': 'elevator'
            }

            for layer in found_layers:
                for keyword, category in layer_mappings.items():
                    if keyword in layer and category not in found_labels:
                        if f"layer_{layer}" not in self.current_color_scheme:
                            new_color = self.current_color_scheme.get(category, self._generate_new_color(layer))
                            self.current_color_scheme[f"layer_{layer}"] = new_color
                            new_colors_added += 1
                            print(f"  ✅ 为图层 '{layer}' 添加颜色: {new_color}")
                        break

            # 更新可视化器的配色方案
            if self.visualizer:
                self.visualizer.update_color_scheme(self.current_color_scheme)

            # 应用配色方案
            self.apply_color_scheme()

            if new_colors_added > 0:
                messagebox.showinfo("刷新完成", f"配色方案已刷新！\n新增了 {new_colors_added} 种颜色")
            else:
                messagebox.showinfo("刷新完成", "配色方案已是最新，无需添加新颜色")

        except Exception as e:
            print(f"❌ 刷新配色方案失败: {e}")
            messagebox.showerror("错误", f"刷新配色方案失败: {e}")

    def _generate_new_color(self, name):
        """为新的实体类型生成颜色"""
        # 预定义的颜色池
        color_pool = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ]

        # 根据名称哈希选择颜色
        import hashlib
        hash_value = int(hashlib.md5(name.encode()).hexdigest(), 16)
        color_index = hash_value % len(color_pool)

        return color_pool[color_index]

    def import_color_scheme_file(self):
        """导入配色方案文件（增强版 - 缺失颜色设为灰色）"""
        try:
            from tkinter import filedialog

            # 选择文件
            file_path = filedialog.askopenfilename(
                title="导入配色方案文件",
                filetypes=[
                    ("JSON文件", "*.json"),
                    ("配色文件", "*.color"),
                    ("所有文件", "*.*")
                ],
                initialdir=os.path.dirname(os.path.abspath(__file__))
            )

            if not file_path:
                return

            # 读取文件
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_scheme = json.load(f)

            if not isinstance(imported_scheme, dict):
                messagebox.showerror("错误", "配色方案文件格式不正确")
                return

            # 验证配色方案
            valid_colors = 0
            invalid_colors = []
            for key, value in imported_scheme.items():
                if isinstance(value, str) and value.startswith('#') and len(value) == 7:
                    valid_colors += 1
                else:
                    invalid_colors.append(key)

            if valid_colors == 0:
                messagebox.showerror("错误", "配色方案文件中没有有效的颜色定义")
                return

            # 🔧 新功能：获取所有应该存在的颜色类型
            all_required_colors = set(self.default_color_scheme.keys())
            imported_colors = set(imported_scheme.keys())

            # 找出缺失的颜色类型
            missing_colors = all_required_colors - imported_colors

            # 🎨 为缺失的颜色设置灰色
            gray_color = '#808080'  # 标准灰色
            for missing_color in missing_colors:
                imported_scheme[missing_color] = gray_color

            # 更新当前配色方案
            original_count = len(self.current_color_scheme)
            self.current_color_scheme.update(imported_scheme)
            new_count = len(self.current_color_scheme)

            # 更新可视化器
            if self.visualizer:
                self.visualizer.update_color_scheme(self.current_color_scheme)

            # 应用配色方案
            self.apply_color_scheme()

            # 显示导入结果
            imported_valid_colors = len([k for k, v in imported_scheme.items() if v != gray_color])
            gray_colors_count = len(missing_colors)

            result_msg = f"配色方案导入成功！\n\n"
            result_msg += f"📊 导入统计:\n"
            result_msg += f"• 从文件导入: {imported_valid_colors} 种颜色\n"
            result_msg += f"• 缺失设为灰色: {gray_colors_count} 种颜色\n"
            result_msg += f"• 总颜色数: {new_count} 种\n\n"

            if invalid_colors:
                result_msg += f"⚠️ 跳过无效颜色: {', '.join(invalid_colors[:5])}"
                if len(invalid_colors) > 5:
                    result_msg += f" 等{len(invalid_colors)}个"
                result_msg += "\n\n"

            if missing_colors:
                result_msg += f"🔧 设为灰色的类型:\n"
                missing_list = list(missing_colors)[:8]  # 只显示前8个
                result_msg += f"• {', '.join(missing_list)}"
                if len(missing_colors) > 8:
                    result_msg += f" 等{len(missing_colors)}个"

            messagebox.showinfo("导入成功", result_msg)

            print(f"✅ 配色方案导入成功: {file_path}")
            print(f"   有效导入: {imported_valid_colors} 个")
            print(f"   设为灰色: {gray_colors_count} 个")
            print(f"   无效跳过: {len(invalid_colors)} 个")

        except FileNotFoundError:
            messagebox.showerror("错误", "配色方案文件不存在")
        except json.JSONDecodeError:
            messagebox.showerror("错误", "配色方案文件格式错误，请检查JSON格式")
        except Exception as e:
            print(f"❌ 导入配色方案失败: {e}")
            messagebox.showerror("错误", f"导入配色方案失败: {e}")

    def _create_color_legend(self, parent):
        """创建颜色索引显示"""
        try:
            # 创建颜色索引框架（右侧固定位置）
            legend_frame = Frame(parent, bg='white', relief='solid', bd=1)
            legend_frame.place(relx=0.98, rely=0.02, anchor='ne', width=150, height=200)

            # 标题
            title_label = Label(legend_frame, text="颜色索引", font=('Arial', 9, 'bold'), bg='white')
            title_label.pack(pady=(5, 2))

            # 创建滚动区域
            canvas = Canvas(legend_frame, bg='white', highlightthickness=0)
            scrollbar = Scrollbar(legend_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = Frame(canvas, bg='white')

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True, padx=5)
            scrollbar.pack(side="right", fill="y")

            # 存储引用以便更新
            self.color_legend_frame = scrollable_frame
            self.color_legend_canvas = canvas

            # 初始更新颜色索引
            self._update_color_legend()

        except Exception as e:
            print(f"创建颜色索引失败: {e}")

    def _update_color_legend(self):
        """更新颜色索引显示"""
        try:
            if not hasattr(self, 'color_legend_frame'):
                return

            # 清除现有内容
            for widget in self.color_legend_frame.winfo_children():
                widget.destroy()

            # 获取当前使用的颜色
            active_colors = self._get_active_colors()

            if not active_colors:
                Label(self.color_legend_frame, text="暂无颜色",
                     font=('Arial', 8), bg='white', fg='gray').pack(pady=5)
                return

            # 显示颜色项目
            for category, color in active_colors.items():
                color_frame = Frame(self.color_legend_frame, bg='white')
                color_frame.pack(fill='x', pady=1, padx=2)

                # 颜色方块
                color_label = Label(color_frame, text="  ", bg=color,
                                  relief='solid', bd=1, width=3)
                color_label.pack(side='left', padx=(0, 5))

                # 类别名称
                name_label = Label(color_frame, text=self._get_category_display_name(category),
                                 font=('Arial', 7), bg='white', anchor='w')
                name_label.pack(side='left', fill='x', expand=True)

            # 更新画布滚动区域
            self.color_legend_frame.update_idletasks()
            self.color_legend_canvas.configure(scrollregion=self.color_legend_canvas.bbox("all"))

        except Exception as e:
            print(f"更新颜色索引失败: {e}")

    def _get_active_colors(self):
        """获取当前活跃的颜色（在图中实际显示的颜色）"""
        active_colors = {}

        try:
            # 基础颜色（总是显示）
            basic_colors = {
                'wall': '墙体',
                'door_window': '门窗',
                'furniture': '家具',
                'other': '其他',
                'unlabeled': '未标注',
                'highlight': '当前组'
            }

            for key, name in basic_colors.items():
                if key in self.current_color_scheme:
                    active_colors[key] = self.current_color_scheme[key]

            # 如果有加载的文件，检查实际使用的标签
            if hasattr(self, 'processor') and self.processor:
                all_entities = []
                if hasattr(self.processor, 'current_file_entities'):
                    all_entities = self.processor.current_file_entities
                elif hasattr(self.processor, 'all_groups'):
                    for group in self.processor.all_groups:
                        if isinstance(group, list):
                            all_entities.extend(group)

                # 收集实际使用的标签
                used_labels = set()
                for entity in all_entities:
                    label = entity.get('label')
                    if label and label not in ['None', 'none', '']:
                        used_labels.add(label)

                # 添加实际使用的标签颜色
                for label in used_labels:
                    if label in self.current_color_scheme:
                        active_colors[label] = self.current_color_scheme[label]

        except Exception as e:
            print(f"获取活跃颜色失败: {e}")

        return active_colors

    def _get_category_display_name(self, category):
        """获取类别的显示名称"""
        name_mapping = {
            'wall': '墙体',
            'door_window': '门窗',
            'furniture': '家具',
            'column': '柱子',
            'stair': '楼梯',
            'elevator': '电梯',
            'other': '其他',
            'unlabeled': '未标注',
            'pending': '待处理',
            'highlight': '当前组',
            'background': '背景',
            'text': '文字'
        }

        return name_mapping.get(category, category)

    def open_color_settings(self):
        """打开配色设置窗口"""
        try:
            from tkinter import colorchooser, Toplevel, Listbox, Scrollbar

            # 创建配色设置窗口
            color_window = Toplevel(self.root)
            color_window.title("配色设置")
            color_window.geometry("600x500")
            color_window.resizable(True, True)

            # 左侧颜色列表
            left_frame = Frame(color_window)
            left_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)

            Label(left_frame, text="颜色项目:", font=('Arial', 12, 'bold')).pack(anchor='w')

            # 颜色列表框
            listbox_frame = Frame(left_frame)
            listbox_frame.pack(fill='both', expand=True, pady=(5, 0))

            scrollbar = Scrollbar(listbox_frame)
            scrollbar.pack(side='right', fill='y')

            color_listbox = Listbox(listbox_frame, yscrollcommand=scrollbar.set, font=('Arial', 10))
            color_listbox.pack(side='left', fill='both', expand=True)
            scrollbar.config(command=color_listbox.yview)

            # 填充颜色项目
            color_names = {
                'background': '背景色',
                'wall': '墙体',
                'door_window': '门窗',
                'railing': '栏杆',
                'furniture': '家具',
                'bed': '床',
                'sofa': '沙发',
                'cabinet': '柜子',
                'dining_table': '餐桌',
                'appliance': '电器',
                'stair': '楼梯',
                'elevator': '电梯',
                'dimension': '尺寸标注',
                'room_label': '房间标签',
                'column': '柱子',
                'other': '其他',
                'fill': '填充色',
                'text': '文字',
                'current_group': '当前组',
                'labeled_group': '已标注组'
            }

            for key, name in color_names.items():
                color = self.current_color_scheme.get(key, '#000000')
                color_listbox.insert(tk.END, f"{name} ({color})")

            # 右侧控制按钮
            right_frame = Frame(color_window)
            right_frame.pack(side='right', fill='y', padx=10, pady=10)

            Label(right_frame, text="操作:", font=('Arial', 12, 'bold')).pack(anchor='w')

            def change_color():
                selection = color_listbox.curselection()
                if selection:
                    index = selection[0]
                    key = list(color_names.keys())[index]
                    current_color = self.current_color_scheme.get(key, '#000000')

                    new_color = colorchooser.askcolor(color=current_color, title=f"选择{color_names[key]}颜色")
                    if new_color[1]:  # 用户选择了颜色
                        self.current_color_scheme[key] = new_color[1]
                        # 更新列表显示
                        color_listbox.delete(index)
                        color_listbox.insert(index, f"{color_names[key]} ({new_color[1]})")
                        color_listbox.selection_set(index)

            Button(right_frame, text="修改颜色", command=change_color,
                   bg='#2196F3', fg='white', font=('Arial', 10)).pack(fill='x', pady=(10, 5))

            def apply_colors():
                # 应用颜色到可视化
                if self.visualizer:
                    self.visualizer.update_color_scheme(self.current_color_scheme)
                    if self.canvas:
                        self.visualizer.update_canvas(self.canvas)
                messagebox.showinfo("成功", "配色已应用")

            Button(right_frame, text="应用配色", command=apply_colors,
                   bg='#4CAF50', fg='white', font=('Arial', 10)).pack(fill='x', pady=5)

            def reset_colors():
                self.current_color_scheme = self.default_color_scheme.copy()
                # 更新列表显示
                color_listbox.delete(0, tk.END)
                for key, name in color_names.items():
                    color = self.current_color_scheme.get(key, '#000000')
                    color_listbox.insert(tk.END, f"{name} ({color})")
                messagebox.showinfo("成功", "已重置为默认配色")

            Button(right_frame, text="重置默认", command=reset_colors,
                   bg='#FF9800', fg='white', font=('Arial', 10)).pack(fill='x', pady=5)

            Button(right_frame, text="关闭", command=color_window.destroy,
                   bg='#9E9E9E', fg='white', font=('Arial', 10)).pack(fill='x', pady=(20, 5))

        except Exception as e:
            messagebox.showerror("错误", f"打开配色设置失败: {e}")

    def save_color_scheme(self):
        """保存配色方案"""
        try:
            from tkinter import simpledialog

            # 询问配色方案名称
            scheme_name = simpledialog.askstring("保存配色方案", "请输入配色方案名称:")
            if not scheme_name:
                return

            # 保存到文件
            filename = f"{scheme_name}.txt"
            filepath = os.path.join(self.color_schemes_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                for key, value in self.current_color_scheme.items():
                    f.write(f"{key}={value}\n")

            # 添加到配色方案列表
            self.color_schemes[scheme_name] = self.current_color_scheme.copy()

            # 更新下拉菜单
            self.update_color_scheme_combo()

            # 更新当前配色方案显示
            self.color_scheme_var.set(scheme_name)

            messagebox.showinfo("成功", f"配色方案 '{scheme_name}' 已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存配色方案失败: {e}")

    def load_color_scheme(self):
        """加载配色方案"""
        try:
            from tkinter import Toplevel, Listbox, Scrollbar

            if not self.color_schemes:
                messagebox.showinfo("提示", "没有可用的配色方案")
                return

            # 创建选择窗口
            select_window = Toplevel(self.root)
            select_window.title("选择配色方案")
            select_window.geometry("400x300")

            Label(select_window, text="选择要加载的配色方案:", font=('Arial', 12, 'bold')).pack(pady=10)

            # 配色方案列表
            listbox_frame = Frame(select_window)
            listbox_frame.pack(fill='both', expand=True, padx=20, pady=10)

            scrollbar = Scrollbar(listbox_frame)
            scrollbar.pack(side='right', fill='y')

            scheme_listbox = Listbox(listbox_frame, yscrollcommand=scrollbar.set, font=('Arial', 10))
            scheme_listbox.pack(side='left', fill='both', expand=True)
            scrollbar.config(command=scheme_listbox.yview)

            # 填充配色方案列表
            for scheme_name in self.color_schemes.keys():
                scheme_listbox.insert(tk.END, scheme_name)

            # 按钮框架
            btn_frame = Frame(select_window)
            btn_frame.pack(pady=10)

            def load_selected():
                selection = scheme_listbox.curselection()
                if selection:
                    scheme_name = list(self.color_schemes.keys())[selection[0]]
                    self.current_color_scheme = self.color_schemes[scheme_name].copy()

                    # 更新下拉菜单和当前选择
                    self.update_color_scheme_combo()
                    self.color_scheme_var.set(scheme_name)

                    # 应用配色
                    if self.visualizer:
                        self.visualizer.update_color_scheme(self.current_color_scheme)
                        if self.canvas:
                            self.visualizer.update_canvas(self.canvas)

                    select_window.destroy()
                    messagebox.showinfo("成功", f"已加载配色方案 '{scheme_name}'")
                else:
                    messagebox.showwarning("警告", "请选择一个配色方案")

            def load_from_file():
                filepath = filedialog.askopenfilename(
                    title="选择配色文件",
                    filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
                )
                if filepath:
                    scheme = self.load_color_scheme_from_file(filepath)
                    if scheme:
                        scheme_name = os.path.splitext(os.path.basename(filepath))[0]
                        self.current_color_scheme = scheme.copy()
                        self.color_schemes[scheme_name] = scheme.copy()

                        # 更新下拉菜单和当前选择
                        self.update_color_scheme_combo()
                        self.color_scheme_var.set(scheme_name)

                        # 应用配色
                        if self.visualizer:
                            self.visualizer.update_color_scheme(self.current_color_scheme)
                            if self.canvas:
                                self.visualizer.update_canvas(self.canvas)

                        select_window.destroy()
                        messagebox.showinfo("成功", f"已加载配色文件 '{scheme_name}'")
                    else:
                        messagebox.showerror("错误", "无法识别配色文件格式")

            def save_selected_scheme():
                """保存选中的配色方案到文件"""
                selection = scheme_listbox.curselection()
                if selection:
                    scheme_name = list(self.color_schemes.keys())[selection[0]]

                    # 询问保存位置
                    filepath = filedialog.asksaveasfilename(
                        title="保存配色文件",
                        defaultextension=".txt",
                        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                        initialvalue=f"{scheme_name}.txt"
                    )

                    if filepath:
                        try:
                            scheme = self.color_schemes[scheme_name]
                            with open(filepath, 'w', encoding='utf-8') as f:
                                for key, value in scheme.items():
                                    f.write(f"{key}={value}\n")

                            messagebox.showinfo("成功", f"配色方案 '{scheme_name}' 已保存到文件")
                        except Exception as e:
                            messagebox.showerror("错误", f"保存配色文件失败: {e}")
                else:
                    messagebox.showwarning("警告", "请选择一个配色方案")

            Button(btn_frame, text="加载选中", command=load_selected,
                   bg='#4CAF50', fg='white', font=('Arial', 10)).pack(side='left', padx=2)

            Button(btn_frame, text="保存配色文件", command=save_selected_scheme,
                   bg='#FF9800', fg='white', font=('Arial', 10)).pack(side='left', padx=2)

            Button(btn_frame, text="从文件加载", command=load_from_file,
                   bg='#2196F3', fg='white', font=('Arial', 10)).pack(side='left', padx=2)

            Button(btn_frame, text="取消", command=select_window.destroy,
                   bg='#9E9E9E', fg='white', font=('Arial', 10)).pack(side='left', padx=2)

        except Exception as e:
            messagebox.showerror("错误", f"加载配色方案失败: {e}")

    def update_color_scheme_combo(self):
        """更新配色方案下拉菜单"""
        try:
            scheme_names = list(self.color_schemes.keys())
            self.color_scheme_combo['values'] = scheme_names
            if scheme_names and self.color_scheme_var.get() not in scheme_names:
                self.color_scheme_var.set(scheme_names[0])
        except Exception as e:
            print(f"更新配色方案下拉菜单失败: {e}")

    def on_color_scheme_selected(self, event):
        """配色方案选择事件"""
        try:
            scheme_name = self.color_scheme_var.get()
            if scheme_name in self.color_schemes:
                self.current_color_scheme = self.color_schemes[scheme_name].copy()
                print(f"选择配色方案: {scheme_name}")
        except Exception as e:
            print(f"配色方案选择失败: {e}")

    def _get_entity_color_from_scheme_enhanced(self, entity):
        """根据配色方案获取实体颜色（增强版）"""
        try:
            # 优先使用实体的标签
            if entity.get('label'):
                label = entity['label']
                # 根据标签类型映射到配色方案
                label_color_map = {
                    'wall': self.current_color_scheme.get('wall', '#8B4513'),
                    'door': self.current_color_scheme.get('door_window', '#FFD700'),
                    'window': self.current_color_scheme.get('door_window', '#87CEEB'),
                    'door_window': self.current_color_scheme.get('door_window', '#FFD700'),
                    'column': self.current_color_scheme.get('column', '#696969'),
                    'beam': self.current_color_scheme.get('other', '#2F4F4F'),
                    'stair': self.current_color_scheme.get('stair', '#9370DB'),
                    'elevator': self.current_color_scheme.get('elevator', '#FF6347'),
                    'furniture': self.current_color_scheme.get('furniture', '#8B4513'),
                    'bed': self.current_color_scheme.get('bed', '#DDA0DD'),
                    'sofa': self.current_color_scheme.get('sofa', '#F0E68C'),
                    'cabinet': self.current_color_scheme.get('cabinet', '#8B4513'),
                    'dining_table': self.current_color_scheme.get('dining_table', '#D2691E'),
                    'appliance': self.current_color_scheme.get('appliance', '#708090'),
                    'dimension': self.current_color_scheme.get('dimension', '#FF1493'),
                    'room_label': self.current_color_scheme.get('room_label', '#FF6347'),
                    'railing': self.current_color_scheme.get('railing', '#4682B4'),
                    'other': self.current_color_scheme.get('other', '#808080')
                }
                return label_color_map.get(label, self.current_color_scheme.get('other', '#808080'))

            # 如果没有标签，根据实体类型和图层判断
            entity_type = entity.get('type', 'LINE')
            layer_name = str(entity.get('layer', '')).lower()

            # 根据图层名称推断类型
            if any(wall_keyword in layer_name for wall_keyword in ['wall', '墙', 'a-wall']):
                return self.current_color_scheme.get('wall', '#8B4513')
            elif any(door_keyword in layer_name for door_keyword in ['door', '门', 'window', '窗', 'a-door', 'a-window']):
                return self.current_color_scheme.get('door_window', '#FFD700')
            else:
                # 根据实体类型判断
                type_color_map = {
                    'LINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'LWPOLYLINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'POLYLINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'CIRCLE': self.current_color_scheme.get('other', '#808080'),
                    'ARC': self.current_color_scheme.get('other', '#808080'),
                    'INSERT': self.current_color_scheme.get('furniture', '#8B4513'),
                    'TEXT': self.current_color_scheme.get('text', '#000000'),
                    'MTEXT': self.current_color_scheme.get('text', '#000000')
                }
                return type_color_map.get(entity_type, self.current_color_scheme.get('other', '#808080'))

        except Exception as e:
            return self.current_color_scheme.get('other', '#808080')

    def apply_color_scheme(self):
        """应用配色到全图概览和后续操作"""
        try:
            if self.visualizer:
                # 更新可视化器的配色方案，影响后续所有操作
                self.visualizer.update_color_scheme(self.current_color_scheme)

                # 如果有全图概览，更新全图概览
                if hasattr(self.visualizer, 'ax_overview'):
                    ax_overview = self.visualizer.ax_overview

                    # 设置背景色
                    if 'background' in self.current_color_scheme:
                        ax_overview.set_facecolor(self.current_color_scheme['background'])

                    # 如果有数据，重新绘制全图概览
                    if hasattr(self, 'all_groups') and self.all_groups:
                        current_group_index = getattr(self, 'current_group_index', 0)

                        # 重新绘制全图概览，使用新的配色方案
                        ax_overview.clear()
                        ax_overview.set_facecolor(self.current_color_scheme.get('background', '#FFFFFF'))

                        # 重新绘制所有组，使用新的配色
                        for i, group in enumerate(self.all_groups):
                            # 确定颜色
                            if i == current_group_index:
                                # 当前组使用高亮颜色
                                color = self.current_color_scheme.get('current_group', '#FF0000')
                            else:
                                # 🔑 关键修复：改进已标注组的颜色显示逻辑
                                if group:
                                    first_entity = group[0]
                                    # 检查是否有标签
                                    if first_entity.get('label'):
                                        label = first_entity.get('label')
                                        # 使用对应类别的颜色
                                        if hasattr(self.visualizer, 'category_colors') and label in self.visualizer.category_colors:
                                            color = self.visualizer.category_colors[label]
                                        else:
                                            color = self.current_color_scheme.get(label, '#00FF00')
                                    elif first_entity.get('auto_labeled'):
                                        # 自动标注但无具体标签，使用通用已标注颜色
                                        color = self.current_color_scheme.get('labeled_group', '#00FF00')
                                    else:
                                        # 未标注组使用灰色
                                        color = '#808080'
                                else:
                                    color = '#808080'

                            # 绘制组
                            for entity in group:
                                self.visualizer._draw_entity(entity, color, 1, 0.7, ax_overview)

                        # 设置坐标轴和标题
                        ax_overview.set_aspect('equal')
                        ax_overview.set_title(f'全图概览 (组 {current_group_index + 1}/{len(self.all_groups)})',
                                            color=self.current_color_scheme.get('text', '#000000'))
                    else:
                        # 即使没有数据，也设置背景色
                        ax_overview.clear()
                        ax_overview.set_facecolor(self.current_color_scheme.get('background', '#FFFFFF'))
                        ax_overview.set_title('全图概览 - 配色方案已应用',
                                            color=self.current_color_scheme.get('text', '#000000'))

                # 更新画布
                if self.canvas:
                    self.canvas.draw()

                # 🎨 更新颜色索引显示
                self._update_color_legend()

                messagebox.showinfo("成功", f"配色方案已应用，将影响所有后续显示")
            else:
                messagebox.showwarning("警告", "可视化器未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"应用配色失败: {e}")
            import traceback
            traceback.print_exc()

    def export_current_color_scheme(self):
        """导出当前配色方案到文件"""
        try:
            # 获取当前配色方案名称
            current_scheme_name = self.color_scheme_var.get()

            # 询问保存位置
            filepath = filedialog.asksaveasfilename(
                title="导出配色文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialfile=f"{current_scheme_name}.txt"
            )

            if filepath:
                # 保存当前配色方案
                with open(filepath, 'w', encoding='utf-8') as f:
                    for key, value in self.current_color_scheme.items():
                        f.write(f"{key}={value}\n")

                messagebox.showinfo("成功", f"配色方案 '{current_scheme_name}' 已导出到文件")
        except Exception as e:
            messagebox.showerror("错误", f"导出配色文件失败: {e}")

    def _get_group_fill_status(self, group_index):
        """获取组的填充状态（返回带颜色信息的状态）"""
        # 初始化填充状态字典
        if not hasattr(self, 'group_fill_status'):
            self.group_fill_status = {}

        # 检查组是否已填充
        if group_index in self.group_fill_status and self.group_fill_status[group_index]:
            return "●填充"  # 使用圆点符号表示已填充状态

        # 检查组的状态，确定填充按钮的可用性
        if hasattr(self, 'all_groups') and self.all_groups and group_index < len(self.all_groups):
            group = self.all_groups[group_index]
            if group and len(group) > 0:
                first_entity = group[0]
                if first_entity.get('auto_labeled') or first_entity.get('label'):
                    return "□填充"  # 使用方框符号表示可填充状态
                else:
                    return "▢填充"  # 使用虚框符号表示待标注状态

        return "▢填充"  # 默认状态

    def _get_fill_tag(self, group_index, fill_status):
        """获取填充状态的标签"""
        if "●" in fill_status:
            return "filled"
        elif "□" in fill_status:
            return "fillable"
        else:
            return "unfillable"

    def _set_fill_column_color(self, item_id, group_index, fill_status):
        """为填充列设置颜色"""
        try:
            # 根据填充状态设置不同的显示样式
            if "●" in fill_status:
                # 已填充：绿色背景
                self.group_tree.set(item_id, '填充', '●填充')
            elif "□" in fill_status:
                # 可填充：根据实体类型设置颜色
                if hasattr(self, 'all_groups') and self.all_groups and group_index < len(self.all_groups):
                    group = self.all_groups[group_index]
                    if group and len(group) > 0:
                        first_entity = group[0]
                        label = first_entity.get('label', 'other')
                        # 使用对应类型的填充颜色符号
                        color_symbol = self._get_color_symbol(label)
                        self.group_tree.set(item_id, '填充', f'{color_symbol}填充')
                    else:
                        self.group_tree.set(item_id, '填充', '□填充')
                else:
                    self.group_tree.set(item_id, '填充', '□填充')
            else:
                # 不可填充：灰色
                self.group_tree.set(item_id, '填充', '▢填充')
        except Exception as e:
            print(f"设置填充列颜色失败: {e}")

    def _get_color_symbol(self, label):
        """根据实体类型获取颜色符号"""
        color_symbols = {
            'wall': '■',        # 墙体：黑色方块
            'door_window': '●', # 门窗：红色圆点
            'railing': '▲',     # 栏杆：绿色三角
            'furniture': '♦',   # 家具：蓝色菱形
            'bed': '♥',         # 床：紫色心形
            'sofa': '★',        # 沙发：黄色星形
            'cabinet': '◆',     # 柜子：青色菱形
            'dining_table': '♠', # 餐桌：紫色黑桃
            'appliance': '◉',   # 电器：橙色圆点
            'stair': '▼',       # 楼梯：灰色下三角
            'elevator': '■',    # 电梯：红色方块
            'dimension': '◊',   # 尺寸：绿色空菱形
            'room_label': '○',  # 房间标签：蓝色空圆
            'column': '▪',      # 柱子：黄色小方块
            'other': '□'        # 其他：空方块
        }
        return color_symbols.get(label, '□')

    def on_group_click(self, event):
        """处理组列表单击事件（修复版 - 支持已标注组预览和填充）"""
        try:
            # 获取点击的位置
            region = self.group_tree.identify_region(event.x, event.y)
            if region == "cell":
                # 获取点击的列（只传x坐标）
                column = self.group_tree.identify_column(event.x)
                item = self.group_tree.identify_row(event.y)

                if item:
                    # 获取组信息
                    group_text = self.group_tree.item(item, 'text')
                    values = self.group_tree.item(item, 'values')
                    status = values[0] if values else ""

                    if group_text.startswith('组'):
                        group_index = int(group_text[1:]) - 1

                        if column == '#4':  # 填充列
                            self.fill_group(group_index)
                        else:
                            # 🔧 修复：单击已标注组时显示预览和高亮
                            if status in ['已标注', '自动标注', '重新标注']:
                                self._show_group_preview_and_highlight(group_index)
                            elif status == '未标注':
                                # 未标注组，跳转到该组进行标注
                                if self.processor and hasattr(self.processor, 'jump_to_group'):
                                    self.processor.jump_to_group(group_index)
        except Exception as e:
            print(f"处理组点击事件失败: {e}")

    def _show_group_preview_and_highlight(self, group_index):
        """显示组预览和概览图高亮（新增方法）"""
        try:
            if not self.processor or not hasattr(self.processor, 'all_groups'):
                return

            if group_index < 0 or group_index >= len(self.processor.all_groups):
                return

            group = self.processor.all_groups[group_index]
            print(f"🔍 显示组{group_index + 1}预览和高亮")

            # 1. 更新左侧视图显示当前组
            if self.processor.visualizer and self.processor.canvas:
                # 清理组数据
                cleaned_group = self.processor._clean_group_data(group)

                # 显示组详细视图
                self.processor.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)

                # 2. 更新概览图高亮显示
                current_file_entities = getattr(self.processor, 'current_file_entities', [])
                auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
                labeled_entities = getattr(self.processor, 'labeled_entities', [])

                if current_file_entities:
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,
                        cleaned_group,  # 当前组实体用于高亮
                        auto_labeled_entities + labeled_entities,
                        processor=self.processor,
                        current_group_index=group_index  # 传递组索引
                    )

                # 更新画布
                self.processor.visualizer.update_canvas(self.processor.canvas)

                print(f"✅ 组{group_index + 1}预览和高亮显示完成")

        except Exception as e:
            print(f"显示组预览和高亮失败: {e}")
            import traceback
            traceback.print_exc()

    def fill_group(self, group_index):
        """对指定组进行填充"""
        try:
            if not hasattr(self, 'all_groups') or not self.all_groups or group_index >= len(self.all_groups):
                messagebox.showwarning("警告", "无效的组索引")
                return

            group = self.all_groups[group_index]
            if not group:
                messagebox.showwarning("警告", "组为空")
                return

            # 检查是否已填充，如果是则询问是否清除
            if hasattr(self, 'group_fill_status') and group_index in self.group_fill_status and self.group_fill_status[group_index]:
                result = messagebox.askyesno("确认", f"组 {group_index + 1} 已填充，是否清除填充？")
                if result:
                    # 清除填充
                    del self.group_fill_status[group_index]
                    self.update_group_list()
                    messagebox.showinfo("成功", f"组 {group_index + 1} 的填充已清除")
                return

            # 检查组的状态
            first_entity = group[0]
            if not (first_entity.get('auto_labeled') or first_entity.get('label')):
                messagebox.showwarning("警告", "只能对已标注的组进行填充")
                return

            # 获取组的标签
            label = first_entity.get('label', 'other')

            # 获取填充颜色
            fill_color = self.current_color_scheme.get(f'{label}_fill', self.current_color_scheme.get('fill', '#E0E0E0'))

            # 执行填充（这里需要实现具体的填充逻辑）
            success = self._perform_group_fill(group, fill_color)

            if success:
                # 初始化填充状态字典
                if not hasattr(self, 'group_fill_status'):
                    self.group_fill_status = {}

                # 更新填充状态
                self.group_fill_status[group_index] = True

                # 更新组列表显示
                self.update_group_list()

                messagebox.showinfo("成功", f"组 {group_index + 1} 填充完成")
            else:
                messagebox.showwarning("失败", f"组 {group_index + 1} 无法填充")

        except Exception as e:
            messagebox.showerror("错误", f"填充组失败: {e}")

    def _perform_group_fill(self, group, fill_color):
        """执行组填充的具体逻辑"""
        try:
            # 这里应该实现具体的填充算法
            # 暂时返回True表示成功
            print(f"对组进行填充，颜色: {fill_color}")
            return True
        except Exception as e:
            print(f"执行填充失败: {e}")
            return False

    def init_shadow_system(self):
        """初始化阴影系统"""
        try:
            # 检查阴影系统是否可用
            try:
                from shadow_generator import DirectionalShadowGenerator, ContactShadowGenerator
                self.shadow_available = True
                print("✅ 阴影系统已加载")
            except ImportError:
                self.shadow_available = False
                print("⚠️ 阴影系统不可用")
                return

            # 初始化图层阴影配置
            self.layer_shadows = {}

            # 默认阴影配置
            self.default_shadow_config = {
                'type': 'directional',  # 方向阴影
                'direction': 45,        # 阴影方向（度）
                'intensity': 0.3,       # 阴影强度
                'length': 10,           # 阴影长度
                'enabled': False,       # 是否启用
                'generator': None       # 阴影生成器实例
            }

            # 预定义图层阴影配置
            layer_configs = {
                'wall': {'type': 'contact', 'direction': 0, 'intensity': 0.2, 'length': 5},
                'door_window': {'type': 'directional', 'direction': 45, 'intensity': 0.4, 'length': 15},
                'furniture': {'type': 'directional', 'direction': 135, 'intensity': 0.3, 'length': 12},
                'column': {'type': 'contact', 'direction': 0, 'intensity': 0.5, 'length': 8},
                'other': {'type': 'directional', 'direction': 90, 'intensity': 0.2, 'length': 8}
            }

            # 初始化各图层的阴影配置
            for layer_key, config in layer_configs.items():
                self.layer_shadows[layer_key] = {**self.default_shadow_config, **config}

            print(f"✅ 阴影系统初始化完成，支持 {len(self.layer_shadows)} 个图层")

        except Exception as e:
            print(f"❌ 阴影系统初始化失败: {e}")
            self.shadow_available = False

    def show_shadow_control_panel(self):
        """显示阴影控制面板"""
        try:
            if not hasattr(self, 'shadow_available') or not self.shadow_available:
                messagebox.showwarning("警告", "阴影系统不可用")
                return

            # 创建阴影控制窗口
            shadow_window = tk.Toplevel(self.root)
            shadow_window.title("阴影控制面板")
            shadow_window.geometry("600x500")
            shadow_window.resizable(False, False)
            shadow_window.transient(self.root)
            shadow_window.grab_set()

            # 居中显示
            shadow_window.update_idletasks()
            x = (shadow_window.winfo_screenwidth() // 2) - (shadow_window.winfo_width() // 2)
            y = (shadow_window.winfo_screenheight() // 2) - (shadow_window.winfo_height() // 2)
            shadow_window.geometry(f"+{x}+{y}")

            # 标题
            title_label = tk.Label(shadow_window, text="图层阴影控制",
                                 font=('Arial', 14, 'bold'))
            title_label.pack(pady=10)

            # 说明文本
            info_label = tk.Label(shadow_window,
                                text="为不同图层的实体添加阴影效果，增强视觉层次感",
                                font=('Arial', 10))
            info_label.pack(pady=5)

            # 滚动框架
            canvas = tk.Canvas(shadow_window)
            scrollbar = tk.Scrollbar(shadow_window, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 为每个图层创建控制区域
            if hasattr(self, 'layer_shadows'):
                for layer_key, shadow_config in self.layer_shadows.items():
                    self._create_layer_shadow_controls(scrollable_frame, layer_key, shadow_config)
            else:
                # 如果没有阴影配置，显示提示
                no_config_label = tk.Label(scrollable_frame,
                                         text="暂无可用的图层阴影配置",
                                         font=('Arial', 12))
                no_config_label.pack(pady=20)

            # 布局
            canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
            scrollbar.pack(side="right", fill="y")

            # 关闭按钮
            close_btn = tk.Button(shadow_window, text="关闭",
                                command=shadow_window.destroy,
                                font=('Arial', 10), width=10)
            close_btn.pack(pady=10)

        except Exception as e:
            print(f"❌ 显示阴影控制面板失败: {e}")
            messagebox.showerror("错误", f"显示阴影控制面板失败: {e}")

    def _create_layer_shadow_controls(self, parent, layer_key, shadow_config):
        """创建图层阴影控制区域"""
        try:
            # 图层框架
            layer_frame = tk.LabelFrame(parent, text=f"图层: {layer_key}",
                                      font=('Arial', 10, 'bold'))
            layer_frame.pack(fill='x', padx=5, pady=5)

            # 按钮行
            button_frame = tk.Frame(layer_frame)
            button_frame.pack(fill='x', padx=5, pady=5)

            # 添加阴影按钮
            add_btn = tk.Button(button_frame, text="添加阴影",
                              command=lambda: self._add_layer_shadow(layer_key),
                              bg='#4CAF50', fg='white', width=10)
            add_btn.pack(side='left', padx=2)

            # 删除阴影按钮
            remove_btn = tk.Button(button_frame, text="删除阴影",
                                 command=lambda: self._remove_layer_shadow(layer_key),
                                 bg='#F44336', fg='white', width=10)
            remove_btn.pack(side='left', padx=2)

            # 隐藏/显示按钮
            hide_btn = tk.Button(button_frame, text="隐藏/显示",
                               command=lambda: self._hide_layer_shadow(layer_key),
                               bg='#FF9800', fg='white', width=10)
            hide_btn.pack(side='left', padx=2)

            # 状态显示
            status_text = "已启用" if shadow_config['enabled'] else "未启用"
            status_label = tk.Label(button_frame, text=f"状态: {status_text}",
                                  fg='green' if shadow_config['enabled'] else 'red')
            status_label.pack(side='right')

        except Exception as e:
            print(f"❌ 创建图层阴影控制失败: {e}")

    def _add_layer_shadow(self, layer_key):
        """为图层添加阴影"""
        try:
            if not hasattr(self, 'shadow_available') or not self.shadow_available:
                messagebox.showwarning("警告", "阴影系统不可用")
                return

            if not hasattr(self, 'layer_shadows') or layer_key not in self.layer_shadows:
                messagebox.showwarning("警告", f"图层 {layer_key} 配置不存在")
                return

            shadow_config = self.layer_shadows[layer_key]
            shadow_config['enabled'] = True

            print(f"✅ 已为图层 {layer_key} 添加阴影")
            messagebox.showinfo("成功", f"已为图层 {layer_key} 添加阴影")

        except Exception as e:
            print(f"❌ 添加图层阴影失败: {e}")
            messagebox.showerror("错误", f"添加阴影失败: {e}")

    def _remove_layer_shadow(self, layer_key):
        """删除图层阴影"""
        try:
            if hasattr(self, 'layer_shadows') and layer_key in self.layer_shadows:
                self.layer_shadows[layer_key]['enabled'] = False
                print(f"🗑️ 已删除图层 {layer_key} 的阴影")
                messagebox.showinfo("成功", f"已删除图层 {layer_key} 的阴影")

        except Exception as e:
            print(f"❌ 删除图层阴影失败: {e}")
            messagebox.showerror("错误", f"删除阴影失败: {e}")

    def _hide_layer_shadow(self, layer_key):
        """隐藏/显示图层阴影"""
        try:
            if hasattr(self, 'layer_shadows') and layer_key in self.layer_shadows:
                shadow_config = self.layer_shadows[layer_key]
                shadow_config['enabled'] = not shadow_config['enabled']

                status = "隐藏" if not shadow_config['enabled'] else "显示"
                print(f"👁️ 图层 {layer_key} 阴影已{status}")
                messagebox.showinfo("成功", f"图层 {layer_key} 阴影已{status}")

        except Exception as e:
            print(f"❌ 切换图层阴影可见性失败: {e}")
            messagebox.showerror("错误", f"切换阴影可见性失败: {e}")

    def show_room_recognition_panel(self):
        """显示房间识别面板"""
        try:
            # 检查房间识别系统是否可用
            try:
                from room_recognition_processor import RoomRecognitionProcessor
                room_available = True
                print("✅ 房间识别系统已加载")
            except ImportError:
                room_available = False
                print("⚠️ 房间识别系统不可用")
                messagebox.showwarning("警告", "房间识别系统不可用，请确保相关模块已安装")
                return

            # 创建房间识别窗口
            room_window = tk.Toplevel(self.root)
            room_window.title("房间识别功能")
            room_window.geometry("800x600")
            room_window.resizable(True, True)
            room_window.transient(self.root)
            room_window.grab_set()

            # 居中显示
            room_window.update_idletasks()
            x = (room_window.winfo_screenwidth() // 2) - (room_window.winfo_width() // 2)
            y = (room_window.winfo_screenheight() // 2) - (room_window.winfo_height() // 2)
            room_window.geometry(f"+{x}+{y}")

            # 标题
            title_label = tk.Label(room_window, text="房间识别与分类",
                                 font=('Arial', 16, 'bold'))
            title_label.pack(pady=10)

            # 创建主框架
            main_frame = tk.Frame(room_window)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # 左侧控制面板
            control_frame = tk.Frame(main_frame, width=300)
            control_frame.pack(side='left', fill='y', padx=(0, 10))
            control_frame.pack_propagate(False)

            # 右侧显示区域
            display_frame = tk.Frame(main_frame)
            display_frame.pack(side='right', fill='both', expand=True)

            # 创建控制区域
            self._create_room_control_area(control_frame, room_window)

            # 创建显示区域
            self._create_room_display_area(display_frame)

            # 初始化房间处理器
            if not hasattr(self, 'room_processor'):
                self.room_processor = RoomRecognitionProcessor()

        except Exception as e:
            print(f"❌ 显示房间识别面板失败: {e}")
            messagebox.showerror("错误", f"显示房间识别面板失败: {e}")

    def _create_room_control_area(self, parent, window):
        """创建房间控制区域"""
        try:
            # 功能按钮区
            button_frame = tk.LabelFrame(parent, text="房间识别功能", font=('Arial', 10, 'bold'))
            button_frame.pack(fill='x', pady=(0, 10))

            # 自动房间识别按钮（主要功能）
            auto_btn = tk.Button(button_frame, text="🤖 自动房间识别",
                               command=self._auto_room_recognition,
                               bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'),
                               height=2)
            auto_btn.pack(fill='x', padx=5, pady=5)

            # 分步功能按钮
            step_frame = tk.Frame(button_frame)
            step_frame.pack(fill='x', padx=5, pady=5)

            # 识别建筑外轮廓按钮
            outline_btn = tk.Button(step_frame, text="识别外轮廓",
                                  command=self._identify_building_outline,
                                  bg='#2196F3', fg='white', font=('Arial', 9))
            outline_btn.pack(fill='x', pady=2)

            # 识别房间按钮
            room_btn = tk.Button(step_frame, text="识别房间",
                               command=self._identify_rooms,
                               bg='#FF9800', fg='white', font=('Arial', 9))
            room_btn.pack(fill='x', pady=2)

            # 房间切分按钮
            split_btn = tk.Button(step_frame, text="房间切分",
                                command=self._split_rooms,
                                bg='#9C27B0', fg='white', font=('Arial', 9))
            split_btn.pack(fill='x', pady=2)

            # 房间列表区
            list_frame = tk.LabelFrame(parent, text="房间列表", font=('Arial', 10, 'bold'))
            list_frame.pack(fill='both', expand=True, pady=(0, 10))

            # 创建房间列表
            columns = ('编号', '类型', '面积')
            self.room_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

            # 设置列标题
            for col in columns:
                self.room_tree.heading(col, text=col)
                self.room_tree.column(col, width=80, anchor='center')

            # 滚动条
            room_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.room_tree.yview)
            self.room_tree.configure(yscrollcommand=room_scrollbar.set)

            # 布局
            self.room_tree.pack(side='left', fill='both', expand=True)
            room_scrollbar.pack(side='right', fill='y')

            # 房间类型修改区
            type_frame = tk.LabelFrame(parent, text="房间类型修改", font=('Arial', 10, 'bold'))
            type_frame.pack(fill='x')

            tk.Label(type_frame, text="选择房间类型:", font=('Arial', 9)).pack(anchor='w', padx=5, pady=2)

            # 房间类型选择
            self.room_type_var = tk.StringVar()
            room_types = ['客厅', '卧室', '阳台', '厨房', '卫生间', '杂物间', '其他房间', '设备平台']
            type_combo = ttk.Combobox(type_frame, textvariable=self.room_type_var,
                                    values=room_types, state='readonly')
            type_combo.pack(fill='x', padx=5, pady=2)

            # 修改类型按钮
            change_btn = tk.Button(type_frame, text="修改选中房间类型",
                                 command=self._change_room_type,
                                 bg='#607D8B', fg='white', font=('Arial', 9))
            change_btn.pack(fill='x', padx=5, pady=5)

            # 关闭按钮
            close_btn = tk.Button(parent, text="关闭", command=window.destroy,
                                font=('Arial', 10), width=15)
            close_btn.pack(pady=10)

        except Exception as e:
            print(f"❌ 创建房间控制区域失败: {e}")

    def _create_room_display_area(self, parent):
        """创建房间显示区域"""
        try:
            # 显示标题
            display_label = tk.Label(parent, text="房间布局显示",
                                   font=('Arial', 12, 'bold'))
            display_label.pack(pady=(0, 10))

            # 创建matplotlib画布
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

            self.room_fig, self.room_ax = plt.subplots(figsize=(8, 6))
            self.room_canvas = FigureCanvasTkAgg(self.room_fig, parent)
            self.room_canvas.get_tk_widget().pack(fill='both', expand=True)

            # 初始化显示
            self.room_ax.set_title("房间识别结果")
            self.room_ax.set_aspect('equal')
            self.room_ax.grid(True, alpha=0.3)
            self.room_canvas.draw()

        except Exception as e:
            print(f"❌ 创建房间显示区域失败: {e}")
            # 如果matplotlib不可用，创建简单的文本显示
            text_label = tk.Label(parent, text="房间显示区域\n(需要matplotlib支持)",
                                font=('Arial', 12), bg='lightgray')
            text_label.pack(fill='both', expand=True)

    def _auto_room_recognition(self):
        """自动房间识别（完整流程）"""
        try:
            print("🤖 开始自动房间识别...")

            # 检查是否有处理器和数据
            if not self.processor:
                messagebox.showwarning("警告", "请先加载CAD文件")
                return

            if not hasattr(self.processor, 'all_groups') or not self.processor.all_groups:
                messagebox.showwarning("警告", "请先进行墙体识别")
                return

            # 清空房间列表
            for item in self.room_tree.get_children():
                self.room_tree.delete(item)

            # 显示进度
            self.room_tree.insert('', 'end', values=("---", "正在识别...", "---"))

            # 获取墙体组和门窗组数据
            wall_groups, door_window_groups = self._get_wall_door_data()

            if not wall_groups:
                messagebox.showwarning("警告", "未找到墙体数据，请先进行墙体识别")
                return

            # 设置数据到房间处理器
            self.room_processor.wall_groups = wall_groups
            self.room_processor.door_window_groups = door_window_groups or []

            # 执行完整的房间识别流程
            identified_rooms = self.room_processor._identify_rooms()

            if identified_rooms:
                print(f"✅ 房间识别成功: {len(identified_rooms)} 个房间")

                # 设置识别结果
                self.room_processor.rooms = identified_rooms

                # 自动分类房间
                self.room_processor._classify_rooms_automatically()

                # 更新UI显示
                self._update_room_list()
                self._update_room_display()

                messagebox.showinfo("成功", f"自动房间识别完成！\n识别到 {len(identified_rooms)} 个房间")
            else:
                print("❌ 房间识别失败")
                messagebox.showerror("错误", "房间识别失败，请检查墙体和门窗数据")
                # 清除进度显示
                for item in self.room_tree.get_children():
                    self.room_tree.delete(item)

        except Exception as e:
            print(f"❌ 自动房间识别失败: {e}")
            messagebox.showerror("错误", f"自动房间识别失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_wall_door_data(self):
        """获取墙体和门窗数据"""
        try:
            wall_groups = []
            door_window_groups = []

            if not self.processor or not hasattr(self.processor, 'all_groups'):
                return wall_groups, door_window_groups

            # 从所有组中筛选墙体和门窗组
            for group in self.processor.all_groups:
                if not group:
                    continue

                # 检查组的类型
                group_type = 'unknown'
                if isinstance(group[0], dict):
                    layer = group[0].get('layer', '').lower()
                    label = group[0].get('label', '').lower()

                    # 判断是否为墙体
                    if any(keyword in layer for keyword in ['wall', '墙']) or 'wall' in label:
                        group_type = 'wall'
                    # 判断是否为门窗
                    elif any(keyword in layer for keyword in ['door', 'window', '门', '窗']) or any(keyword in label for keyword in ['door', 'window', '门', '窗']):
                        group_type = 'door_window'

                # 分类到对应的组
                if group_type == 'wall':
                    wall_groups.append(group)
                elif group_type == 'door_window':
                    door_window_groups.append(group)

            print(f"📊 数据统计: 墙体组 {len(wall_groups)} 个, 门窗组 {len(door_window_groups)} 个")
            return wall_groups, door_window_groups

        except Exception as e:
            print(f"❌ 获取墙体门窗数据失败: {e}")
            return [], []

    def _identify_building_outline(self):
        """识别建筑外轮廓"""
        try:
            print("🔍 开始识别建筑外轮廓...")

            # 获取墙体组和门窗组数据
            wall_groups, door_window_groups = self._get_wall_door_data()

            if not wall_groups:
                messagebox.showwarning("警告", "未找到墙体数据，请先进行墙体识别")
                return

            # 设置数据到房间处理器
            self.room_processor.wall_groups = wall_groups
            self.room_processor.door_window_groups = door_window_groups

            # 识别建筑外轮廓
            outline = self.room_processor._identify_building_outline()

            if outline:
                messagebox.showinfo("成功", "建筑外轮廓识别成功！")
                self._update_room_display()
            else:
                messagebox.showerror("失败", "建筑外轮廓识别失败")

        except Exception as e:
            print(f"❌ 识别建筑外轮廓失败: {e}")
            messagebox.showerror("错误", f"识别建筑外轮廓失败: {e}")

    def _identify_rooms(self):
        """识别房间"""
        try:
            print("🏠 开始识别房间...")

            if not hasattr(self.room_processor, 'building_outline') or not self.room_processor.building_outline:
                messagebox.showwarning("警告", "请先识别建筑外轮廓")
                return

            # 识别房间
            rooms = self.room_processor._identify_rooms()

            if rooms:
                # 自动分类房间
                self.room_processor._classify_rooms_automatically()

                # 更新房间列表
                self._update_room_list()

                # 更新显示
                self._update_room_display()

                messagebox.showinfo("成功", f"房间识别成功！识别到 {len(rooms)} 个房间")
            else:
                messagebox.showerror("失败", "房间识别失败")

        except Exception as e:
            print(f"❌ 识别房间失败: {e}")
            messagebox.showerror("错误", f"识别房间失败: {e}")

    def _split_rooms(self):
        """房间切分"""
        try:
            print("✂️ 开始房间切分...")

            if not hasattr(self.room_processor, 'rooms') or not self.room_processor.rooms:
                messagebox.showwarning("警告", "请先识别房间")
                return

            # 这里可以添加更复杂的房间切分逻辑
            # 目前简单地重新识别房间
            self._identify_rooms()

            messagebox.showinfo("成功", "房间切分完成")

        except Exception as e:
            print(f"❌ 房间切分失败: {e}")
            messagebox.showerror("错误", f"房间切分失败: {e}")

    def _update_room_list(self):
        """更新房间列表"""
        try:
            # 清空现有列表
            for item in self.room_tree.get_children():
                self.room_tree.delete(item)

            if not hasattr(self.room_processor, 'rooms') or not self.room_processor.rooms:
                return

            # 添加房间到列表
            for i, room in enumerate(self.room_processor.rooms):
                room_id = f"R{i+1:02d}"
                room_type = room.get('type', '未知')
                room_area = f"{room.get('area', 0):.1f}"

                self.room_tree.insert('', 'end', values=(room_id, room_type, room_area))

            print(f"✅ 房间列表更新完成: {len(self.room_processor.rooms)} 个房间")

        except Exception as e:
            print(f"❌ 更新房间列表失败: {e}")

    def _update_room_display(self):
        """更新房间显示"""
        try:
            if not hasattr(self, 'room_ax'):
                return

            # 清空画布
            self.room_ax.clear()
            self.room_ax.set_title("房间识别结果")
            self.room_ax.set_aspect('equal')
            self.room_ax.grid(True, alpha=0.3)

            # 房间类型颜色映射
            room_colors = {
                '客厅': '#FF6B6B',
                '卧室': '#4ECDC4',
                '阳台': '#45B7D1',
                '厨房': '#96CEB4',
                '卫生间': '#FFEAA7',
                '杂物间': '#DDA0DD',
                '其他房间': '#D3D3D3',
                '设备平台': '#F4A460',
                '墙体空腔': '#CCCCCC',
                '未知': '#EEEEEE'
            }

            # 绘制建筑外轮廓
            if hasattr(self.room_processor, 'building_outline') and self.room_processor.building_outline:
                outline = self.room_processor.building_outline
                if hasattr(outline, 'exterior'):
                    x, y = outline.exterior.xy
                    self.room_ax.plot(x, y, 'k-', linewidth=2, label='建筑外轮廓')

            # 绘制房间
            if hasattr(self.room_processor, 'rooms') and self.room_processor.rooms:
                for i, room in enumerate(self.room_processor.rooms):
                    geometry = room.get('geometry')
                    room_type = room.get('type', '未知')

                    if geometry and hasattr(geometry, 'exterior'):
                        x, y = geometry.exterior.xy
                        color = room_colors.get(room_type, '#EEEEEE')

                        # 填充房间
                        self.room_ax.fill(x, y, color=color, alpha=0.6, label=room_type if i == 0 else "")

                        # 绘制房间边界
                        self.room_ax.plot(x, y, 'k-', linewidth=1, alpha=0.8)

                        # 添加房间标签
                        centroid = geometry.centroid
                        self.room_ax.text(centroid.x, centroid.y, f"R{i+1:02d}\n{room_type}",
                                        ha='center', va='center', fontsize=8,
                                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            # 添加图例
            handles, labels = self.room_ax.get_legend_handles_labels()
            if handles:
                # 去重图例
                unique_labels = []
                unique_handles = []
                for handle, label in zip(handles, labels):
                    if label not in unique_labels:
                        unique_labels.append(label)
                        unique_handles.append(handle)

                self.room_ax.legend(unique_handles, unique_labels, loc='upper right', fontsize=8)

            # 刷新画布
            self.room_canvas.draw()

        except Exception as e:
            print(f"❌ 更新房间显示失败: {e}")

    def _change_room_type(self):
        """修改房间类型"""
        try:
            # 获取选中的房间
            selection = self.room_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要修改的房间")
                return

            # 获取新的房间类型
            new_type = self.room_type_var.get()
            if not new_type:
                messagebox.showwarning("警告", "请选择房间类型")
                return

            # 获取房间索引
            item = selection[0]
            room_id = self.room_tree.item(item, 'values')[0]
            room_index = int(room_id[1:]) - 1  # 从R01格式提取索引

            # 修改房间类型
            if hasattr(self.room_processor, 'rooms') and room_index < len(self.room_processor.rooms):
                self.room_processor.rooms[room_index]['type'] = new_type

                # 更新显示
                self._update_room_list()
                self._update_room_display()

                messagebox.showinfo("成功", f"房间 {room_id} 类型已修改为: {new_type}")
            else:
                messagebox.showerror("错误", "房间索引无效")

        except Exception as e:
            print(f"❌ 修改房间类型失败: {e}")
            messagebox.showerror("错误", f"修改房间类型失败: {e}")

    def zoom_in(self):
        """放大视图"""
        try:
            if not self.processor or not self.processor.visualizer:
                messagebox.showwarning("警告", "请先加载CAD文件")
                return

            # 获取当前视图范围
            if hasattr(self.processor.visualizer, 'ax_detail'):
                ax = self.processor.visualizer.ax_detail
                xlim = ax.get_xlim()
                ylim = ax.get_ylim()

                # 计算缩放中心（视图中心）
                center_x = (xlim[0] + xlim[1]) / 2
                center_y = (ylim[0] + ylim[1]) / 2

                # 缩放因子
                zoom_factor = 0.8  # 放大20%

                # 计算新的视图范围
                width = (xlim[1] - xlim[0]) * zoom_factor
                height = (ylim[1] - ylim[0]) * zoom_factor

                new_xlim = [center_x - width/2, center_x + width/2]
                new_ylim = [center_y - height/2, center_y + height/2]

                # 应用新的视图范围
                ax.set_xlim(new_xlim)
                ax.set_ylim(new_ylim)

                # 更新画布
                if self.processor.canvas:
                    self.processor.canvas.draw()

                print(f"🔍 放大视图: {width:.1f} x {height:.1f}")

        except Exception as e:
            print(f"❌ 放大视图失败: {e}")
            messagebox.showerror("错误", f"放大视图失败: {e}")

    def zoom_out(self):
        """缩小视图"""
        try:
            if not self.processor or not self.processor.visualizer:
                messagebox.showwarning("警告", "请先加载CAD文件")
                return

            # 获取当前视图范围
            if hasattr(self.processor.visualizer, 'ax_detail'):
                ax = self.processor.visualizer.ax_detail
                xlim = ax.get_xlim()
                ylim = ax.get_ylim()

                # 计算缩放中心（视图中心）
                center_x = (xlim[0] + xlim[1]) / 2
                center_y = (ylim[0] + ylim[1]) / 2

                # 缩放因子
                zoom_factor = 1.25  # 缩小25%

                # 计算新的视图范围
                width = (xlim[1] - xlim[0]) * zoom_factor
                height = (ylim[1] - ylim[0]) * zoom_factor

                new_xlim = [center_x - width/2, center_x + width/2]
                new_ylim = [center_y - height/2, center_y + height/2]

                # 应用新的视图范围
                ax.set_xlim(new_xlim)
                ax.set_ylim(new_ylim)

                # 更新画布
                if self.processor.canvas:
                    self.processor.canvas.draw()

                print(f"🔍 缩小视图: {width:.1f} x {height:.1f}")

        except Exception as e:
            print(f"❌ 缩小视图失败: {e}")
            messagebox.showerror("错误", f"缩小视图失败: {e}")

    def zoom_fit(self):
        """适应窗口大小"""
        try:
            if not self.processor or not self.processor.visualizer:
                messagebox.showwarning("警告", "请先加载CAD文件")
                return

            # 获取所有实体的边界
            if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities:
                all_x = []
                all_y = []

                for entity in self.processor.current_file_entities:
                    if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                        points = entity.get('points', [])
                        for point in points:
                            if len(point) >= 2:
                                all_x.append(point[0])
                                all_y.append(point[1])
                    elif entity.get('type') == 'CIRCLE':
                        center = entity.get('center', [0, 0])
                        radius = entity.get('radius', 0)
                        all_x.extend([center[0] - radius, center[0] + radius])
                        all_y.extend([center[1] - radius, center[1] + radius])

                if all_x and all_y:
                    # 计算边界
                    min_x, max_x = min(all_x), max(all_x)
                    min_y, max_y = min(all_y), max(all_y)

                    # 添加边距
                    margin_x = (max_x - min_x) * 0.1
                    margin_y = (max_y - min_y) * 0.1

                    # 应用到详细视图
                    if hasattr(self.processor.visualizer, 'ax_detail'):
                        ax = self.processor.visualizer.ax_detail
                        ax.set_xlim(min_x - margin_x, max_x + margin_x)
                        ax.set_ylim(min_y - margin_y, max_y + margin_y)

                        # 更新画布
                        if self.processor.canvas:
                            self.processor.canvas.draw()

                        print(f"🔍 适应视图: ({min_x:.1f}, {min_y:.1f}) - ({max_x:.1f}, {max_y:.1f})")

                    # 同时更新概览图
                    if hasattr(self.processor.visualizer, 'ax_overview'):
                        ax_overview = self.processor.visualizer.ax_overview
                        ax_overview.set_xlim(min_x - margin_x, max_x + margin_x)
                        ax_overview.set_ylim(min_y - margin_y, max_y + margin_y)

                        # 更新画布
                        if self.processor.canvas:
                            self.processor.canvas.draw()
                else:
                    messagebox.showinfo("提示", "没有找到可显示的实体")
            else:
                messagebox.showinfo("提示", "没有加载的实体数据")

        except Exception as e:
            print(f"❌ 适应视图失败: {e}")
            messagebox.showerror("错误", f"适应视图失败: {e}")

    def save_current_view(self):
        """保存当前视图为图像"""
        try:
            if not self.processor or not self.processor.visualizer:
                messagebox.showwarning("警告", "请先加载CAD文件")
                return

            # 选择保存路径
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="保存图像",
                defaultextension=".png",
                filetypes=[
                    ("PNG图像", "*.png"),
                    ("JPEG图像", "*.jpg"),
                    ("PDF文件", "*.pdf"),
                    ("SVG文件", "*.svg"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return

            # 保存图像
            if hasattr(self.processor.visualizer, 'fig'):
                # 保存整个图形
                self.processor.visualizer.fig.savefig(
                    file_path,
                    dpi=300,  # 高分辨率
                    bbox_inches='tight',  # 紧凑布局
                    facecolor='white',  # 白色背景
                    edgecolor='none'
                )

                print(f"💾 图像已保存: {file_path}")
                messagebox.showinfo("成功", f"图像已保存到:\n{file_path}")

            else:
                messagebox.showerror("错误", "没有可保存的图像")

        except Exception as e:
            print(f"❌ 保存图像失败: {e}")
            messagebox.showerror("错误", f"保存图像失败: {e}")

    def enable_zoom_pan(self):
        """启用缩放和平移功能"""
        try:
            if not self.processor or not self.processor.visualizer:
                return

            # 为matplotlib画布添加交互功能
            if hasattr(self.processor.visualizer, 'fig') and self.processor.canvas:
                # 启用matplotlib的导航工具栏功能
                from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk

                # 检查是否已经有工具栏
                if not hasattr(self, 'nav_toolbar'):
                    # 创建导航工具栏
                    toolbar_frame = Frame(self.canvas_frame)
                    toolbar_frame.pack(side='bottom', fill='x')

                    self.nav_toolbar = NavigationToolbar2Tk(self.processor.canvas, toolbar_frame)
                    self.nav_toolbar.update()

                    print("✅ 缩放平移工具栏已启用")

        except Exception as e:
            print(f"❌ 启用缩放平移功能失败: {e}")

    def add_mouse_zoom_support(self):
        """添加鼠标滚轮缩放支持"""
        try:
            if not self.processor or not self.processor.canvas:
                return

            def on_scroll(event):
                """鼠标滚轮事件处理"""
                try:
                    if event.inaxes:
                        # 获取当前鼠标位置
                        x_mouse, y_mouse = event.xdata, event.ydata

                        # 获取当前视图范围
                        xlim = event.inaxes.get_xlim()
                        ylim = event.inaxes.get_ylim()

                        # 缩放因子
                        if event.button == 'up':
                            scale_factor = 0.9  # 放大
                        elif event.button == 'down':
                            scale_factor = 1.1  # 缩小
                        else:
                            return

                        # 计算新的视图范围（以鼠标位置为中心）
                        width = (xlim[1] - xlim[0]) * scale_factor
                        height = (ylim[1] - ylim[0]) * scale_factor

                        new_xlim = [x_mouse - (x_mouse - xlim[0]) * scale_factor,
                                   x_mouse + (xlim[1] - x_mouse) * scale_factor]
                        new_ylim = [y_mouse - (y_mouse - ylim[0]) * scale_factor,
                                   y_mouse + (ylim[1] - y_mouse) * scale_factor]

                        # 应用新的视图范围
                        event.inaxes.set_xlim(new_xlim)
                        event.inaxes.set_ylim(new_ylim)

                        # 更新画布
                        self.processor.canvas.draw()

                except Exception as e:
                    print(f"鼠标滚轮缩放失败: {e}")

            # 连接滚轮事件
            self.processor.canvas.mpl_connect('scroll_event', on_scroll)
            print("✅ 鼠标滚轮缩放已启用")

        except Exception as e:
            print(f"❌ 添加鼠标缩放支持失败: {e}")

    def _delayed_init_zoom(self):
        """延迟初始化缩放功能"""
        try:
            # 等待处理器创建后再启用缩放功能
            if self.processor and self.processor.canvas:
                self.add_mouse_zoom_support()
                print("✅ 延迟初始化缩放功能完成")
            else:
                # 如果还没准备好，再等一会儿
                self.root.after(2000, self._delayed_init_zoom)
        except Exception as e:
            print(f"❌ 延迟初始化缩放功能失败: {e}")

def main():
    """主程序入口"""
    try:
        print("正在启动CAD分类标注工具 - 增强版...")
        
        # 设置matplotlib后端
        import matplotlib
        matplotlib.use('TkAgg')
        
        root = tk.Tk()
        app = EnhancedCADApp(root)
        
        print("程序启动成功，开始主循环...")
        root.mainloop()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
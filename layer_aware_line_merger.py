#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图层感知线条合并器
基于测试结果改进，确保线条合并时保留图层信息，避免跨图层合并
"""

import copy
import time
from typing import List, Dict, Any, Optional
from collections import defaultdict

try:
    from line_merger import SimpleLineMerger
    LINE_MERGER_AVAILABLE = True
except ImportError:
    LINE_MERGER_AVAILABLE = False
    print("⚠️ 原始线条合并器不可用")


class LayerAwareLineMerger:
    """
    图层感知的线条合并器
    
    核心改进：
    1. 按图层分组后再合并，避免跨图层合并
    2. 保留完整的图层信息和实体属性
    3. 提供详细的合并统计和追踪
    4. 支持特殊图层的定制化合并策略
    """
    
    def __init__(self, distance_threshold=5, angle_threshold=2):
        """
        初始化图层感知线条合并器
        
        Args:
            distance_threshold: 距离阈值
            angle_threshold: 角度阈值
        """
        self.distance_threshold = distance_threshold
        self.angle_threshold = angle_threshold
        
        # 图层特定配置
        self.layer_configs = {
            'wall_layers': {
                'patterns': ['wall', '墙', 'a-wall', 'arch-wall'],
                'merge_enabled': True,
                'distance_threshold': 3,  # 墙体使用更严格的阈值
                'angle_threshold': 1,
                'preserve_endpoints': True
            },
            'door_window_layers': {
                'patterns': ['door', 'window', '门', '窗', 'a-door', 'a-wind'],
                'merge_enabled': True,
                'distance_threshold': 8,
                'angle_threshold': 3,
                'preserve_endpoints': False
            },
            'railing_layers': {
                'patterns': ['rail', '栏杆', '护栏', 'a-rail'],
                'merge_enabled': True,
                'distance_threshold': 5,
                'angle_threshold': 2,
                'preserve_endpoints': True
            },
            'text_layers': {
                'patterns': ['text', '文字', '标注', 'a-anno', 'anno'],
                'merge_enabled': False,  # 文字图层不合并
                'preserve_original': True
            },
            'dimension_layers': {
                'patterns': ['dim', 'dimension', '尺寸', 'a-dims'],
                'merge_enabled': False,  # 标注图层不合并
                'preserve_original': True
            }
        }
        
        # 合并统计
        self.merge_stats = {
            'total_entities_input': 0,
            'total_entities_output': 0,
            'layers_processed': 0,
            'layer_details': {},
            'processing_time': 0.0,
            'cross_layer_prevention_count': 0
        }
        
        print("🔧 图层感知线条合并器初始化完成")
    
    def merge_entities_by_layer(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        按图层分组合并实体
        
        Args:
            entities: 输入实体列表
            
        Returns:
            合并后的实体列表
        """
        print(f"🔄 开始图层感知线条合并: {len(entities)} 个实体")
        start_time = time.time()
        
        # 重置统计
        self.merge_stats = {
            'total_entities_input': len(entities),
            'total_entities_output': 0,
            'layers_processed': 0,
            'layer_details': {},
            'processing_time': 0.0,
            'cross_layer_prevention_count': 0
        }
        
        # 按图层分组
        layer_groups = self._group_entities_by_layer(entities)
        
        print(f"   📋 发现 {len(layer_groups)} 个图层")
        
        merged_entities = []
        
        for layer_name, layer_entities in layer_groups.items():
            print(f"   🔧 处理图层: {layer_name} ({len(layer_entities)} 个实体)")
            
            # 获取图层配置
            layer_config = self._get_layer_config(layer_name)
            
            # 处理图层实体
            layer_merged = self._process_layer_entities(
                layer_entities, layer_name, layer_config
            )
            
            merged_entities.extend(layer_merged)
            
            # 更新统计
            self.merge_stats['layer_details'][layer_name] = {
                'input_count': len(layer_entities),
                'output_count': len(layer_merged),
                'merge_enabled': layer_config.get('merge_enabled', True),
                'config_used': layer_config
            }
        
        # 完成统计
        self.merge_stats['total_entities_output'] = len(merged_entities)
        self.merge_stats['layers_processed'] = len(layer_groups)
        self.merge_stats['processing_time'] = time.time() - start_time
        
        print(f"✅ 图层感知合并完成: {len(entities)} -> {len(merged_entities)} 个实体")
        print(f"   处理时间: {self.merge_stats['processing_time']:.3f} 秒")
        print(f"   图层数: {len(layer_groups)}")
        
        return merged_entities
    
    def _group_entities_by_layer(self, entities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按图层分组实体"""
        layer_groups = defaultdict(list)
        
        for entity in entities:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                # 创建实体副本，确保原始数据不被修改
                entity_copy = copy.deepcopy(entity)
                entity_copy['original_layer'] = layer  # 保存原始图层信息
                layer_groups[layer].append(entity_copy)
        
        return dict(layer_groups)
    
    def _get_layer_config(self, layer_name: str) -> Dict[str, Any]:
        """获取图层配置"""
        layer_name_lower = layer_name.lower()
        
        # 检查各种图层类型
        for layer_type, config in self.layer_configs.items():
            patterns = config.get('patterns', [])
            if any(pattern in layer_name_lower for pattern in patterns):
                return config
        
        # 默认配置
        return {
            'merge_enabled': True,
            'distance_threshold': self.distance_threshold,
            'angle_threshold': self.angle_threshold,
            'preserve_endpoints': False
        }
    
    def _process_layer_entities(self, entities: List[Dict[str, Any]], 
                               layer_name: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理单个图层的实体"""
        
        # 检查是否启用合并
        if not config.get('merge_enabled', True):
            print(f"     📝 图层 {layer_name} 禁用合并，保持原状")
            return self._preserve_entities(entities, layer_name)
        
        # 分离线条和非线条实体
        line_entities = []
        non_line_entities = []
        
        for entity in entities:
            if self._is_line_entity(entity):
                line_entities.append(entity)
            else:
                non_line_entities.append(entity)
        
        print(f"     线条实体: {len(line_entities)}, 非线条实体: {len(non_line_entities)}")
        
        # 合并线条实体
        if line_entities and LINE_MERGER_AVAILABLE:
            merged_lines = self._merge_line_entities(line_entities, config)
        else:
            merged_lines = line_entities
        
        # 处理非线条实体
        processed_non_lines = self._preserve_entities(non_line_entities, layer_name)
        
        # 合并结果
        result = merged_lines + processed_non_lines
        
        print(f"     合并结果: {len(line_entities)} 线条 -> {len(merged_lines)}, "
              f"{len(non_line_entities)} 其他 -> {len(processed_non_lines)}")
        
        return result
    
    def _merge_line_entities(self, line_entities: List[Dict[str, Any]],
                           config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """合并线条实体（修复版：处理LineString对象和性能优化）"""
        if not line_entities:
            return []

        # 提取线条坐标
        line_coords = []
        entity_map = {}  # 坐标到实体的映射

        for i, entity in enumerate(line_entities):
            coords = self._extract_line_coordinates(entity)
            if coords:
                line_coords.append(coords)
                entity_map[i] = entity

        if not line_coords:
            return line_entities

        # 使用原始线条合并器
        merger = SimpleLineMerger(
            distance_threshold=config.get('distance_threshold', self.distance_threshold),
            angle_threshold=config.get('angle_threshold', self.angle_threshold)
        )

        try:
            merged_coords = merger.merge_lines(line_coords)

            # 重建实体，保留图层信息
            merged_entities = []
            for i, coords_obj in enumerate(merged_coords):
                # 处理不同类型的坐标对象
                if hasattr(coords_obj, 'coords'):
                    # LineString 对象
                    coords = list(coords_obj.coords)
                elif isinstance(coords_obj, (list, tuple)):
                    # 普通坐标列表
                    coords = coords_obj
                else:
                    # 其他类型，尝试转换
                    try:
                        coords = list(coords_obj)
                    except:
                        print(f"     ⚠️ 无法处理坐标对象类型: {type(coords_obj)}")
                        continue

                # 确保坐标格式正确
                if len(coords) < 2:
                    continue

                # 创建新的合并实体
                merged_entity = {
                    'type': 'LINE',
                    'layer': line_entities[0]['layer'],  # 保持原图层
                    'original_layer': line_entities[0]['layer'],
                    'points': coords,
                    'start_point': coords[0],
                    'end_point': coords[-1],
                    'merged_from_count': len(line_entities),
                    'merged_by': 'LayerAwareLineMerger',
                    'merge_timestamp': time.time(),
                    'layer_preserved': True
                }

                # 继承第一个实体的其他属性
                first_entity = line_entities[0]
                for key in ['color', 'linetype', 'lineweight']:
                    if key in first_entity:
                        merged_entity[key] = first_entity[key]

                merged_entities.append(merged_entity)

            return merged_entities

        except Exception as e:
            print(f"     ⚠️ 线条合并失败: {e}")
            import traceback
            traceback.print_exc()
            return line_entities
    
    def _preserve_entities(self, entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """保持实体原状，添加处理标记"""
        preserved = []
        
        for entity in entities:
            entity_copy = copy.deepcopy(entity)
            entity_copy['layer_preserved'] = True
            entity_copy['processed_by'] = 'LayerAwareLineMerger'
            entity_copy['processing_timestamp'] = time.time()
            entity_copy['merge_skipped_reason'] = 'preserve_original'
            preserved.append(entity_copy)
        
        return preserved
    
    def _is_line_entity(self, entity: Dict[str, Any]) -> bool:
        """判断是否为线条实体"""
        entity_type = entity.get('type', '').upper()
        line_types = ['LINE', 'LWPOLYLINE', 'POLYLINE']
        return entity_type in line_types
    
    def _extract_line_coordinates(self, entity: Dict[str, Any]) -> Optional[List[List[float]]]:
        """提取线条坐标"""
        if 'points' in entity:
            return entity['points']
        elif 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        else:
            return None
    
    def get_merge_statistics(self) -> Dict[str, Any]:
        """获取合并统计信息"""
        return {
            'merge_stats': self.merge_stats,
            'layer_configs': self.layer_configs,
            'cross_layer_prevention': {
                'enabled': True,
                'prevention_count': self.merge_stats.get('cross_layer_prevention_count', 0),
                'description': '通过按图层分组处理，完全避免跨图层合并'
            }
        }
    
    def validate_merge_result(self, original_entities: List[Dict[str, Any]], 
                            merged_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证合并结果"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 检查图层信息保留
        original_layers = set()
        merged_layers = set()
        
        for entity in original_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                original_layers.add(entity['layer'])
        
        for entity in merged_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                merged_layers.add(entity['layer'])
        
        # 验证图层完整性
        missing_layers = original_layers - merged_layers
        if missing_layers:
            validation['errors'].append(f"丢失图层: {missing_layers}")
            validation['is_valid'] = False
        
        # 验证跨图层合并
        cross_layer_entities = 0
        for entity in merged_entities:
            if isinstance(entity, dict):
                if entity.get('layer') != entity.get('original_layer'):
                    cross_layer_entities += 1
        
        if cross_layer_entities > 0:
            validation['warnings'].append(f"{cross_layer_entities} 个实体的图层信息不一致")
        
        # 统计信息
        validation['statistics'] = {
            'original_entities': len(original_entities),
            'merged_entities': len(merged_entities),
            'original_layers': len(original_layers),
            'merged_layers': len(merged_layers),
            'layer_preservation_rate': len(merged_layers) / len(original_layers) if original_layers else 1.0,
            'cross_layer_entities': cross_layer_entities
        }
        
        return validation


# 向后兼容的包装器
class EnhancedLineMerger(LayerAwareLineMerger):
    """增强线条合并器 - 向后兼容包装器"""
    
    def __init__(self, distance_threshold=5, angle_threshold=2):
        super().__init__(distance_threshold, angle_threshold)
        print("🔄 使用增强线条合并器（图层感知）")
    
    def merge_lines(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """向后兼容的合并方法"""
        return self.merge_entities_by_layer(entities)


# 工厂函数
def create_layer_aware_merger(distance_threshold=5, angle_threshold=2) -> LayerAwareLineMerger:
    """创建图层感知线条合并器"""
    return LayerAwareLineMerger(distance_threshold, angle_threshold)


def create_enhanced_merger(distance_threshold=5, angle_threshold=2) -> EnhancedLineMerger:
    """创建增强线条合并器（向后兼容）"""
    return EnhancedLineMerger(distance_threshold, angle_threshold)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概览图颜色显示问题诊断脚本
"""

def test_entity_color_scheme():
    """测试实体配色方案"""
    print("🎨 测试实体配色方案...")
    
    # 模拟配色方案
    color_scheme = {
        'wall': '#8B4513',
        'door_window': '#FFD700', 
        'furniture': '#4DB6AC',
        'other': '#808080'
    }
    
    # 模拟实体数据
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'wall'},
        {'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window'},
        {'type': 'INSERT', 'layer': '0', 'label': 'furniture'},
        {'type': 'LINE', 'layer': '0', 'label': None},  # 无标签
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'None'},  # 字符串'None'
    ]
    
    def get_entity_color(entity):
        """模拟颜色获取逻辑"""
        # 检查标签
        label = entity.get('label')
        if label and label != 'None' and label != 'none':
            label_color_map = {
                'wall': color_scheme.get('wall', '#8B4513'),
                'door_window': color_scheme.get('door_window', '#FFD700'),
                'furniture': color_scheme.get('furniture', '#4DB6AC'),
            }
            return label_color_map.get(label, color_scheme.get('other', '#808080'))
        
        # 根据图层判断
        layer_name = str(entity.get('layer', '')).lower()
        if 'wall' in layer_name or 'a-wall' in layer_name:
            return color_scheme.get('wall', '#8B4513')
        elif 'door' in layer_name or 'window' in layer_name:
            return color_scheme.get('door_window', '#FFD700')
        else:
            return color_scheme.get('other', '#808080')
    
    print("实体颜色测试结果:")
    for i, entity in enumerate(test_entities):
        color = get_entity_color(entity)
        print(f"  实体{i+1}: {entity} -> 颜色: {color}")
    
    return True

def test_group_status_detection():
    """测试组状态检测"""
    print("🔍 测试组状态检测...")
    
    # 模拟组数据
    all_groups = [
        [{'type': 'LINE', 'label': 'wall', 'auto_labeled': True}],  # 自动标注组
        [{'type': 'LINE', 'label': 'furniture', 'auto_labeled': False}],  # 手动标注组
        [{'type': 'LINE', 'label': None}],  # 未标注组
    ]
    
    # 模拟组信息
    groups_info = [
        {'index': 0, 'status': 'auto_labeled', 'label': 'wall'},
        {'index': 1, 'status': 'labeled', 'label': 'furniture'},
        {'index': 2, 'status': 'unlabeled', 'label': '未标注'},
    ]
    
    def get_entity_group_status(entity, all_groups, groups_info):
        """模拟组状态检测"""
        for i, group in enumerate(all_groups):
            if entity in group:
                if i < len(groups_info):
                    group_info = groups_info[i]
                    return group_info['status'], group_info['label']
        return None
    
    print("组状态检测结果:")
    for i, group in enumerate(all_groups):
        for entity in group:
            status_info = get_entity_group_status(entity, all_groups, groups_info)
            print(f"  组{i+1}实体: {entity} -> 状态: {status_info}")
    
    return True

def test_color_display_pipeline():
    """测试颜色显示管道"""
    print("🔄 测试颜色显示管道...")
    
    # 模拟完整的颜色获取流程
    def simulate_get_entity_display_info(entity, processor=None):
        """模拟 _get_entity_display_info_enhanced 方法"""
        
        # 1. 检查实体标签
        entity_label = entity.get('label')
        if entity_label == 'None' or entity_label == 'none':
            entity_label = None
        
        auto_labeled = entity.get('auto_labeled', False)
        
        # 2. 根据标签和状态确定颜色
        category_colors = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'furniture': '#4DB6AC',
        }
        
        if entity_label and entity_label in category_colors:
            if auto_labeled:
                status = 'auto_labeled'
                color = category_colors[entity_label]
                alpha = 0.7
            else:
                status = 'labeled'
                color = category_colors[entity_label]
                alpha = 0.9
        else:
            # 3. 使用处理器的颜色方案
            status = 'unlabeled'
            if processor and hasattr(processor, '_get_entity_color_from_scheme_enhanced'):
                try:
                    color = processor._get_entity_color_from_scheme_enhanced(entity)
                    alpha = 0.7
                except:
                    color = '#808080'  # 默认灰色
                    alpha = 0.7
            else:
                color = '#808080'  # 默认灰色
                alpha = 0.7
        
        return color, alpha, 1.0, status
    
    # 模拟处理器
    class MockProcessor:
        def __init__(self):
            self.current_color_scheme = {
                'wall': '#8B4513',
                'door_window': '#FFD700',
                'furniture': '#4DB6AC',
                'other': '#808080'
            }
        
        def _get_entity_color_from_scheme_enhanced(self, entity):
            label = entity.get('label')
            if label and label in self.current_color_scheme:
                return self.current_color_scheme[label]
            
            layer_name = str(entity.get('layer', '')).lower()
            if 'wall' in layer_name:
                return self.current_color_scheme['wall']
            elif 'door' in layer_name or 'window' in layer_name:
                return self.current_color_scheme['door_window']
            else:
                return self.current_color_scheme['other']
    
    processor = MockProcessor()
    
    # 测试实体
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'wall', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window', 'auto_labeled': False},
        {'type': 'LINE', 'layer': '0', 'label': None},
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'None'},  # 字符串'None'
    ]
    
    print("颜色显示管道测试结果:")
    for i, entity in enumerate(test_entities):
        color, alpha, linewidth, status = simulate_get_entity_display_info(entity, processor)
        print(f"  实体{i+1}: {entity}")
        print(f"    -> 颜色: {color}, 透明度: {alpha}, 状态: {status}")
        
        # 检查是否为灰色
        if color == '#808080':
            print(f"    ⚠️ 显示为灰色！可能的原因:")
            if not entity.get('label') or entity.get('label') in ['None', 'none']:
                print(f"      - 实体无有效标签")
            if not hasattr(processor, '_get_entity_color_from_scheme_enhanced'):
                print(f"      - 处理器缺少颜色方案方法")
    
    return True

def test_processor_integration():
    """测试处理器集成"""
    print("🔗 测试处理器集成...")
    
    # 检查关键方法是否存在
    required_methods = [
        '_get_entity_color_from_scheme_enhanced',
        'current_color_scheme',
    ]
    
    # 模拟检查
    class MockMainProcessor:
        def __init__(self):
            self.current_color_scheme = {'wall': '#8B4513', 'other': '#808080'}
        
        def _get_entity_color_from_scheme_enhanced(self, entity):
            return self.current_color_scheme.get('other', '#808080')
    
    processor = MockMainProcessor()
    
    print("处理器方法检查:")
    for method in required_methods:
        if hasattr(processor, method):
            print(f"  ✅ {method}: 存在")
        else:
            print(f"  ❌ {method}: 缺失")
    
    # 测试方法调用
    test_entity = {'type': 'LINE', 'layer': '0', 'label': None}
    try:
        color = processor._get_entity_color_from_scheme_enhanced(test_entity)
        print(f"  颜色获取测试: {color}")
        if color == '#808080':
            print(f"  ⚠️ 返回默认灰色，可能配色方案未正确应用")
    except Exception as e:
        print(f"  ❌ 颜色获取失败: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🔍 概览图颜色显示问题诊断")
    print("=" * 50)
    
    tests = [
        ("实体配色方案", test_entity_color_scheme),
        ("组状态检测", test_group_status_detection),
        ("颜色显示管道", test_color_display_pipeline),
        ("处理器集成", test_processor_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"结果: {status}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 诊断总结:")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print("\n🔍 可能的问题原因:")
    print("1. 实体标签为字符串'None'而不是None值")
    print("2. 处理器的颜色方案方法未被正确调用")
    print("3. 配色方案数据未正确传递到可视化器")
    print("4. 组状态信息不准确或缺失")
    print("5. 显示控制器的配色匹配逻辑有问题")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主程序集成模块
将增强的CAD数据处理器集成到主程序中，提供无缝的升级路径
"""

import time
from typing import List, Dict, Any, Optional

try:
    from enhanced_cad_processor import EnhancedCADProcessor
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSOR_AVAILABLE = False
    print("⚠️ 增强CAD数据处理器不可用")

try:
    from cad_data_processor import CADDataProcessor
    TRADITIONAL_PROCESSOR_AVAILABLE = True
except ImportError:
    TRADITIONAL_PROCESSOR_AVAILABLE = False
    print("⚠️ 传统CAD数据处理器不可用")


class MainProgramIntegration:
    """
    主程序集成类
    
    功能：
    1. 无缝集成增强处理器到现有主程序
    2. 提供向后兼容性
    3. 支持渐进式升级
    4. 自动选择最佳处理方式
    """
    
    def __init__(self, prefer_enhanced=True, auto_fallback=True):
        """
        初始化主程序集成
        
        Args:
            prefer_enhanced: 优先使用增强处理器
            auto_fallback: 自动回退到传统处理器
        """
        self.prefer_enhanced = prefer_enhanced
        self.auto_fallback = auto_fallback
        
        # 初始化处理器
        self.enhanced_processor = None
        self.traditional_processor = None
        self.active_processor = None
        
        self._initialize_processors()
        
        # 集成统计
        self.integration_stats = {
            'enhanced_usage_count': 0,
            'traditional_usage_count': 0,
            'fallback_count': 0,
            'total_processing_time': 0.0,
            'current_mode': self._get_current_mode()
        }
        
        print(f"🔗 主程序集成初始化完成 (模式: {self.integration_stats['current_mode']})")
    
    def _initialize_processors(self):
        """初始化处理器"""
        # 尝试初始化增强处理器
        if ENHANCED_PROCESSOR_AVAILABLE:
            try:
                self.enhanced_processor = EnhancedCADProcessor(use_enhanced_architecture=False)
                print("✅ 增强处理器初始化成功")
            except Exception as e:
                print(f"⚠️ 增强处理器初始化失败: {e}")
                self.enhanced_processor = None
        
        # 尝试初始化传统处理器
        if TRADITIONAL_PROCESSOR_AVAILABLE:
            try:
                self.traditional_processor = CADDataProcessor()
                print("✅ 传统处理器初始化成功")
            except Exception as e:
                print(f"⚠️ 传统处理器初始化失败: {e}")
                self.traditional_processor = None
        
        # 选择活动处理器
        if self.prefer_enhanced and self.enhanced_processor:
            self.active_processor = self.enhanced_processor
        elif self.traditional_processor:
            self.active_processor = self.traditional_processor
        else:
            self.active_processor = None
            print("❌ 没有可用的处理器")
    
    def _get_current_mode(self) -> str:
        """获取当前模式"""
        if self.enhanced_processor and self.active_processor == self.enhanced_processor:
            return "enhanced"
        elif self.traditional_processor and self.active_processor == self.traditional_processor:
            return "traditional"
        else:
            return "none"
    
    def process_entities(self, entities: List[Dict[str, Any]], 
                        file_path: str = None,
                        distance_threshold: float = 20,
                        force_traditional: bool = False) -> Dict[str, Any]:
        """
        处理实体 - 主要接口方法
        
        Args:
            entities: 实体列表
            file_path: 文件路径
            distance_threshold: 距离阈值
            force_traditional: 强制使用传统处理器
            
        Returns:
            处理结果字典
        """
        print(f"\n🔗 主程序集成处理: {len(entities)} 个实体")
        start_time = time.time()
        
        # 选择处理器
        if force_traditional or not self.enhanced_processor:
            processor = self.traditional_processor
            mode = "traditional"
        else:
            processor = self.enhanced_processor
            mode = "enhanced"
        
        if not processor:
            return self._create_error_result("没有可用的处理器", entities)
        
        try:
            print(f"   使用处理器: {mode}")
            
            # 执行处理
            if mode == "enhanced":
                result = processor.process_entities(entities, file_path, distance_threshold)
                self.integration_stats['enhanced_usage_count'] += 1
            else:
                result = self._process_with_traditional(processor, entities, distance_threshold)
                self.integration_stats['traditional_usage_count'] += 1
            
            # 添加集成信息
            result['integration_info'] = {
                'processor_used': mode,
                'integration_version': '1.0',
                'processing_timestamp': time.time()
            }
            
            processing_time = time.time() - start_time
            self.integration_stats['total_processing_time'] += processing_time
            
            print(f"✅ 主程序集成处理完成 ({mode}), 耗时 {processing_time:.3f} 秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            
            # 尝试回退
            if self.auto_fallback and mode == "enhanced" and self.traditional_processor:
                print("🔄 回退到传统处理器...")
                try:
                    result = self._process_with_traditional(
                        self.traditional_processor, entities, distance_threshold
                    )
                    result['integration_info'] = {
                        'processor_used': 'traditional_fallback',
                        'fallback_reason': str(e),
                        'integration_version': '1.0'
                    }
                    
                    self.integration_stats['fallback_count'] += 1
                    self.integration_stats['traditional_usage_count'] += 1
                    
                    print("✅ 回退处理成功")
                    return result
                    
                except Exception as fallback_error:
                    print(f"❌ 回退处理也失败: {fallback_error}")
            
            return self._create_error_result(str(e), entities)
    
    def _process_with_traditional(self, processor, entities: List[Dict[str, Any]], 
                                distance_threshold: float) -> Dict[str, Any]:
        """使用传统处理器处理"""
        # 执行分组
        groups = processor.group_entities(entities, distance_threshold=distance_threshold)
        
        # 简单的自动标注
        labeled_entities = []
        for group in groups:
            for entity in group:
                if isinstance(entity, dict):
                    entity_copy = entity.copy()
                    
                    # 基于图层的简单标注
                    layer = entity_copy.get('layer', '').lower()
                    if 'wall' in layer or '墙' in layer:
                        entity_copy['label'] = 'wall'
                    elif 'door' in layer or 'window' in layer or '门' in layer or '窗' in layer:
                        entity_copy['label'] = 'door_window'
                    elif 'text' in layer or '文字' in layer:
                        entity_copy['label'] = 'text'
                    else:
                        entity_copy['label'] = 'other'
                    
                    entity_copy['auto_labeled'] = True
                    entity_copy['confidence'] = 0.7
                    labeled_entities.append(entity_copy)
        
        return {
            'success': True,
            'entities': entities,
            'groups': groups,
            'labeled_entities': labeled_entities,
            'auto_labeled_entities': labeled_entities,
            'processing_mode': 'traditional',
            'total_entities': len(entities),
            'total_groups': len(groups),
            'total_labeled': len(labeled_entities)
        }
    
    def _create_error_result(self, error_message: str, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'error': error_message,
            'entities': entities,
            'groups': [],
            'labeled_entities': [],
            'auto_labeled_entities': [],
            'processing_mode': 'error',
            'total_entities': len(entities),
            'total_groups': 0,
            'total_labeled': 0
        }
    
    def update_legacy_processor_data(self, legacy_processor, result: Dict[str, Any]):
        """
        更新传统处理器的数据
        
        Args:
            legacy_processor: 传统处理器实例
            result: 处理结果
        """
        if not legacy_processor:
            return
        
        try:
            # 更新基本数据
            if hasattr(legacy_processor, 'current_file_entities'):
                legacy_processor.current_file_entities = result.get('entities', [])
            
            if hasattr(legacy_processor, 'all_groups'):
                legacy_processor.all_groups = result.get('groups', [])
            
            if hasattr(legacy_processor, 'auto_labeled_entities'):
                legacy_processor.auto_labeled_entities = result.get('auto_labeled_entities', [])
            
            if hasattr(legacy_processor, 'labeled_entities'):
                legacy_processor.labeled_entities = result.get('labeled_entities', [])
            
            if hasattr(legacy_processor, 'dataset'):
                legacy_processor.dataset = result.get('labeled_entities', [])
            
            # 更新处理器状态
            if hasattr(legacy_processor, 'processor') and hasattr(legacy_processor.processor, 'merged_entities'):
                legacy_processor.processor.merged_entities = result.get('entities', [])
            
            print(f"✅ 传统处理器数据已更新")
            
        except Exception as e:
            print(f"⚠️ 更新传统处理器数据失败: {e}")
    
    def get_integration_summary(self) -> Dict[str, Any]:
        """获取集成摘要"""
        summary = {
            'integration_stats': self.integration_stats,
            'processor_status': {
                'enhanced_available': ENHANCED_PROCESSOR_AVAILABLE,
                'traditional_available': TRADITIONAL_PROCESSOR_AVAILABLE,
                'enhanced_initialized': self.enhanced_processor is not None,
                'traditional_initialized': self.traditional_processor is not None,
                'active_processor': self._get_current_mode()
            },
            'configuration': {
                'prefer_enhanced': self.prefer_enhanced,
                'auto_fallback': self.auto_fallback
            }
        }
        
        # 添加处理器详细信息
        if self.enhanced_processor:
            try:
                summary['enhanced_processor_summary'] = self.enhanced_processor.get_processing_summary()
            except Exception:
                pass
        
        return summary
    
    def switch_to_enhanced(self) -> bool:
        """切换到增强处理器"""
        if self.enhanced_processor:
            self.active_processor = self.enhanced_processor
            self.prefer_enhanced = True
            self.integration_stats['current_mode'] = 'enhanced'
            print("🔄 已切换到增强处理器")
            return True
        else:
            print("⚠️ 增强处理器不可用")
            return False
    
    def switch_to_traditional(self) -> bool:
        """切换到传统处理器"""
        if self.traditional_processor:
            self.active_processor = self.traditional_processor
            self.prefer_enhanced = False
            self.integration_stats['current_mode'] = 'traditional'
            print("🔄 已切换到传统处理器")
            return True
        else:
            print("⚠️ 传统处理器不可用")
            return False
    
    def reset_stats(self):
        """重置统计信息"""
        self.integration_stats = {
            'enhanced_usage_count': 0,
            'traditional_usage_count': 0,
            'fallback_count': 0,
            'total_processing_time': 0.0,
            'current_mode': self._get_current_mode()
        }
        print("📊 集成统计信息已重置")


# 全局集成实例
_global_integration = None


def get_integration_instance(prefer_enhanced=True, auto_fallback=True) -> MainProgramIntegration:
    """获取全局集成实例"""
    global _global_integration
    if _global_integration is None:
        _global_integration = MainProgramIntegration(prefer_enhanced, auto_fallback)
    return _global_integration


def process_entities_integrated(entities: List[Dict[str, Any]], 
                              file_path: str = None,
                              distance_threshold: float = 20,
                              force_traditional: bool = False) -> Dict[str, Any]:
    """集成的实体处理函数"""
    integration = get_integration_instance()
    return integration.process_entities(entities, file_path, distance_threshold, force_traditional)


def update_legacy_processor_integrated(legacy_processor, result: Dict[str, Any]):
    """集成的传统处理器数据更新函数"""
    integration = get_integration_instance()
    integration.update_legacy_processor_data(legacy_processor, result)

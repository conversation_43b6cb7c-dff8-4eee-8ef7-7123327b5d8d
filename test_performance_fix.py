#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试独立图层处理器性能修复效果
验证深拷贝优化是否解决了241.5秒的性能问题
"""

import time
import copy
from typing import List, Dict, Any

try:
    from independent_layer_processor import IndependentLayerProcessor
    PROCESSOR_AVAILABLE = True
except ImportError:
    PROCESSOR_AVAILABLE = False
    print("⚠️ 独立图层处理器不可用")


def create_complex_test_entity(entity_id: int, layer: str) -> Dict[str, Any]:
    """创建复杂的测试实体，模拟真实CAD数据的复杂性"""
    return {
        'id': f'entity_{entity_id}',
        'type': 'LINE',
        'layer': layer,
        'points': [[i * 10.0, i * 20.0] for i in range(50)],  # 50个点，模拟复杂线条
        'start_point': [0.0, 0.0],
        'end_point': [490.0, 980.0],
        'color': 7,
        'linetype': 'CONTINUOUS',
        'lineweight': 0.25,
        'properties': {
            'complex_data': {
                'nested_dict': {
                    'deep_nested': {
                        'values': [i for i in range(100)],  # 100个数值
                        'metadata': {
                            'creation_time': time.time(),
                            'version': '1.0',
                            'tags': [f'tag_{i}' for i in range(20)]  # 20个标签
                        }
                    }
                }
            },
            'geometry_cache': {
                'bounding_box': [0.0, 0.0, 490.0, 980.0],
                'length': 1000.0,
                'area': 0.0
            }
        },
        'processing_history': [],
        'annotations': [f'annotation_{i}' for i in range(10)]  # 10个注释
    }


def create_test_dataset(entity_count: int = 296) -> List[Dict[str, Any]]:
    """创建测试数据集，模拟真实的296个实体"""
    print(f"🔧 创建包含 {entity_count} 个复杂实体的测试数据集...")
    
    entities = []
    layers = ['A-WALL', 'A-WINDOW', '0']  # 3个图层
    
    for i in range(entity_count):
        layer = layers[i % len(layers)]
        entity = create_complex_test_entity(i, layer)
        entities.append(entity)
    
    # 统计各图层实体数量
    layer_counts = {}
    for entity in entities:
        layer = entity['layer']
        layer_counts[layer] = layer_counts.get(layer, 0) + 1
    
    print(f"📊 测试数据集创建完成:")
    for layer, count in layer_counts.items():
        print(f"   {layer}: {count} 个实体")
    
    return entities


def test_deepcopy_performance(entities: List[Dict[str, Any]]) -> float:
    """测试深拷贝性能"""
    print(f"\n🔧 测试深拷贝性能...")
    start_time = time.time()
    
    copied_entities = []
    for entity in entities:
        entity_copy = copy.deepcopy(entity)
        copied_entities.append(entity_copy)
    
    deepcopy_time = time.time() - start_time
    print(f"   深拷贝 {len(entities)} 个复杂实体耗时: {deepcopy_time:.3f} 秒")
    
    return deepcopy_time


def test_shallow_copy_performance(entities: List[Dict[str, Any]]) -> float:
    """测试浅拷贝性能"""
    print(f"\n🚀 测试浅拷贝性能...")
    start_time = time.time()
    
    copied_entities = []
    for entity in entities:
        entity_copy = entity.copy()  # 浅拷贝
        # 只复制必要的嵌套数据结构
        if 'points' in entity and isinstance(entity['points'], list):
            entity_copy['points'] = entity['points'].copy()
        copied_entities.append(entity_copy)
    
    shallow_copy_time = time.time() - start_time
    print(f"   浅拷贝 {len(entities)} 个复杂实体耗时: {shallow_copy_time:.3f} 秒")
    
    return shallow_copy_time


def test_processor_performance(entities: List[Dict[str, Any]]) -> float:
    """测试处理器性能"""
    if not PROCESSOR_AVAILABLE:
        print("⚠️ 处理器不可用，跳过测试")
        return 0.0
    
    print(f"\n🔄 测试独立图层处理器性能...")
    processor = IndependentLayerProcessor()
    
    start_time = time.time()
    result = processor.process_entities(entities)
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理器性能测试结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(entities)}")
    print(f"   输出实体: {len(result)}")
    print(f"   减少实体: {len(entities) - len(result)}")
    print(f"   减少率: {(len(entities) - len(result)) / len(entities) * 100:.1f}%")
    
    return processing_time


def main():
    """主测试函数"""
    print("🚀 独立图层处理器性能修复测试")
    print("=" * 50)
    
    # 创建测试数据
    test_entities = create_test_dataset(296)
    
    # 测试深拷贝性能
    deepcopy_time = test_deepcopy_performance(test_entities)
    
    # 测试浅拷贝性能
    shallow_copy_time = test_shallow_copy_performance(test_entities)
    
    # 计算性能提升
    if deepcopy_time > 0:
        performance_improvement = (deepcopy_time - shallow_copy_time) / deepcopy_time * 100
        speedup_ratio = deepcopy_time / shallow_copy_time if shallow_copy_time > 0 else float('inf')
        
        print(f"\n📊 性能对比分析:")
        print(f"   深拷贝耗时: {deepcopy_time:.3f} 秒")
        print(f"   浅拷贝耗时: {shallow_copy_time:.3f} 秒")
        print(f"   性能提升: {performance_improvement:.1f}%")
        print(f"   加速比: {speedup_ratio:.1f}x")
        
        if performance_improvement > 90:
            print(f"   🎉 性能提升显著！")
        elif performance_improvement > 50:
            print(f"   ✅ 性能提升良好")
        else:
            print(f"   ⚠️ 性能提升有限")
    
    # 测试处理器整体性能
    processor_time = test_processor_performance(test_entities)
    
    # 最终评估
    print(f"\n🎯 性能修复效果评估:")
    if processor_time < 1.0:
        print(f"   ✅ 处理器性能优秀 ({processor_time:.3f}秒)")
        print(f"   🎉 成功解决241.5秒的性能问题！")
    elif processor_time < 10.0:
        print(f"   ✅ 处理器性能良好 ({processor_time:.3f}秒)")
        print(f"   🎉 显著改善了性能问题！")
    else:
        print(f"   ⚠️ 处理器性能仍需优化 ({processor_time:.3f}秒)")
        print(f"   🔧 可能还有其他性能瓶颈")


if __name__ == "__main__":
    main()

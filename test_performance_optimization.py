#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试性能优化效果
验证去重算法优化是否解决了性能问题
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from optimized_deduplication import OptimizedDeduplicator, HybridDeduplicator
    from independent_layer_processor import IndependentLayerProcessor
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_large_test_data(entity_count: int) -> List[Dict[str, Any]]:
    """创建大量测试数据"""
    entities = []
    
    # 创建大量线条实体（模拟A-WINDOW图层）
    for i in range(entity_count):
        # 每10个实体中有1个重复
        if i % 10 == 0 and i > 0:
            # 创建重复实体
            base_index = i - 1
            entities.append({
                'id': f'line_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [base_index * 10, 0],
                'end_point': [base_index * 10 + 5, 0],
                'color': 2,
                'points': [[base_index * 10, 0], [base_index * 10 + 5, 0]]
            })
        else:
            # 创建正常实体
            entities.append({
                'id': f'line_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 10, 0],
                'end_point': [i * 10 + 5, 0],
                'color': 2,
                'points': [[i * 10, 0], [i * 10 + 5, 0]]
            })
    
    return entities


def test_deduplication_algorithms():
    """测试不同去重算法的性能"""
    print("🧪 测试去重算法性能对比")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    # 测试不同规模的数据
    test_sizes = [50, 100, 154, 200]
    results = {}
    
    for size in test_sizes:
        print(f"\n📊 测试数据规模: {size} 个实体")
        print("-" * 40)
        
        test_entities = create_large_test_data(size)
        expected_duplicates = size // 10  # 每10个实体中有1个重复
        
        print(f"   输入实体: {len(test_entities)} 个")
        print(f"   预期重复: {expected_duplicates} 个")
        
        # 测试优化去重器
        print(f"\n   🚀 测试优化去重器...")
        optimized_deduplicator = OptimizedDeduplicator()
        
        start_time = time.time()
        optimized_result = optimized_deduplicator.deduplicate_entities(
            test_entities, 'A-WINDOW', 'performance_test'
        )
        optimized_time = time.time() - start_time
        
        optimized_removed = len(test_entities) - len(optimized_result)
        
        print(f"     处理时间: {optimized_time:.3f} 秒")
        print(f"     移除实体: {optimized_removed} 个")
        print(f"     输出实体: {len(optimized_result)} 个")
        
        # 测试混合去重器
        print(f"\n   🎯 测试混合去重器...")
        hybrid_deduplicator = HybridDeduplicator(threshold=50)
        
        start_time = time.time()
        hybrid_result = hybrid_deduplicator.deduplicate_entities(
            test_entities, 'A-WINDOW', 'performance_test'
        )
        hybrid_time = time.time() - start_time
        
        hybrid_removed = len(test_entities) - len(hybrid_result)
        
        print(f"     处理时间: {hybrid_time:.3f} 秒")
        print(f"     移除实体: {hybrid_removed} 个")
        print(f"     输出实体: {len(hybrid_result)} 个")
        
        # 记录结果
        results[size] = {
            'input_entities': len(test_entities),
            'expected_duplicates': expected_duplicates,
            'optimized': {
                'processing_time': optimized_time,
                'removed_entities': optimized_removed,
                'output_entities': len(optimized_result)
            },
            'hybrid': {
                'processing_time': hybrid_time,
                'removed_entities': hybrid_removed,
                'output_entities': len(hybrid_result)
            }
        }
    
    return results


def test_independent_processor_performance():
    """测试独立图层处理器的性能"""
    print("\n🧪 测试独立图层处理器性能")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    # 创建模拟真实场景的测试数据
    test_entities = []
    
    # A-WALL图层（208个实体）
    wall_entities = create_large_test_data(208)
    for entity in wall_entities:
        entity['layer'] = 'A-WALL'
    test_entities.extend(wall_entities)
    
    # A-WINDOW图层（154个实体）
    window_entities = create_large_test_data(154)
    for entity in window_entities:
        entity['layer'] = 'A-WINDOW'
    test_entities.extend(window_entities)
    
    # 0图层（122个实体）
    layer0_entities = create_large_test_data(122)
    for entity in layer0_entities:
        entity['layer'] = '0'
    test_entities.extend(layer0_entities)
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    print(f"   A-WALL: 208 个实体")
    print(f"   A-WINDOW: 154 个实体")
    print(f"   0: 122 个实体")
    
    # 创建独立图层处理器
    processor = IndependentLayerProcessor()
    
    print(f"\n🔄 执行独立图层处理...")
    start_time = time.time()
    
    processed_entities = processor.process_entities(test_entities)
    
    total_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   总处理时间: {total_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(processed_entities)}")
    
    # 获取详细的处理报告
    processing_report = processor.get_layer_processing_report()
    
    print(f"\n📊 图层处理详情:")
    for layer_name, details in processing_report['layer_details'].items():
        print(f"   {layer_name}:")
        print(f"     输入: {details['input_count']} -> 输出: {details['output_count']}")
        print(f"     处理时间: {details['processing_time']:.3f} 秒")
        print(f"     处理方式: {details['processing_method']}")
    
    return {
        'total_entities': len(test_entities),
        'processed_entities': len(processed_entities),
        'total_processing_time': total_time,
        'layer_details': processing_report['layer_details']
    }


def compare_with_original_performance():
    """与原始性能进行对比"""
    print("\n🧪 性能对比分析")
    print("=" * 60)
    
    # 原始性能数据（从用户反馈）
    original_performance = {
        'A-WINDOW': {'entities': 154, 'time': 109.0},
        '0': {'entities': 122, 'time': 61.0},
        'total_time': 711.0
    }
    
    print("📊 原始性能（问题版本）:")
    print(f"   A-WINDOW: 154个实体, 109.0秒")
    print(f"   0图层: 122个实体, 61.0秒")
    print(f"   总时间: 711.0秒")
    
    # 测试优化后的性能
    if not MODULES_AVAILABLE:
        print("❌ 无法测试优化性能")
        return None
    
    print(f"\n📈 优化后性能测试:")
    
    # 测试A-WINDOW图层
    window_entities = create_large_test_data(154)
    optimized_deduplicator = OptimizedDeduplicator()
    
    start_time = time.time()
    window_result = optimized_deduplicator.deduplicate_entities(
        window_entities, 'A-WINDOW', 'performance_test'
    )
    window_time = time.time() - start_time
    
    print(f"   A-WINDOW: 154个实体, {window_time:.3f}秒")
    
    # 测试0图层
    layer0_entities = create_large_test_data(122)
    
    start_time = time.time()
    layer0_result = optimized_deduplicator.deduplicate_entities(
        layer0_entities, '0', 'performance_test'
    )
    layer0_time = time.time() - start_time
    
    print(f"   0图层: 122个实体, {layer0_time:.3f}秒")
    
    total_optimized_time = window_time + layer0_time
    print(f"   总时间: {total_optimized_time:.3f}秒")
    
    # 计算性能提升
    window_speedup = original_performance['A-WINDOW']['time'] / window_time
    layer0_speedup = original_performance['0']['time'] / layer0_time
    total_speedup = original_performance['total_time'] / total_optimized_time
    
    print(f"\n🚀 性能提升:")
    print(f"   A-WINDOW: {window_speedup:.1f}x 提升")
    print(f"   0图层: {layer0_speedup:.1f}x 提升")
    print(f"   总体: {total_speedup:.1f}x 提升")
    
    return {
        'original': original_performance,
        'optimized': {
            'A-WINDOW': {'entities': 154, 'time': window_time},
            '0': {'entities': 122, 'time': layer0_time},
            'total_time': total_optimized_time
        },
        'speedup': {
            'A-WINDOW': window_speedup,
            '0': layer0_speedup,
            'total': total_speedup
        }
    }


def run_comprehensive_performance_test():
    """运行综合性能测试"""
    print("🚀 开始综合性能测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'algorithm_comparison': None,
        'processor_performance': None,
        'performance_comparison': None,
        'summary': {}
    }
    
    try:
        # 1. 测试去重算法性能
        results['algorithm_comparison'] = test_deduplication_algorithms()
        
        # 2. 测试独立处理器性能
        results['processor_performance'] = test_independent_processor_performance()
        
        # 3. 与原始性能对比
        results['performance_comparison'] = compare_with_original_performance()
        
        # 4. 生成总结
        total_time = time.time() - start_time
        
        # 分析性能提升
        if results['performance_comparison']:
            total_speedup = results['performance_comparison']['speedup']['total']
            performance_excellent = total_speedup > 100  # 100倍以上提升
        else:
            total_speedup = 0
            performance_excellent = False
        
        results['summary'] = {
            'total_test_time': total_time,
            'all_tests_completed': True,
            'performance_speedup': total_speedup,
            'performance_excellent': performance_excellent,
            'optimization_successful': total_speedup > 10
        }
        
        print(f"\n🎉 综合性能测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   性能提升: {total_speedup:.1f}x")
        print(f"   优化成功: {'是' if results['summary']['optimization_successful'] else '否'}")
        print(f"   性能优秀: {'是' if performance_excellent else '否'}")
        
        # 保存测试结果
        with open('performance_optimization_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: performance_optimization_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_performance_test()
    sys.exit(0 if success else 1)

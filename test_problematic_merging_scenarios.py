#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试问题性合并场景
专门测试可能导致跨图层错误合并的边界情况
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from cad_data_processor import CADDataProcessor
    from line_merger import SimpleLineMerger
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_problematic_scenario_1() -> List[Dict[str, Any]]:
    """
    场景1: 完全重合的不同图层线条
    墙体线条与构造线条完全重合，应该分别处理
    """
    return [
        # 墙体线条
        {
            'id': 'wall_main',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]]
        },
        # 构造线条（完全重合）
        {
            'id': 'construction_overlay',
            'type': 'LINE',
            'layer': 'CONSTRUCTION',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 9,
            'points': [[0, 0], [100, 0]]
        },
        # 门线条（部分重合）
        {
            'id': 'door_opening',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [30, 0],
            'end_point': [50, 0],
            'color': 2,
            'points': [[30, 0], [50, 0]]
        }
    ]


def create_problematic_scenario_2() -> List[Dict[str, Any]]:
    """
    场景2: 端点精确重合的不同图层线条
    不同图层的线条端点精确重合，可能被错误连接
    """
    return [
        # 墙体线条
        {
            'id': 'wall_segment_1',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [50, 0],
            'color': 1,
            'points': [[0, 0], [50, 0]]
        },
        {
            'id': 'wall_segment_2',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [50, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[50, 0], [100, 0]]
        },
        # 门窗线条（端点与墙体重合）
        {
            'id': 'door_frame_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [50, 0],  # 与墙体端点重合
            'end_point': [50, 20],
            'color': 2,
            'points': [[50, 0], [50, 20]]
        },
        # 栏杆线条（端点与墙体重合）
        {
            'id': 'railing_connect',
            'type': 'LINE',
            'layer': 'A-RAIL',
            'start_point': [0, 0],   # 与墙体端点重合
            'end_point': [0, -20],
            'color': 4,
            'points': [[0, 0], [0, -20]]
        },
        # 其他图层线条（端点接近）
        {
            'id': 'equipment_line',
            'type': 'LINE',
            'layer': 'EQUIPMENT',
            'start_point': [100, 0], # 与墙体端点重合
            'end_point': [120, 0],
            'color': 8,
            'points': [[100, 0], [120, 0]]
        }
    ]


def create_problematic_scenario_3() -> List[Dict[str, Any]]:
    """
    场景3: 距离阈值内的不同图层线条
    不同图层的线条在合并阈值距离内，可能被错误合并
    """
    return [
        # 墙体线条
        {
            'id': 'wall_base',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]]
        },
        # 平行的构造线条（距离5单位）
        {
            'id': 'construction_parallel',
            'type': 'LINE',
            'layer': 'CONSTRUCTION',
            'start_point': [0, 5],
            'end_point': [100, 5],
            'color': 9,
            'points': [[0, 5], [100, 5]]
        },
        # 平行的设备线条（距离10单位）
        {
            'id': 'equipment_parallel',
            'type': 'LINE',
            'layer': 'EQUIPMENT',
            'start_point': [0, 10],
            'end_point': [100, 10],
            'color': 8,
            'points': [[0, 10], [100, 10]]
        },
        # 平行的家具线条（距离15单位）
        {
            'id': 'furniture_parallel',
            'type': 'LINE',
            'layer': 'FURNITURE',
            'start_point': [0, 15],
            'end_point': [100, 15],
            'color': 7,
            'points': [[0, 15], [100, 15]]
        }
    ]


def create_problematic_scenario_4() -> List[Dict[str, Any]]:
    """
    场景4: 混合实体类型的跨图层情况
    包含线条、圆弧、文字等不同类型的实体
    """
    return [
        # 墙体线条
        {
            'id': 'wall_line',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1,
            'points': [[0, 0], [100, 0]]
        },
        # 门弧线（与墙体端点重合）
        {
            'id': 'door_arc',
            'type': 'ARC',
            'layer': 'A-DOOR',
            'center': [50, 0],
            'radius': 20,
            'start_angle': 0,
            'end_angle': 180,
            'color': 2
        },
        # 文字（位置接近墙体）
        {
            'id': 'room_text',
            'type': 'TEXT',
            'layer': 'A-ANNO-TEXT',
            'text': '房间',
            'position': [50, 5],  # 距离墙体5单位
            'height': 3,
            'color': 5
        },
        # 标注（引用墙体端点）
        {
            'id': 'wall_dimension',
            'type': 'DIMENSION',
            'layer': 'A-DIMS',
            'def_points': [[0, 0], [100, 0], [50, -10]],
            'text': '100',
            'color': 6
        },
        # 圆形设备（位置接近墙体）
        {
            'id': 'equipment_circle',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [50, 8],
            'radius': 5,
            'color': 8
        }
    ]


def test_scenario(scenario_name: str, entities: List[Dict[str, Any]], 
                 distance_threshold: float = 20) -> Dict[str, Any]:
    """
    测试特定场景的合并问题
    
    Args:
        scenario_name: 场景名称
        entities: 测试实体
        distance_threshold: 距离阈值
        
    Returns:
        测试结果
    """
    print(f"\n🧪 测试场景: {scenario_name}")
    print("=" * 60)
    
    # 显示输入数据
    print(f"📊 输入数据: {len(entities)} 个实体")
    layer_counts = {}
    for entity in entities:
        layer = entity['layer']
        layer_counts[layer] = layer_counts.get(layer, 0) + 1
        print(f"   {entity['id']} ({entity['type']}) - {layer}")
    
    print(f"\n📋 图层分布:")
    for layer, count in layer_counts.items():
        print(f"   {layer}: {count} 个实体")
    
    # 使用CAD数据处理器进行分组
    processor = CADDataProcessor()
    
    print(f"\n🔄 执行分组处理 (阈值: {distance_threshold})...")
    start_time = time.time()
    
    groups = processor.group_entities(entities, distance_threshold=distance_threshold, debug=False)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输出分组: {len(groups)} 个")
    
    # 详细分析每个组
    cross_layer_issues = []
    pure_layer_groups = 0
    
    for i, group in enumerate(groups):
        if not group:
            continue
            
        # 统计组内图层
        group_layers = set()
        group_types = set()
        entity_details = []
        
        for entity in group:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                entity_type = entity.get('type', 'UNKNOWN')
                entity_id = entity.get('id', f"entity_{i}")
                
                group_layers.add(layer)
                group_types.add(entity_type)
                entity_details.append({
                    'id': entity_id,
                    'type': entity_type,
                    'layer': layer
                })
        
        print(f"\n   组 {i + 1}: {len(group)} 个实体")
        print(f"     图层: {list(group_layers)}")
        print(f"     类型: {list(group_types)}")
        
        for detail in entity_details:
            print(f"       - {detail['id']} ({detail['type']}, {detail['layer']})")
        
        # 检查跨图层问题
        if len(group_layers) > 1:
            cross_layer_issues.append({
                'group_index': i,
                'layers': list(group_layers),
                'types': list(group_types),
                'entities': entity_details,
                'entity_count': len(group)
            })
            print(f"     🚨 跨图层组！")
        else:
            pure_layer_groups += 1
            print(f"     ✅ 纯图层组")
    
    # 总结分析
    print(f"\n🔍 跨图层合并分析:")
    print(f"   纯图层组: {pure_layer_groups}")
    print(f"   跨图层组: {len(cross_layer_issues)}")
    
    if cross_layer_issues:
        print(f"\n   🚨 发现的跨图层问题:")
        for issue in cross_layer_issues:
            print(f"     组 {issue['group_index'] + 1}: {issue['layers']} "
                  f"({issue['entity_count']} 个实体)")
    
    return {
        'scenario_name': scenario_name,
        'input_entities': len(entities),
        'input_layers': len(layer_counts),
        'output_groups': len(groups),
        'pure_layer_groups': pure_layer_groups,
        'cross_layer_groups': len(cross_layer_issues),
        'cross_layer_issues': cross_layer_issues,
        'processing_time': processing_time,
        'distance_threshold': distance_threshold
    }


def test_line_merger_scenarios():
    """测试线条合并器在各种场景下的表现"""
    print(f"\n🧪 测试线条合并器场景")
    print("=" * 60)
    
    scenarios = [
        ("完全重合线条", [
            [[0, 0], [100, 0]],  # 墙体线条
            [[0, 0], [100, 0]],  # 构造线条（完全重合）
            [[30, 0], [50, 0]]   # 门线条（部分重合）
        ]),
        ("端点连接线条", [
            [[0, 0], [50, 0]],   # 墙体段1
            [[50, 0], [100, 0]], # 墙体段2
            [[50, 0], [50, 20]]  # 门框（端点重合）
        ]),
        ("平行接近线条", [
            [[0, 0], [100, 0]],  # 墙体
            [[0, 5], [100, 5]],  # 构造线（距离5）
            [[0, 10], [100, 10]] # 设备线（距离10）
        ])
    ]
    
    merger = SimpleLineMerger(distance_threshold=8, angle_threshold=5)
    results = []
    
    for scenario_name, lines in scenarios:
        print(f"\n📋 场景: {scenario_name}")
        print(f"   输入线条: {len(lines)}")
        for i, line in enumerate(lines):
            print(f"     线条 {i + 1}: {line}")
        
        start_time = time.time()
        merged_lines = merger.merge_lines(lines)
        processing_time = time.time() - start_time
        
        print(f"   合并结果: {len(lines)} -> {len(merged_lines)}")
        print(f"   处理时间: {processing_time:.3f} 秒")
        
        for i, merged_line in enumerate(merged_lines):
            print(f"     合并线条 {i + 1}: {merged_line}")
        
        results.append({
            'scenario': scenario_name,
            'input_lines': len(lines),
            'output_lines': len(merged_lines),
            'processing_time': processing_time,
            'merger_stats': merger.stats.copy()
        })
    
    return results


def run_problematic_scenarios_test():
    """运行所有问题性场景测试"""
    print("🚀 开始问题性合并场景测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    # 测试场景
    scenarios = [
        ("完全重合线条场景", create_problematic_scenario_1()),
        ("端点精确重合场景", create_problematic_scenario_2()),
        ("距离阈值内场景", create_problematic_scenario_3()),
        ("混合实体类型场景", create_problematic_scenario_4())
    ]
    
    results = {
        'test_time': time.time(),
        'scenario_results': [],
        'line_merger_results': [],
        'summary': {}
    }
    
    try:
        # 测试各个场景
        for scenario_name, entities in scenarios:
            # 测试不同的距离阈值
            for threshold in [10, 20, 50]:
                result = test_scenario(f"{scenario_name} (阈值{threshold})", entities, threshold)
                results['scenario_results'].append(result)
        
        # 测试线条合并器
        results['line_merger_results'] = test_line_merger_scenarios()
        
        # 生成总结
        total_scenarios = len(results['scenario_results'])
        cross_layer_scenarios = sum(1 for r in results['scenario_results'] if r['cross_layer_groups'] > 0)
        
        results['summary'] = {
            'total_scenarios_tested': total_scenarios,
            'scenarios_with_cross_layer_issues': cross_layer_scenarios,
            'cross_layer_issue_rate': cross_layer_scenarios / total_scenarios if total_scenarios > 0 else 0,
            'total_test_time': time.time() - start_time
        }
        
        # 打印总结
        print(f"\n🎉 问题性场景测试完成")
        print("=" * 80)
        print(f"   总测试场景: {total_scenarios}")
        print(f"   发现跨图层问题的场景: {cross_layer_scenarios}")
        print(f"   跨图层问题率: {results['summary']['cross_layer_issue_rate']:.1%}")
        print(f"   总耗时: {results['summary']['total_test_time']:.2f} 秒")
        
        # 保存结果
        with open('problematic_scenarios_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: problematic_scenarios_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_problematic_scenarios_test()
    sys.exit(0 if success else 1)

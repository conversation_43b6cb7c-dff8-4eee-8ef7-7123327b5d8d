#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
线条处理模式选择器
提供不同的线条处理模式，解决性能和功能需求的平衡
"""

import time
from typing import List, Dict, Any, Optional
from enum import Enum

try:
    from layer_aware_line_merger import LayerAwareLineMerger
    LAYER_AWARE_AVAILABLE = True
except ImportError:
    LAYER_AWARE_AVAILABLE = False

try:
    from line_merger import SimpleLineMerger
    SIMPLE_MERGER_AVAILABLE = True
except ImportError:
    SIMPLE_MERGER_AVAILABLE = False

try:
    from selective_layer_processor import SelectiveLayerProcessor
    SELECTIVE_PROCESSOR_AVAILABLE = True
except ImportError:
    SELECTIVE_PROCESSOR_AVAILABLE = False

try:
    from independent_layer_processor import IndependentLayerProcessor
    INDEPENDENT_PROCESSOR_AVAILABLE = True
except ImportError:
    INDEPENDENT_PROCESSOR_AVAILABLE = False


class LineProcessingMode(Enum):
    """线条处理模式枚举"""
    SKIP = "skip"                           # 跳过线条处理
    FAST = "fast"                          # 快速模式（简单合并）
    LAYER_AWARE = "layer_aware"            # 图层感知模式（推荐）
    LAYER_AWARE_FAST = "layer_aware_fast"  # 图层感知快速模式
    SELECTIVE = "selective"                # 选择性处理模式（墙体迭代合并+其他去重）
    INDEPENDENT = "independent"            # 独立图层处理模式（每个图层单独处理）
    TRADITIONAL = "traditional"            # 传统模式
    CUSTOM = "custom"                      # 自定义模式


class LineProcessingModeManager:
    """
    线条处理模式管理器
    
    提供多种线条处理模式，用户可以根据需要选择：
    1. 跳过模式：不进行线条处理，最快
    2. 快速模式：简单线条合并，较快
    3. 图层感知模式：按图层分别处理，推荐
    4. 图层感知快速模式：图层感知+性能优化
    5. 传统模式：使用原始处理方式
    6. 自定义模式：用户自定义参数
    """
    
    def __init__(self):
        """初始化模式管理器"""
        self.current_mode = LineProcessingMode.INDEPENDENT
        self.custom_config = {}
        
        # 模式配置
        self.mode_configs = {
            LineProcessingMode.SKIP: {
                'name': '跳过处理',
                'description': '不进行线条处理，直接使用原始数据',
                'performance': '最快',
                'quality': '原始',
                'enabled': True
            },
            LineProcessingMode.FAST: {
                'name': '快速模式',
                'description': '简单线条合并，适合大文件快速预览',
                'performance': '快',
                'quality': '基础',
                'enabled': SIMPLE_MERGER_AVAILABLE,
                'distance_threshold': 10,
                'angle_threshold': 5,
                'max_iterations': 1
            },
            LineProcessingMode.LAYER_AWARE: {
                'name': '图层感知模式',
                'description': '按图层分别处理，保证数据准确性（推荐）',
                'performance': '中等',
                'quality': '高',
                'enabled': LAYER_AWARE_AVAILABLE,
                'distance_threshold': 5,
                'angle_threshold': 2,
                'max_iterations': 3
            },
            LineProcessingMode.LAYER_AWARE_FAST: {
                'name': '图层感知快速',
                'description': '图层感知处理+性能优化，平衡速度和质量',
                'performance': '较快',
                'quality': '较高',
                'enabled': LAYER_AWARE_AVAILABLE,
                'distance_threshold': 8,
                'angle_threshold': 3,
                'max_iterations': 2,
                'enable_performance_optimization': True
            },
            LineProcessingMode.SELECTIVE: {
                'name': '选择性处理',
                'description': '墙体图层迭代合并，其他图层去重处理（推荐）',
                'performance': '快',
                'quality': '高',
                'enabled': SELECTIVE_PROCESSOR_AVAILABLE,
                'distance_threshold': 5,
                'angle_threshold': 2,
                'wall_merge_enabled': True,
                'other_dedup_enabled': True
            },
            LineProcessingMode.INDEPENDENT: {
                'name': '独立图层处理',
                'description': '每个图层完全独立处理，墙体迭代合并+其他简单去重（推荐）',
                'performance': '快',
                'quality': '高',
                'enabled': INDEPENDENT_PROCESSOR_AVAILABLE,
                'distance_threshold': 5,
                'angle_threshold': 2,
                'layer_independence': True,
                'detailed_tracking': True
            },
            LineProcessingMode.TRADITIONAL: {
                'name': '传统模式',
                'description': '使用原始处理方式，兼容性最好',
                'performance': '中等',
                'quality': '中等',
                'enabled': SIMPLE_MERGER_AVAILABLE,
                'distance_threshold': 5,
                'angle_threshold': 2
            },
            LineProcessingMode.CUSTOM: {
                'name': '自定义模式',
                'description': '用户自定义处理参数',
                'performance': '可调',
                'quality': '可调',
                'enabled': True
            }
        }
        
        print("🔧 线条处理模式管理器初始化完成")
    
    def get_available_modes(self) -> List[Dict[str, Any]]:
        """获取可用的处理模式"""
        available = []
        for mode, config in self.mode_configs.items():
            if config['enabled']:
                available.append({
                    'mode': mode,
                    'name': config['name'],
                    'description': config['description'],
                    'performance': config['performance'],
                    'quality': config['quality']
                })
        return available
    
    def set_mode(self, mode: LineProcessingMode, custom_config: Dict[str, Any] = None):
        """设置处理模式"""
        if mode not in self.mode_configs:
            raise ValueError(f"未知的处理模式: {mode}")
        
        if not self.mode_configs[mode]['enabled']:
            raise ValueError(f"处理模式不可用: {mode}")
        
        self.current_mode = mode
        if custom_config:
            self.custom_config = custom_config
        
        print(f"🔄 切换到线条处理模式: {self.mode_configs[mode]['name']}")
    
    def process_entities(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        根据当前模式处理实体
        
        Args:
            entities: 输入实体列表
            
        Returns:
            处理结果字典
        """
        print(f"🔄 开始线条处理: {self.mode_configs[self.current_mode]['name']}")
        start_time = time.time()
        
        try:
            if self.current_mode == LineProcessingMode.SKIP:
                result = self._process_skip_mode(entities)
            elif self.current_mode == LineProcessingMode.FAST:
                result = self._process_fast_mode(entities)
            elif self.current_mode == LineProcessingMode.LAYER_AWARE:
                result = self._process_layer_aware_mode(entities)
            elif self.current_mode == LineProcessingMode.LAYER_AWARE_FAST:
                result = self._process_layer_aware_fast_mode(entities)
            elif self.current_mode == LineProcessingMode.SELECTIVE:
                result = self._process_selective_mode(entities)
            elif self.current_mode == LineProcessingMode.INDEPENDENT:
                result = self._process_independent_mode(entities)
            elif self.current_mode == LineProcessingMode.TRADITIONAL:
                result = self._process_traditional_mode(entities)
            elif self.current_mode == LineProcessingMode.CUSTOM:
                result = self._process_custom_mode(entities)
            else:
                raise ValueError(f"未实现的处理模式: {self.current_mode}")
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            result['processing_mode'] = self.current_mode.value
            
            print(f"✅ 线条处理完成: {len(entities)} -> {len(result['entities'])} 个实体, "
                  f"耗时 {processing_time:.3f} 秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 线条处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 返回原始数据
            return {
                'success': False,
                'error': str(e),
                'entities': entities,
                'processing_time': time.time() - start_time,
                'processing_mode': self.current_mode.value
            }
    
    def _process_skip_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """跳过处理模式"""
        print("  📋 跳过线条处理，直接返回原始数据")
        return {
            'success': True,
            'entities': entities,
            'mode_info': '跳过处理'
        }
    
    def _process_fast_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """快速处理模式"""
        print("  ⚡ 快速线条处理模式")
        
        if not SIMPLE_MERGER_AVAILABLE:
            return self._process_skip_mode(entities)
        
        config = self.mode_configs[LineProcessingMode.FAST]
        merger = SimpleLineMerger(
            distance_threshold=config['distance_threshold'],
            angle_threshold=config['angle_threshold']
        )
        
        # 简单合并，不区分图层
        line_entities = [e for e in entities if self._is_line_entity(e)]
        non_line_entities = [e for e in entities if not self._is_line_entity(e)]
        
        if line_entities:
            line_coords = [self._extract_line_coordinates(e) for e in line_entities if self._extract_line_coordinates(e)]
            if line_coords:
                merged_coords = merger.merge_lines(line_coords)
                merged_entities = self._rebuild_entities_from_coords(merged_coords, line_entities[0])
            else:
                merged_entities = line_entities
        else:
            merged_entities = []
        
        all_entities = merged_entities + non_line_entities
        
        return {
            'success': True,
            'entities': all_entities,
            'mode_info': f'快速合并: {len(line_entities)} -> {len(merged_entities)} 线条'
        }
    
    def _process_layer_aware_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """图层感知处理模式"""
        print("  🎯 图层感知线条处理模式")
        
        if not LAYER_AWARE_AVAILABLE:
            return self._process_fast_mode(entities)
        
        merger = LayerAwareLineMerger()
        merged_entities = merger.merge_entities_by_layer(entities)
        
        return {
            'success': True,
            'entities': merged_entities,
            'mode_info': '图层感知处理',
            'merge_stats': merger.get_merge_statistics()
        }
    
    def _process_layer_aware_fast_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """图层感知快速处理模式"""
        print("  🚀 图层感知快速处理模式")

        if not LAYER_AWARE_AVAILABLE:
            return self._process_fast_mode(entities)

        # 使用优化的参数
        config = self.mode_configs[LineProcessingMode.LAYER_AWARE_FAST]
        merger = LayerAwareLineMerger(
            distance_threshold=config['distance_threshold'],
            angle_threshold=config['angle_threshold']
        )

        # 性能优化：限制处理的实体数量
        if len(entities) > 1000:
            print(f"    ⚡ 性能优化：处理大量实体 ({len(entities)} 个)")
            # 可以在这里添加更多优化逻辑

        merged_entities = merger.merge_entities_by_layer(entities)

        return {
            'success': True,
            'entities': merged_entities,
            'mode_info': '图层感知快速处理',
            'merge_stats': merger.get_merge_statistics()
        }

    def _process_selective_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """选择性处理模式：墙体迭代合并+其他去重"""
        print("  🎯 选择性处理模式")

        if not SELECTIVE_PROCESSOR_AVAILABLE:
            print("    ⚠️ 选择性处理器不可用，回退到图层感知模式")
            return self._process_layer_aware_fast_mode(entities)

        # 使用选择性处理器
        config = self.mode_configs[LineProcessingMode.SELECTIVE]
        processor = SelectiveLayerProcessor(
            distance_threshold=config['distance_threshold'],
            angle_threshold=config['angle_threshold']
        )

        processed_entities = processor.process_entities(entities)

        # 验证处理结果
        validation = processor.validate_processing_result(entities, processed_entities)

        return {
            'success': True,
            'entities': processed_entities,
            'mode_info': f'选择性处理: 墙体迭代合并+其他去重',
            'processing_stats': processor.get_processing_statistics(),
            'validation': validation
        }

    def _process_independent_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """独立图层处理模式：每个图层完全独立处理"""
        print("  🎯 独立图层处理模式")

        if not INDEPENDENT_PROCESSOR_AVAILABLE:
            print("    ⚠️ 独立图层处理器不可用，回退到选择性处理模式")
            return self._process_selective_mode(entities)

        # 使用独立图层处理器
        config = self.mode_configs[LineProcessingMode.INDEPENDENT]
        processor = IndependentLayerProcessor(
            distance_threshold=config['distance_threshold'],
            angle_threshold=config['angle_threshold']
        )

        processed_entities = processor.process_entities(entities)

        # 获取详细的处理报告
        processing_report = processor.get_layer_processing_report()

        # 验证处理结果
        validation = processor.validate_processing_result(entities, processed_entities)

        return {
            'success': True,
            'entities': processed_entities,
            'mode_info': f'独立图层处理: {processing_report["summary"]["total_layers"]}个图层独立处理',
            'processing_stats': processor.get_processing_statistics(),
            'processing_report': processing_report,
            'validation': validation
        }

    def _process_traditional_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """传统处理模式"""
        print("  📊 传统线条处理模式")
        return self._process_fast_mode(entities)  # 简化实现
    
    def _process_custom_mode(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """自定义处理模式"""
        print("  🔧 自定义线条处理模式")
        
        # 使用自定义配置
        if self.custom_config.get('use_layer_aware', True) and LAYER_AWARE_AVAILABLE:
            merger = LayerAwareLineMerger(
                distance_threshold=self.custom_config.get('distance_threshold', 5),
                angle_threshold=self.custom_config.get('angle_threshold', 2)
            )
            merged_entities = merger.merge_entities_by_layer(entities)
            return {
                'success': True,
                'entities': merged_entities,
                'mode_info': '自定义图层感知处理',
                'merge_stats': merger.get_merge_statistics()
            }
        else:
            return self._process_fast_mode(entities)
    
    def _is_line_entity(self, entity: Dict[str, Any]) -> bool:
        """判断是否为线条实体"""
        entity_type = entity.get('type', '').upper()
        return entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']
    
    def _extract_line_coordinates(self, entity: Dict[str, Any]) -> Optional[List[List[float]]]:
        """提取线条坐标"""
        if 'points' in entity:
            return entity['points']
        elif 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        return None
    
    def _rebuild_entities_from_coords(self, coords_list, template_entity):
        """从坐标重建实体"""
        entities = []
        for coords in coords_list:
            # 处理不同类型的坐标对象
            if hasattr(coords, 'coords'):
                coord_points = list(coords.coords)
            else:
                coord_points = coords
            
            entity = {
                'type': 'LINE',
                'layer': template_entity.get('layer', 'UNKNOWN'),
                'points': coord_points,
                'start_point': coord_points[0] if len(coord_points) >= 2 else None,
                'end_point': coord_points[-1] if len(coord_points) >= 2 else None,
                'color': template_entity.get('color', 1),
                'merged_by': 'FastMode'
            }
            entities.append(entity)
        
        return entities
    
    def get_mode_info(self) -> Dict[str, Any]:
        """获取当前模式信息"""
        return {
            'current_mode': self.current_mode.value,
            'mode_config': self.mode_configs[self.current_mode],
            'custom_config': self.custom_config,
            'available_modes': self.get_available_modes()
        }


# 全局实例
_global_mode_manager = None


def get_mode_manager() -> LineProcessingModeManager:
    """获取全局模式管理器实例"""
    global _global_mode_manager
    if _global_mode_manager is None:
        _global_mode_manager = LineProcessingModeManager()
    return _global_mode_manager

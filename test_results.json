{"test_time": 1753712378.4131958, "total_duration": 0.11892986297607422, "enhanced_result": {"success": true, "file_path": "test_file.dxf", "entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955}, {"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 5.0, "angle_threshold": 2.0, "processing_timestamp": 1753712378.3751795, "processing_stage": "processed"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 5.0, "angle_threshold": 2.0, "processing_timestamp": 1753712378.3751795, "processing_stage": "processed"}, {"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4011955, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4021933}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4021933, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 5.0, "angle_threshold": 2.0, "processing_timestamp": 1753712378.3762097, "processing_stage": "processed"}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4021933, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 10.0, "angle_threshold": 5.0, "processing_timestamp": 1753712378.377179, "processing_stage": "processed"}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 10.0, "angle_threshold": 5.0, "processing_timestamp": 1753712378.377179, "processing_stage": "processed"}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 10.0, "angle_threshold": 5.0, "processing_timestamp": 1753712378.3782063, "processing_stage": "processed"}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.4031909, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3931694, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4041636}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4041636, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 8.0, "angle_threshold": 3.0, "processing_timestamp": 1753712378.3791778, "processing_stage": "processed"}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.4041636, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.394169, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 8.0, "angle_threshold": 3.0, "processing_timestamp": 1753712378.380205, "processing_stage": "processed"}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.3951674, "processing_stage": "processed", "group_index": 0}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "text", "processing_timestamp": 1753712378.3811772, "processing_stage": "processed"}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.4051626, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3961952, "processing_stage": "processed", "group_index": 0}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "text", "processing_timestamp": 1753712378.3811772, "processing_stage": "processed"}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "dimension", "processing_timestamp": 1753712378.3822024, "processing_stage": "processed"}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.406205, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4071946}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4071946, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "dimension", "processing_timestamp": 1753712378.3822024, "processing_stage": "processed"}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.4071946, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3981671, "processing_stage": "processed", "group_index": 0}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607, "line_processed": true, "processing_type": "basic_process", "layer_type_confirmed": "other", "processing_timestamp": 1753712378.383175, "processing_stage": "processed"}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.399166, "processing_stage": "processed", "group_index": 0}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607, "line_processed": true, "processing_type": "basic_process", "layer_type_confirmed": "other", "processing_timestamp": 1753712378.383175, "processing_stage": "processed"}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.4081607, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.4001925, "processing_stage": "processed", "group_index": 0}], "groups": [[{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0}], [{"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}], [{"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0}], [{"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3931694, "processing_stage": "processed", "group_index": 0}], [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.394169, "processing_stage": "processed", "group_index": 0}], [{"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.3951674, "processing_stage": "processed", "group_index": 0}], [{"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3961952, "processing_stage": "processed", "group_index": 0}], [{"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0}], [{"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0}], [{"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3981671, "processing_stage": "processed", "group_index": 0}], [{"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.399166, "processing_stage": "processed", "group_index": 0}], [{"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.4001925, "processing_stage": "processed", "group_index": 0}]], "labeled_entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3931694, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.394169, "processing_stage": "processed", "group_index": 0, "label": "railing", "auto_labeled": true, "confidence": 0.8, "labeled_by": "RailingLayerProcessor"}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.3951674, "processing_stage": "processed", "group_index": 0, "label": "railing", "auto_labeled": true, "confidence": 0.8, "labeled_by": "RailingLayerProcessor"}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3961952, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3981671, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.399166, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.4001925, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}], "auto_labeled_entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3842025, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3911707, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "container_layer_type": "wall", "container_layer_name": "WALL-INNER", "original_layer": "WALL-INNER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753712378.3852031, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3921697, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "container_layer_type": "door_window", "container_layer_name": "A-WIND", "original_layer": "A-WIND", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753712378.3931694, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "A-RAIL", "original_layer": "A-RAIL", "type_information_verified": true, "type_verification_timestamp": 1753712378.3862143, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.394169, "processing_stage": "processed", "group_index": 0, "label": "railing", "auto_labeled": true, "confidence": 0.8, "labeled_by": "RailingLayerProcessor"}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "container_layer_type": "railing", "container_layer_name": "RAILING", "original_layer": "RAILING", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "RailingLayerProcessor", "entity_category": "railing", "processing_timestamp": 1753712378.3951674, "processing_stage": "processed", "group_index": 0, "label": "railing", "auto_labeled": true, "confidence": 0.8, "labeled_by": "RailingLayerProcessor"}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753712378.3871996, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3961952, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "container_layer_type": "text", "container_layer_name": "TEXT-LAYER", "original_layer": "TEXT-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3972077, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "DIM-LAYER", "original_layer": "DIM-LAYER", "type_information_verified": true, "type_verification_timestamp": 1753712378.3881984, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753712378.3981671, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "container_layer_type": "other", "container_layer_name": "FURNITURE", "original_layer": "FURNITURE", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.399166, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "container_layer_type": "other", "container_layer_name": "MISC", "original_layer": "MISC", "type_information_verified": true, "type_verification_timestamp": 1753712378.3891983, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753712378.4001925, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}], "processing_mode": "enhanced", "total_entities": 42, "total_groups": 12, "total_labeled": 14, "enhanced_data": {"containers": ["LayerDataContainer(name='A-WALL', type='wall', entities=6, groups=1, stage='labeled')", "LayerDataContainer(name='WALL-INNER', type='wall', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-DOOR', type='door_window', entities=6, groups=1, stage='labeled')", "LayerDataContainer(name='A-WIND', type='door_window', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-RAIL', type='railing', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='RAILING', type='railing', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-ANNO-TEXT', type='text', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='TEXT-LAYER', type='text', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-DIMS', type='dimension', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='DIM-LAYER', type='dimension', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='FURNITURE', type='other', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='MISC', type='other', entities=3, groups=1, stage='labeled')"], "layer_summary": {"A-WALL": {"layer_type": "wall", "entity_count": 6, "group_count": 1, "labeled_count": 2, "processing_summary": {"layer_info": {"name": "A-WALL", "type": "wall", "creation_time": 1753712378.4011955}, "data_counts": {"original_entities": 2, "processed_entities": 4, "groups": 1, "labeled_entities": 2}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 2, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3762097, "input_count": 2, "output_count": 4}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3911707, "input_count": 2, "output_count": 6}, {"operation": "set_groups", "processor": "WallLayerProcessor", "timestamp": 1753712378.3911707, "input_count": 6, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "WallLayerProcessor", "timestamp": 1753712378.3911707, "input_count": 6, "output_count": 2}]}}, "WALL-INNER": {"layer_type": "wall", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "WALL-INNER", "type": "wall", "creation_time": 1753712378.4021933}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3762097, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3921697, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "WallLayerProcessor", "timestamp": 1753712378.3921697, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "WallLayerProcessor", "timestamp": 1753712378.3921697, "input_count": 3, "output_count": 1}]}}, "A-DOOR": {"layer_type": "door_window", "entity_count": 6, "group_count": 1, "labeled_count": 2, "processing_summary": {"layer_info": {"name": "A-DOOR", "type": "door_window", "creation_time": 1753712378.4021933}, "data_counts": {"original_entities": 2, "processed_entities": 4, "groups": 1, "labeled_entities": 2}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 2, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.377179, "input_count": 2, "output_count": 4}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3921697, "input_count": 2, "output_count": 6}, {"operation": "set_groups", "processor": "DoorWindowLayerProcessor", "timestamp": 1753712378.3921697, "input_count": 6, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DoorWindowLayerProcessor", "timestamp": 1753712378.3921697, "input_count": 6, "output_count": 2}]}}, "A-WIND": {"layer_type": "door_window", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-WIND", "type": "door_window", "creation_time": 1753712378.4031909}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3782063, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3931694, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "DoorWindowLayerProcessor", "timestamp": 1753712378.3931694, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DoorWindowLayerProcessor", "timestamp": 1753712378.3931694, "input_count": 3, "output_count": 1}]}}, "A-RAIL": {"layer_type": "railing", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-RAIL", "type": "railing", "creation_time": 1753712378.4041636}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3791778, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.394169, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "RailingLayerProcessor", "timestamp": 1753712378.394169, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "RailingLayerProcessor", "timestamp": 1753712378.394169, "input_count": 3, "output_count": 1}]}}, "RAILING": {"layer_type": "railing", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "RAILING", "type": "railing", "creation_time": 1753712378.4041636}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3682103, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.380205, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3951674, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "RailingLayerProcessor", "timestamp": 1753712378.3951674, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "RailingLayerProcessor", "timestamp": 1753712378.3951674, "input_count": 3, "output_count": 1}]}}, "A-ANNO-TEXT": {"layer_type": "text", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-ANNO-TEXT", "type": "text", "creation_time": 1753712378.4051626}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3811772, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3961952, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "TextLayerProcessor", "timestamp": 1753712378.3961952, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "TextLayerProcessor", "timestamp": 1753712378.3961952, "input_count": 3, "output_count": 1}]}}, "TEXT-LAYER": {"layer_type": "text", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "TEXT-LAYER", "type": "text", "creation_time": 1753712378.406205}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3811772, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3972077, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "TextLayerProcessor", "timestamp": 1753712378.3972077, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "TextLayerProcessor", "timestamp": 1753712378.3972077, "input_count": 3, "output_count": 1}]}}, "A-DIMS": {"layer_type": "dimension", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-DIMS", "type": "dimension", "creation_time": 1753712378.406205}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3822024, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3972077, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "DimensionLayerProcessor", "timestamp": 1753712378.3972077, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DimensionLayerProcessor", "timestamp": 1753712378.3972077, "input_count": 3, "output_count": 1}]}}, "DIM-LAYER": {"layer_type": "dimension", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "DIM-LAYER", "type": "dimension", "creation_time": 1753712378.4071946}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3822024, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.3981671, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "DimensionLayerProcessor", "timestamp": 1753712378.3981671, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DimensionLayerProcessor", "timestamp": 1753712378.3981671, "input_count": 3, "output_count": 1}]}}, "FURNITURE": {"layer_type": "other", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "FURNITURE", "type": "other", "creation_time": 1753712378.4071946}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.383175, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.399166, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "OtherLayerProcessor", "timestamp": 1753712378.399166, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "OtherLayerProcessor", "timestamp": 1753712378.399166, "input_count": 3, "output_count": 1}]}}, "MISC": {"layer_type": "other", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "MISC", "type": "other", "creation_time": 1753712378.4081607}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753712378.3692114, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.383175, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753712378.4001925, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "OtherLayerProcessor", "timestamp": 1753712378.4001925, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "OtherLayerProcessor", "timestamp": 1753712378.4001925, "input_count": 3, "output_count": 1}]}}}, "integrity_check": {"is_valid": true, "errors": [], "warnings": [], "statistics": {"total_entities": 42, "total_groups": 12, "total_labeled": 14, "group_entity_count": 14, "entities_without_type": 0, "entities_without_layer": 0}}, "processing_summary": {"pipeline_summary": {"pipeline_stats": {"total_containers_processed": 12, "total_entities_processed": 0, "total_processing_time": 0.010021209716796875, "processor_stats": {"WallLayerProcessor": {"processed_containers": 3, "total_entities": 15, "processing_time": 0.002997875213623047}, "DoorWindowLayerProcessor": {"processed_containers": 3, "total_entities": 15, "processing_time": 0.0}, "RailingLayerProcessor": {"processed_containers": 3, "total_entities": 9, "processing_time": 0.0}, "TextLayerProcessor": {"processed_containers": 3, "total_entities": 9, "processing_time": 0.003068208694458008}, "DimensionLayerProcessor": {"processed_containers": 3, "total_entities": 9, "processing_time": 0.0}, "OtherLayerProcessor": {"processed_containers": 3, "total_entities": 9, "processing_time": 0.0}}}, "available_processors": ["LayerType.WALL", "LayerType.DOOR_WINDOW", "LayerType.RAILING", "LayerType.TEXT", "LayerType.DIMENSION", "LayerType.OTHER"], "processor_details": {"wall": {"processed_containers": 2, "total_entities": 9, "processing_time": 0.001998424530029297}, "door_window": {"processed_containers": 2, "total_entities": 9, "processing_time": 0.0}, "railing": {"processed_containers": 2, "total_entities": 6, "processing_time": 0.0}, "text": {"processed_containers": 2, "total_entities": 6, "processing_time": 0.002040386199951172}, "dimension": {"processed_containers": 2, "total_entities": 6, "processing_time": 0.0}, "other": {"processed_containers": 2, "total_entities": 6, "processing_time": 0.0}}}, "line_processor_summary": {"processing_stats": {"total_entities_processed": 14, "layers_processed": 12, "processing_time": 0.007995367050170898, "layer_stats": {"A-WALL": {"entities_processed": 2, "processing_time": 0.0010302066802978516}, "WALL-INNER": {"entities_processed": 1, "processing_time": 0.0}, "A-DOOR": {"entities_processed": 2, "processing_time": 0.0}, "A-WIND": {"entities_processed": 1, "processing_time": 0.0}, "A-RAIL": {"entities_processed": 1, "processing_time": 0.0}, "RAILING": {"entities_processed": 1, "processing_time": 0.0010271072387695312}, "A-ANNO-TEXT": {"entities_processed": 1, "processing_time": 0.0009722709655761719}, "TEXT-LAYER": {"entities_processed": 1, "processing_time": 0.0}, "A-DIMS": {"entities_processed": 1, "processing_time": 0.0010251998901367188}, "DIM-LAYER": {"entities_processed": 1, "processing_time": 0.0}, "FURNITURE": {"entities_processed": 1, "processing_time": 0.0}, "MISC": {"entities_processed": 1, "processing_time": 0.0}}}, "layer_configs": {"wall": {"merge_threshold": 5.0, "angle_threshold": 2.0, "enable_merging": true, "connection_precision": "high"}, "door_window": {"merge_threshold": 10.0, "angle_threshold": 5.0, "enable_merging": true, "connection_precision": "medium"}, "railing": {"merge_threshold": 8.0, "angle_threshold": 3.0, "enable_merging": true, "connection_precision": "medium"}, "text": {"enable_merging": false, "preserve_original": true}, "dimension": {"enable_merging": false, "preserve_original": true}, "other": {"merge_threshold": 15.0, "angle_threshold": 10.0, "enable_merging": false, "connection_precision": "low"}}}, "transfer_summary": {"transfer_stats": {"total_transfers": 36, "successful_transfers": 12, "failed_transfers": 24, "data_integrity_errors": 0, "type_information_losses": 0}, "total_records": 36, "registered_containers": 36, "recent_transfers": [{"timestamp": 1753712378.4021933, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "50a1e1ff38f0", "validation_passed": true, "entity_count": 6}, {"timestamp": 1753712378.4031909, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "fcb602d15ec5", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4041636, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "a6bbf02cf95e", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4041636, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "1e6e4b3c9d1b", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4051626, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "5c96fc493cee", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.406205, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "f644ad7fd63f", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.406205, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "5be09ae5c394", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4071946, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "ca2e94529727", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4071946, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "a79c4a2c577a", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753712378.4081607, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "98f64776a58a", "validation_passed": true, "entity_count": 3}]}, "manager_stats": {"total_files_processed": 1, "total_entities_processed": 14, "total_containers_created": 12, "total_processing_time": 0.042946577072143555, "stage_times": {"container_creation": 0.0059702396392822266, "line_processing": 0.014018774032592773, "entity_grouping": 0.019021987915039062, "integration_validation": 0.0009665489196777344}}}}}, "legacy_result": {"success": true, "file_path": "test_file.dxf", "entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], "groups": [[{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "legacy_processed": true, "processing_timestamp": 1753712378.411187}], [{"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "legacy_processed": true, "processing_timestamp": 1753712378.411187}]], "labeled_entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "door_window", "auto_labeled": true, "confidence": 0.7}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "door_window", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "text", "auto_labeled": true, "confidence": 0.7}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "text", "auto_labeled": true, "confidence": 0.7}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}], "auto_labeled_entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "WALL-INNER", "start_point": [0, 100], "end_point": [100, 100], "color": 1, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "wall", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-DOOR", "start_point": [50, 0], "end_point": [70, 0], "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "door_window", "auto_labeled": true, "confidence": 0.7}, {"type": "ARC", "layer": "A-DOOR", "center": [60, 0], "radius": 10, "color": 2, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "door_window", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-WIND", "start_point": [100, 30], "end_point": [100, 50], "color": 3, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "A-RAIL", "start_point": [0, 200], "end_point": [50, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINE", "layer": "RAILING", "start_point": [50, 200], "end_point": [100, 200], "color": 4, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "text", "auto_labeled": true, "confidence": 0.7}, {"type": "MTEXT", "layer": "TEXT-LAYER", "text": "说明文字", "position": [150, 50], "height": 3, "color": 5, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "text", "auto_labeled": true, "confidence": 0.7}, {"type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "LINEAR_DIMENSION", "layer": "DIM-LAYER", "def_points": [[100, 0], [100, 100], [120, 50]], "text": "100", "color": 6, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "CIRCLE", "layer": "FURNITURE", "center": [200, 200], "radius": 20, "color": 7, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}, {"type": "POLYLINE", "layer": "MISC", "points": [[300, 300], [350, 300], [350, 350], [300, 350]], "closed": true, "color": 8, "legacy_processed": true, "processing_timestamp": 1753712378.411187, "label": "other", "auto_labeled": true, "confidence": 0.7}], "processing_mode": "legacy", "total_entities": 14, "total_groups": 12, "total_labeled": 14}, "test_entities_count": 14}
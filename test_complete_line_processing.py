#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的线条处理流程
从开始处理到线条处理完的整个过程，找出真正的性能瓶颈
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from independent_layer_processor import IndependentLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    print("✅ 处理模块导入成功")
except ImportError as e:
    print(f"❌ 处理模块导入失败: {e}")
    sys.exit(1)


def create_realistic_test_data():
    """创建真实的测试数据（模拟CAD文件）"""
    entities = []
    
    print("📊 创建真实测试数据...")
    
    # A-WALL图层（208个实体，模拟墙体）
    for i in range(208):
        entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5.0, 0.0],
            'end_point': [i * 5.0 + 3.0, 0.0],
            'color': 1,
            'points': [[i * 5.0, 0.0], [i * 5.0 + 3.0, 0.0]],
            'linetype': 'CONTINUOUS',
            'lineweight': 0.25
        })
    
    # A-WINDOW图层（154个实体，包含重复）
    for i in range(154):
        if i % 10 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'window_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [base_index * 8.0, 10.0],
                'end_point': [base_index * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[base_index * 8.0, 10.0], [base_index * 8.0 + 4.0, 10.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.18
            })
        else:
            entities.append({
                'id': f'window_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 8.0, 10.0],
                'end_point': [i * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[i * 8.0, 10.0], [i * 8.0 + 4.0, 10.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.18
            })
    
    # 0图层（122个实体，包含重复）
    for i in range(122):
        if i % 8 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'layer0_{i}_duplicate',
                'type': 'LINE',
                'layer': '0',
                'start_point': [base_index * 6.0, 20.0],
                'end_point': [base_index * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[base_index * 6.0, 20.0], [base_index * 6.0 + 2.0, 20.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.13
            })
        else:
            entities.append({
                'id': f'layer0_{i}',
                'type': 'LINE',
                'layer': '0',
                'start_point': [i * 6.0, 20.0],
                'end_point': [i * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[i * 6.0, 20.0], [i * 6.0 + 2.0, 20.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.13
            })
    
    print(f"   创建完成: {len(entities)} 个实体")
    return entities


def test_step_by_step_processing():
    """逐步测试处理流程"""
    print("🧪 逐步测试线条处理流程")
    print("=" * 80)
    
    # 创建测试数据
    print("📊 步骤1: 创建测试数据")
    step1_start = time.time()
    test_entities = create_realistic_test_data()
    step1_time = time.time() - step1_start
    print(f"   ✅ 数据创建完成: {step1_time:.3f} 秒")
    
    # 按图层统计
    layer_stats = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in layer_stats:
            layer_stats[layer] = 0
        layer_stats[layer] += 1
    
    print("📋 图层分布:")
    for layer, count in layer_stats.items():
        print(f"   {layer}: {count} 个实体")
    
    # 步骤2: 创建处理器
    print(f"\n📊 步骤2: 创建独立图层处理器")
    step2_start = time.time()
    processor = IndependentLayerProcessor()
    step2_time = time.time() - step2_start
    print(f"   ✅ 处理器创建完成: {step2_time:.3f} 秒")
    
    # 步骤3: 数据预处理
    print(f"\n📊 步骤3: 数据预处理")
    step3_start = time.time()
    # 模拟数据复制和预处理
    import copy
    processed_entities = copy.deepcopy(test_entities)
    step3_time = time.time() - step3_start
    print(f"   ✅ 数据预处理完成: {step3_time:.3f} 秒")
    
    # 步骤4: 核心处理
    print(f"\n📊 步骤4: 核心线条处理")
    step4_start = time.time()
    result = processor.process_entities(test_entities)
    step4_time = time.time() - step4_start
    print(f"   ✅ 核心处理完成: {step4_time:.3f} 秒")
    
    # 步骤5: 结果处理
    print(f"\n📊 步骤5: 结果处理")
    step5_start = time.time()
    # 模拟结果处理和验证
    final_result = copy.deepcopy(result)
    for entity in final_result:
        entity['final_processed'] = True
        entity['final_timestamp'] = time.time()
    step5_time = time.time() - step5_start
    print(f"   ✅ 结果处理完成: {step5_time:.3f} 秒")
    
    # 步骤6: 数据输出准备
    print(f"\n📊 步骤6: 数据输出准备")
    step6_start = time.time()
    # 模拟数据序列化和输出准备
    output_data = {
        'entities': final_result,
        'statistics': {
            'input_count': len(test_entities),
            'output_count': len(final_result),
            'reduction_count': len(test_entities) - len(final_result)
        },
        'processing_time': step4_time
    }
    json_output = json.dumps(output_data, default=str)
    step6_time = time.time() - step6_start
    print(f"   ✅ 输出准备完成: {step6_time:.3f} 秒")
    
    # 总结
    total_time = step1_time + step2_time + step3_time + step4_time + step5_time + step6_time
    
    print(f"\n📈 处理流程时间分解:")
    print(f"   步骤1 - 数据创建: {step1_time:.3f} 秒 ({step1_time/total_time*100:.1f}%)")
    print(f"   步骤2 - 处理器创建: {step2_time:.3f} 秒 ({step2_time/total_time*100:.1f}%)")
    print(f"   步骤3 - 数据预处理: {step3_time:.3f} 秒 ({step3_time/total_time*100:.1f}%)")
    print(f"   步骤4 - 核心处理: {step4_time:.3f} 秒 ({step4_time/total_time*100:.1f}%)")
    print(f"   步骤5 - 结果处理: {step5_time:.3f} 秒 ({step5_time/total_time*100:.1f}%)")
    print(f"   步骤6 - 输出准备: {step6_time:.3f} 秒 ({step6_time/total_time*100:.1f}%)")
    print(f"   总时间: {total_time:.3f} 秒")
    
    # 识别瓶颈
    steps = [
        ('数据创建', step1_time),
        ('处理器创建', step2_time),
        ('数据预处理', step3_time),
        ('核心处理', step4_time),
        ('结果处理', step5_time),
        ('输出准备', step6_time)
    ]
    
    # 找出最慢的步骤
    slowest_step = max(steps, key=lambda x: x[1])
    print(f"\n🚨 性能瓶颈识别:")
    print(f"   最慢步骤: {slowest_step[0]} ({slowest_step[1]:.3f} 秒)")
    
    if slowest_step[1] > 0.1:
        print(f"   ⚠️ 发现性能瓶颈: {slowest_step[0]}")
    else:
        print(f"   ✅ 未发现明显瓶颈")
    
    return {
        'steps': dict(steps),
        'total_time': total_time,
        'bottleneck': slowest_step,
        'input_entities': len(test_entities),
        'output_entities': len(final_result)
    }


def test_mode_manager_processing():
    """测试模式管理器处理流程"""
    print("\n🧪 测试模式管理器处理流程")
    print("=" * 80)
    
    # 创建测试数据
    test_entities = create_realistic_test_data()
    
    # 步骤1: 创建模式管理器
    print("📊 步骤1: 创建模式管理器")
    step1_start = time.time()
    mode_manager = LineProcessingModeManager()
    step1_time = time.time() - step1_start
    print(f"   ✅ 模式管理器创建完成: {step1_time:.3f} 秒")
    
    # 步骤2: 设置模式
    print("📊 步骤2: 设置独立图层处理模式")
    step2_start = time.time()
    mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    step2_time = time.time() - step2_start
    print(f"   ✅ 模式设置完成: {step2_time:.3f} 秒")
    
    # 步骤3: 执行处理
    print("📊 步骤3: 执行模式处理")
    step3_start = time.time()
    result = mode_manager.process_entities(test_entities)
    step3_time = time.time() - step3_start
    print(f"   ✅ 模式处理完成: {step3_time:.3f} 秒")
    
    # 步骤4: 结果验证
    print("📊 步骤4: 结果验证")
    step4_start = time.time()
    success = result.get('success', False)
    entities = result.get('entities', [])
    step4_time = time.time() - step4_start
    print(f"   ✅ 结果验证完成: {step4_time:.3f} 秒")
    
    total_time = step1_time + step2_time + step3_time + step4_time
    
    print(f"\n📈 模式管理器处理时间分解:")
    print(f"   步骤1 - 管理器创建: {step1_time:.3f} 秒")
    print(f"   步骤2 - 模式设置: {step2_time:.3f} 秒")
    print(f"   步骤3 - 模式处理: {step3_time:.3f} 秒")
    print(f"   步骤4 - 结果验证: {step4_time:.3f} 秒")
    print(f"   总时间: {total_time:.3f} 秒")
    print(f"   处理成功: {success}")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(entities)}")
    
    return {
        'total_time': total_time,
        'success': success,
        'input_entities': len(test_entities),
        'output_entities': len(entities)
    }


def test_memory_and_io_operations():
    """测试内存和IO操作"""
    print("\n🧪 测试内存和IO操作")
    print("=" * 80)
    
    test_entities = create_realistic_test_data()
    
    # 测试深拷贝
    print("📊 测试深拷贝操作")
    import copy
    deepcopy_start = time.time()
    copied_entities = copy.deepcopy(test_entities)
    deepcopy_time = time.time() - deepcopy_start
    print(f"   深拷贝时间: {deepcopy_time:.3f} 秒")
    
    # 测试JSON序列化
    print("📊 测试JSON序列化")
    json_start = time.time()
    json_data = json.dumps(test_entities, default=str)
    json_time = time.time() - json_start
    print(f"   JSON序列化时间: {json_time:.3f} 秒")
    print(f"   JSON数据大小: {len(json_data)} 字符")
    
    # 测试JSON反序列化
    print("📊 测试JSON反序列化")
    parse_start = time.time()
    parsed_entities = json.loads(json_data)
    parse_time = time.time() - parse_start
    print(f"   JSON反序列化时间: {parse_time:.3f} 秒")
    
    # 测试文件IO
    print("📊 测试文件IO操作")
    file_write_start = time.time()
    with open('test_entities.json', 'w', encoding='utf-8') as f:
        json.dump(test_entities, f, default=str, indent=2)
    file_write_time = time.time() - file_write_start
    print(f"   文件写入时间: {file_write_time:.3f} 秒")
    
    file_read_start = time.time()
    with open('test_entities.json', 'r', encoding='utf-8') as f:
        loaded_entities = json.load(f)
    file_read_time = time.time() - file_read_start
    print(f"   文件读取时间: {file_read_time:.3f} 秒")
    
    # 清理测试文件
    try:
        os.remove('test_entities.json')
    except:
        pass
    
    return {
        'deepcopy_time': deepcopy_time,
        'json_serialize_time': json_time,
        'json_parse_time': parse_time,
        'file_write_time': file_write_time,
        'file_read_time': file_read_time
    }


def run_complete_line_processing_test():
    """运行完整的线条处理测试"""
    print("🚀 开始完整线条处理流程测试")
    print("=" * 100)
    
    overall_start = time.time()
    
    try:
        # 1. 逐步测试处理流程
        step_results = test_step_by_step_processing()
        
        # 2. 测试模式管理器
        mode_results = test_mode_manager_processing()
        
        # 3. 测试内存和IO操作
        io_results = test_memory_and_io_operations()
        
        overall_time = time.time() - overall_start
        
        print(f"\n🎉 完整线条处理流程测试完成")
        print("=" * 100)
        print(f"   总测试时间: {overall_time:.2f} 秒")
        
        # 综合分析
        print(f"\n📊 综合性能分析:")
        print(f"   逐步处理总时间: {step_results['total_time']:.3f} 秒")
        print(f"   模式管理器总时间: {mode_results['total_time']:.3f} 秒")
        print(f"   最大瓶颈: {step_results['bottleneck'][0]} ({step_results['bottleneck'][1]:.3f} 秒)")
        
        # IO操作分析
        print(f"\n📊 IO操作分析:")
        print(f"   深拷贝时间: {io_results['deepcopy_time']:.3f} 秒")
        print(f"   JSON序列化: {io_results['json_serialize_time']:.3f} 秒")
        print(f"   JSON反序列化: {io_results['json_parse_time']:.3f} 秒")
        print(f"   文件写入: {io_results['file_write_time']:.3f} 秒")
        print(f"   文件读取: {io_results['file_read_time']:.3f} 秒")
        
        # 识别主要瓶颈
        all_operations = [
            ('逐步处理', step_results['total_time']),
            ('模式管理器', mode_results['total_time']),
            ('深拷贝', io_results['deepcopy_time']),
            ('JSON序列化', io_results['json_serialize_time']),
            ('JSON反序列化', io_results['json_parse_time']),
            ('文件写入', io_results['file_write_time']),
            ('文件读取', io_results['file_read_time'])
        ]
        
        main_bottleneck = max(all_operations, key=lambda x: x[1])
        
        print(f"\n🚨 主要性能瓶颈:")
        print(f"   最慢操作: {main_bottleneck[0]} ({main_bottleneck[1]:.3f} 秒)")
        
        if main_bottleneck[1] > 1.0:
            print(f"   ❌ 发现严重性能问题: {main_bottleneck[0]}")
        elif main_bottleneck[1] > 0.1:
            print(f"   ⚠️ 发现性能瓶颈: {main_bottleneck[0]}")
        else:
            print(f"   ✅ 未发现明显性能问题")
        
        # 保存测试结果
        test_results = {
            'overall_time': overall_time,
            'step_results': step_results,
            'mode_results': mode_results,
            'io_results': io_results,
            'main_bottleneck': main_bottleneck
        }
        
        with open('complete_line_processing_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: complete_line_processing_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_complete_line_processing_test()
    
    if success:
        print(f"\n🎊 完整流程测试成功！")
        print(f"   已识别出具体的性能瓶颈")
        print(f"   请查看测试结果进行针对性优化")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)

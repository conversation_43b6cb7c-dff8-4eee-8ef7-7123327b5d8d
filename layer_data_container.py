#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图层数据容器模块
用于封装和管理不同图层的数据及其类型信息，确保在处理流程中信息不丢失
"""

import copy
import time
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass, field


class LayerType(Enum):
    """图层类型枚举"""
    WALL = "wall"                    # 墙体
    DOOR_WINDOW = "door_window"      # 门窗
    RAILING = "railing"              # 栏杆
    TEXT = "text"                    # 文字
    DIMENSION = "dimension"          # 标注
    FURNITURE = "furniture"          # 家具
    COLUMN = "column"                # 柱子
    BEAM = "beam"                    # 梁
    STAIR = "stair"                  # 楼梯
    ELEVATOR = "elevator"            # 电梯
    OTHER = "other"                  # 其他


@dataclass
class ProcessingRecord:
    """处理记录"""
    timestamp: float
    operation: str
    processor: str
    input_count: int
    output_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)


class LayerDataContainer:
    """
    图层数据容器
    
    封装图层数据及其类型信息，确保在整个处理流程中：
    1. 图层类型信息不丢失
    2. 数据变更可追溯
    3. 处理结果可验证
    """
    
    def __init__(self, layer_name: str, layer_type: LayerType, 
                 original_entities: List[Dict[str, Any]] = None):
        """
        初始化图层数据容器
        
        Args:
            layer_name: 图层名称
            layer_type: 图层类型
            original_entities: 原始实体列表
        """
        self.layer_name = layer_name
        self.layer_type = layer_type
        self.creation_time = time.time()
        
        # 数据存储
        self.original_entities = original_entities or []
        self.processed_entities = []
        self.groups = []
        self.labeled_entities = []
        
        # 元数据
        self.metadata = {
            'layer_name': layer_name,
            'layer_type': layer_type.value,
            'creation_time': self.creation_time,
            'entity_count': len(self.original_entities),
            'processing_stage': 'initialized'
        }
        
        # 处理历史
        self.processing_history: List[ProcessingRecord] = []
        
        # 状态标记
        self.is_processed = False
        self.is_grouped = False
        self.is_labeled = False
        
        # 记录初始化
        self._add_processing_record(
            operation="initialize",
            processor="LayerDataContainer",
            input_count=len(self.original_entities),
            output_count=len(self.original_entities)
        )
    
    def _add_processing_record(self, operation: str, processor: str, 
                             input_count: int, output_count: int, 
                             metadata: Dict[str, Any] = None):
        """添加处理记录"""
        record = ProcessingRecord(
            timestamp=time.time(),
            operation=operation,
            processor=processor,
            input_count=input_count,
            output_count=output_count,
            metadata=metadata or {}
        )
        self.processing_history.append(record)
    
    def add_entities(self, entities: List[Dict[str, Any]], stage: str = "unknown"):
        """
        添加实体到容器
        
        Args:
            entities: 实体列表
            stage: 处理阶段标识
        """
        if not entities:
            return
        
        # 确保实体包含图层类型信息
        for entity in entities:
            if isinstance(entity, dict):
                entity['container_layer_type'] = self.layer_type.value
                entity['container_layer_name'] = self.layer_name
                entity['processing_stage'] = stage
        
        if stage == "original":
            self.original_entities.extend(entities)
        elif stage == "processed":
            self.processed_entities.extend(entities)
        else:
            # 默认添加到处理后实体
            self.processed_entities.extend(entities)
        
        # 更新元数据
        self.metadata['entity_count'] = len(self.original_entities)
        self.metadata['processed_count'] = len(self.processed_entities)
        
        # 记录操作
        self._add_processing_record(
            operation=f"add_entities_{stage}",
            processor="LayerDataContainer",
            input_count=len(entities),
            output_count=len(self.get_all_entities())
        )
    
    def set_groups(self, groups: List[List[Dict[str, Any]]], processor_name: str = "unknown"):
        """
        设置分组结果
        
        Args:
            groups: 分组列表
            processor_name: 处理器名称
        """
        self.groups = groups
        self.is_grouped = True
        
        # 为每个组添加容器信息
        for i, group in enumerate(self.groups):
            for entity in group:
                if isinstance(entity, dict):
                    entity['container_layer_type'] = self.layer_type.value
                    entity['container_layer_name'] = self.layer_name
                    entity['group_index'] = i
        
        # 更新元数据
        self.metadata['group_count'] = len(groups)
        self.metadata['processing_stage'] = 'grouped'
        
        # 记录操作
        self._add_processing_record(
            operation="set_groups",
            processor=processor_name,
            input_count=len(self.get_all_entities()),
            output_count=len(groups),
            metadata={'group_count': len(groups)}
        )
    
    def set_labeled_entities(self, labeled_entities: List[Dict[str, Any]], 
                           processor_name: str = "unknown"):
        """
        设置标注实体
        
        Args:
            labeled_entities: 已标注实体列表
            processor_name: 处理器名称
        """
        self.labeled_entities = labeled_entities
        self.is_labeled = True
        
        # 为标注实体添加容器信息
        for entity in self.labeled_entities:
            if isinstance(entity, dict):
                entity['container_layer_type'] = self.layer_type.value
                entity['container_layer_name'] = self.layer_name
        
        # 更新元数据
        self.metadata['labeled_count'] = len(labeled_entities)
        self.metadata['processing_stage'] = 'labeled'
        
        # 记录操作
        self._add_processing_record(
            operation="set_labeled_entities",
            processor=processor_name,
            input_count=len(self.get_all_entities()),
            output_count=len(labeled_entities),
            metadata={'labeled_count': len(labeled_entities)}
        )
    
    def get_all_entities(self) -> List[Dict[str, Any]]:
        """获取所有实体（原始+处理后）"""
        all_entities = []
        all_entities.extend(self.original_entities)
        all_entities.extend(self.processed_entities)
        return all_entities
    
    def get_entities_by_stage(self, stage: str) -> List[Dict[str, Any]]:
        """
        根据处理阶段获取实体
        
        Args:
            stage: 处理阶段 ('original', 'processed', 'grouped', 'labeled')
        """
        if stage == "original":
            return self.original_entities
        elif stage == "processed":
            return self.processed_entities
        elif stage == "grouped":
            # 从分组中提取所有实体
            entities = []
            for group in self.groups:
                entities.extend(group)
            return entities
        elif stage == "labeled":
            return self.labeled_entities
        else:
            return self.get_all_entities()
    
    def create_deep_copy(self) -> 'LayerDataContainer':
        """创建深拷贝，用于安全的数据传递"""
        new_container = LayerDataContainer(
            layer_name=self.layer_name,
            layer_type=self.layer_type,
            original_entities=copy.deepcopy(self.original_entities)
        )
        
        # 复制所有数据
        new_container.processed_entities = copy.deepcopy(self.processed_entities)
        new_container.groups = copy.deepcopy(self.groups)
        new_container.labeled_entities = copy.deepcopy(self.labeled_entities)
        new_container.metadata = copy.deepcopy(self.metadata)
        new_container.processing_history = copy.deepcopy(self.processing_history)
        
        # 复制状态
        new_container.is_processed = self.is_processed
        new_container.is_grouped = self.is_grouped
        new_container.is_labeled = self.is_labeled
        
        return new_container

    def validate_data_integrity(self) -> Dict[str, Any]:
        """
        验证数据完整性

        Returns:
            验证结果字典
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }

        try:
            # 检查基本数据
            if not self.layer_name:
                validation_result['errors'].append("图层名称为空")
                validation_result['is_valid'] = False

            if not self.layer_type:
                validation_result['errors'].append("图层类型为空")
                validation_result['is_valid'] = False

            # 检查实体数据一致性
            original_count = len(self.original_entities)
            processed_count = len(self.processed_entities)

            # 检查实体是否包含必要的类型信息
            missing_type_info = 0
            for entity in self.get_all_entities():
                if isinstance(entity, dict):
                    if 'container_layer_type' not in entity:
                        missing_type_info += 1

            if missing_type_info > 0:
                validation_result['warnings'].append(
                    f"{missing_type_info} 个实体缺少容器类型信息"
                )

            # 统计信息
            validation_result['statistics'] = {
                'original_entities': original_count,
                'processed_entities': processed_count,
                'total_entities': original_count + processed_count,
                'groups': len(self.groups),
                'labeled_entities': len(self.labeled_entities),
                'processing_records': len(self.processing_history),
                'missing_type_info': missing_type_info
            }

        except Exception as e:
            validation_result['errors'].append(f"验证过程中发生错误: {str(e)}")
            validation_result['is_valid'] = False

        return validation_result

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要

        Returns:
            处理摘要字典
        """
        summary = {
            'layer_info': {
                'name': self.layer_name,
                'type': self.layer_type.value,
                'creation_time': self.creation_time
            },
            'data_counts': {
                'original_entities': len(self.original_entities),
                'processed_entities': len(self.processed_entities),
                'groups': len(self.groups),
                'labeled_entities': len(self.labeled_entities)
            },
            'processing_status': {
                'is_processed': self.is_processed,
                'is_grouped': self.is_grouped,
                'is_labeled': self.is_labeled,
                'current_stage': self.metadata.get('processing_stage', 'unknown')
            },
            'processing_history': [
                {
                    'operation': record.operation,
                    'processor': record.processor,
                    'timestamp': record.timestamp,
                    'input_count': record.input_count,
                    'output_count': record.output_count
                }
                for record in self.processing_history
            ]
        }

        return summary

    def merge_with_container(self, other_container: 'LayerDataContainer') -> 'LayerDataContainer':
        """
        与另一个容器合并（相同图层类型）

        Args:
            other_container: 另一个图层数据容器

        Returns:
            合并后的新容器
        """
        if self.layer_type != other_container.layer_type:
            raise ValueError(f"无法合并不同类型的图层容器: {self.layer_type} vs {other_container.layer_type}")

        # 创建新容器
        merged_name = f"{self.layer_name}+{other_container.layer_name}"
        merged_container = LayerDataContainer(
            layer_name=merged_name,
            layer_type=self.layer_type
        )

        # 合并数据
        merged_container.original_entities = (
            copy.deepcopy(self.original_entities) +
            copy.deepcopy(other_container.original_entities)
        )
        merged_container.processed_entities = (
            copy.deepcopy(self.processed_entities) +
            copy.deepcopy(other_container.processed_entities)
        )
        merged_container.groups = (
            copy.deepcopy(self.groups) +
            copy.deepcopy(other_container.groups)
        )
        merged_container.labeled_entities = (
            copy.deepcopy(self.labeled_entities) +
            copy.deepcopy(other_container.labeled_entities)
        )

        # 合并元数据
        merged_container.metadata.update({
            'merged_from': [self.layer_name, other_container.layer_name],
            'merge_time': time.time()
        })

        # 合并处理历史
        merged_container.processing_history = (
            copy.deepcopy(self.processing_history) +
            copy.deepcopy(other_container.processing_history)
        )

        # 更新状态
        merged_container.is_processed = self.is_processed and other_container.is_processed
        merged_container.is_grouped = self.is_grouped and other_container.is_grouped
        merged_container.is_labeled = self.is_labeled and other_container.is_labeled

        # 记录合并操作
        merged_container._add_processing_record(
            operation="merge_containers",
            processor="LayerDataContainer",
            input_count=len(self.get_all_entities()) + len(other_container.get_all_entities()),
            output_count=len(merged_container.get_all_entities()),
            metadata={
                'source_containers': [self.layer_name, other_container.layer_name]
            }
        )

        return merged_container

    def export_to_dict(self) -> Dict[str, Any]:
        """
        导出为字典格式（用于序列化）

        Returns:
            字典格式的容器数据
        """
        return {
            'layer_name': self.layer_name,
            'layer_type': self.layer_type.value,
            'creation_time': self.creation_time,
            'original_entities': self.original_entities,
            'processed_entities': self.processed_entities,
            'groups': self.groups,
            'labeled_entities': self.labeled_entities,
            'metadata': self.metadata,
            'processing_history': [
                {
                    'timestamp': record.timestamp,
                    'operation': record.operation,
                    'processor': record.processor,
                    'input_count': record.input_count,
                    'output_count': record.output_count,
                    'metadata': record.metadata
                }
                for record in self.processing_history
            ],
            'is_processed': self.is_processed,
            'is_grouped': self.is_grouped,
            'is_labeled': self.is_labeled
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LayerDataContainer':
        """
        从字典创建容器（用于反序列化）

        Args:
            data: 字典格式的容器数据

        Returns:
            图层数据容器实例
        """
        # 创建容器
        layer_type = LayerType(data['layer_type'])
        container = cls(
            layer_name=data['layer_name'],
            layer_type=layer_type,
            original_entities=data.get('original_entities', [])
        )

        # 恢复数据
        container.creation_time = data.get('creation_time', time.time())
        container.processed_entities = data.get('processed_entities', [])
        container.groups = data.get('groups', [])
        container.labeled_entities = data.get('labeled_entities', [])
        container.metadata = data.get('metadata', {})

        # 恢复处理历史
        container.processing_history = []
        for record_data in data.get('processing_history', []):
            record = ProcessingRecord(
                timestamp=record_data['timestamp'],
                operation=record_data['operation'],
                processor=record_data['processor'],
                input_count=record_data['input_count'],
                output_count=record_data['output_count'],
                metadata=record_data.get('metadata', {})
            )
            container.processing_history.append(record)

        # 恢复状态
        container.is_processed = data.get('is_processed', False)
        container.is_grouped = data.get('is_grouped', False)
        container.is_labeled = data.get('is_labeled', False)

        return container

    def __str__(self) -> str:
        """字符串表示"""
        return (f"LayerDataContainer(name='{self.layer_name}', "
                f"type='{self.layer_type.value}', "
                f"entities={len(self.get_all_entities())}, "
                f"groups={len(self.groups)}, "
                f"stage='{self.metadata.get('processing_stage', 'unknown')}')")

    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()

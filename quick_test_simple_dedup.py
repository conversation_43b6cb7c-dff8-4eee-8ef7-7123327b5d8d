#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试简单去重集成效果
"""

import os
import sys
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from selective_layer_processor import SelectiveLayerProcessor
    print("✅ 选择性图层处理器导入成功")
except ImportError as e:
    print(f"❌ 选择性图层处理器导入失败: {e}")
    sys.exit(1)

def quick_test():
    """快速测试"""
    print("🧪 快速测试简单去重集成")
    print("=" * 50)
    
    # 创建测试数据
    test_entities = [
        # 墙体图层 - 应该迭代合并
        {
            'id': 'wall_1',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1,
            'points': [[0, 0], [10, 0]]
        },
        {
            'id': 'wall_2',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [10, 0],
            'end_point': [20, 0],
            'color': 1,
            'points': [[10, 0], [20, 0]]
        },
        
        # 门图层 - 应该简单去重
        {
            'id': 'door_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        },
        {
            'id': 'door_1_duplicate',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        }
    ]
    
    print(f"📊 输入: {len(test_entities)} 个实体")
    print("   - A-WALL: 2个线条（应该合并）")
    print("   - A-DOOR: 2个线条（1个重复，应该去重）")
    
    # 创建处理器
    processor = SelectiveLayerProcessor()
    
    # 执行处理
    start_time = time.time()
    result = processor.process_entities(test_entities)
    processing_time = time.time() - start_time
    
    print(f"\n📈 输出: {len(result)} 个实体")
    print(f"🕒 处理时间: {processing_time:.3f} 秒")
    
    # 分析结果
    wall_entities = [e for e in result if e.get('layer') == 'A-WALL']
    door_entities = [e for e in result if e.get('layer') == 'A-DOOR']
    
    print(f"\n🔍 结果分析:")
    print(f"   A-WALL: {len(wall_entities)} 个实体")
    for entity in wall_entities:
        proc_type = entity.get('processing_type', 'unknown')
        print(f"     - 处理方式: {proc_type}")
    
    print(f"   A-DOOR: {len(door_entities)} 个实体")
    for entity in door_entities:
        proc_type = entity.get('processing_type', 'unknown')
        print(f"     - 处理方式: {proc_type}")
    
    # 验证结果
    expected_wall = 1  # 2个墙体线条应该合并为1个
    expected_door = 1  # 2个门线条（1个重复）应该去重为1个
    
    wall_correct = len(wall_entities) == expected_wall
    door_correct = len(door_entities) == expected_door
    
    print(f"\n✅ 验证结果:")
    print(f"   墙体合并: {'正确' if wall_correct else '错误'} (预期{expected_wall}个，实际{len(wall_entities)}个)")
    print(f"   门窗去重: {'正确' if door_correct else '错误'} (预期{expected_door}个，实际{len(door_entities)}个)")
    
    success = wall_correct and door_correct
    print(f"   整体结果: {'✅ 成功' if success else '❌ 失败'}")
    
    return success

if __name__ == "__main__":
    success = quick_test()
    print(f"\n🎉 测试{'成功' if success else '失败'}")
    sys.exit(0 if success else 1)

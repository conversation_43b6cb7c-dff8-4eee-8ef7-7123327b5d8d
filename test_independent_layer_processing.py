#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试独立图层处理功能
验证每个图层单独处理的效果
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from independent_layer_processor import IndependentLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_multi_layer_test_data() -> List[Dict[str, Any]]:
    """创建多图层测试数据"""
    entities = []
    
    # 墙体图层1 (A-WALL) - 应该迭代合并
    entities.extend([
        {
            'id': 'wall1_line1',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1,
            'points': [[0, 0], [10, 0]]
        },
        {
            'id': 'wall1_line2',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [10, 0],
            'end_point': [20, 0],
            'color': 1,
            'points': [[10, 0], [20, 0]]
        },
        {
            'id': 'wall1_text',
            'type': 'TEXT',
            'layer': 'A-WALL',
            'text': '墙体标注',
            'position': [10, 5],
            'color': 1
        }
    ])
    
    # 墙体图层2 (WALL-STRUCTURE) - 应该迭代合并
    entities.extend([
        {
            'id': 'wall2_line1',
            'type': 'LINE',
            'layer': 'WALL-STRUCTURE',
            'start_point': [0, 20],
            'end_point': [15, 20],
            'color': 1,
            'points': [[0, 20], [15, 20]]
        },
        {
            'id': 'wall2_line2',
            'type': 'LINE',
            'layer': 'WALL-STRUCTURE',
            'start_point': [15, 20],
            'end_point': [30, 20],
            'color': 1,
            'points': [[15, 20], [30, 20]]
        }
    ])
    
    # 门窗图层 (A-DOOR) - 应该简单去重
    entities.extend([
        {
            'id': 'door1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        },
        {
            'id': 'door1_duplicate',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [5, 0],
            'end_point': [8, 0],
            'color': 2,
            'points': [[5, 0], [8, 0]]
        },
        {
            'id': 'door2',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [15, 0],
            'end_point': [18, 0],
            'color': 2,
            'points': [[15, 0], [18, 0]]
        }
    ])
    
    # 文字图层 (A-TEXT) - 应该简单去重
    entities.extend([
        {
            'id': 'text1',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [25, 10],
            'height': 3,
            'color': 3
        },
        {
            'id': 'text1_duplicate',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [25, 10],
            'height': 3,
            'color': 3
        },
        {
            'id': 'text2',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间2',
            'position': [35, 10],
            'height': 3,
            'color': 3
        }
    ])
    
    # 设备图层 (EQUIPMENT) - 应该简单去重
    entities.extend([
        {
            'id': 'equipment1',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [40, 15],
            'radius': 3,
            'color': 4
        },
        {
            'id': 'equipment1_duplicate',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [40, 15],
            'radius': 3,
            'color': 4
        }
    ])
    
    return entities


def test_independent_layer_processor():
    """测试独立图层处理器"""
    print("🧪 测试独立图层处理器")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_multi_layer_test_data()
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    
    # 按图层统计输入数据
    input_layer_stats = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in input_layer_stats:
            input_layer_stats[layer] = {'total': 0, 'types': set()}
        input_layer_stats[layer]['total'] += 1
        input_layer_stats[layer]['types'].add(entity.get('type', 'UNKNOWN'))
    
    print("📋 输入数据统计:")
    for layer, stats in input_layer_stats.items():
        print(f"   {layer}: {stats['total']} 个实体, 类型: {list(stats['types'])}")
    
    # 创建独立图层处理器
    processor = IndependentLayerProcessor(distance_threshold=5, angle_threshold=2)
    
    print(f"\n🔄 执行独立图层处理...")
    start_time = time.time()
    
    processed_entities = processor.process_entities(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(processed_entities)}")
    
    # 获取详细的处理报告
    processing_report = processor.get_layer_processing_report()
    
    print(f"\n📊 处理报告:")
    print(f"   总图层数: {processing_report['summary']['total_layers']}")
    print(f"   墙体图层: {processing_report['summary']['wall_layers']}")
    print(f"   其他图层: {processing_report['summary']['other_layers']}")
    print(f"   整体减少率: {processing_report['summary']['overall_reduction_rate']:.1%}")
    
    print(f"\n📋 图层处理顺序:")
    for i, layer_name in enumerate(processing_report['processing_order']):
        print(f"   {i+1}. {layer_name}")
    
    print(f"\n📝 图层处理详情:")
    for layer_name, details in processing_report['layer_details'].items():
        print(f"   {layer_name}:")
        print(f"     类型: {details['layer_type']}")
        print(f"     输入: {details['input_count']} -> 输出: {details['output_count']}")
        print(f"     处理方式: {details['processing_method']}")
        print(f"     处理时间: {details['processing_time']:.3f} 秒")
        if details['reduction_count'] > 0:
            print(f"     减少实体: {details['reduction_count']} 个")
    
    # 验证处理结果
    validation = processor.validate_processing_result(test_entities, processed_entities)
    print(f"\n🔍 验证结果:")
    print(f"   验证通过: {validation['is_valid']}")
    print(f"   图层保留率: {validation['layer_integrity']['preservation_rate']:.1%}")
    print(f"   实体减少率: {validation['statistics']['reduction_rate']:.1%}")
    print(f"   所有实体已处理: {validation['processing_integrity']['all_entities_processed']}")
    
    if validation['errors']:
        print(f"   错误: {validation['errors']}")
    if validation['warnings']:
        print(f"   警告: {validation['warnings']}")
    
    return {
        'input_entities': len(test_entities),
        'output_entities': len(processed_entities),
        'processing_time': processing_time,
        'input_layer_stats': input_layer_stats,
        'processing_report': processing_report,
        'validation': validation
    }


def test_independent_mode_in_manager():
    """测试模式管理器中的独立图层处理模式"""
    print("\n🧪 测试模式管理器中的独立图层处理模式")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_multi_layer_test_data()
    
    # 创建模式管理器
    mode_manager = LineProcessingModeManager()
    
    # 设置为独立图层处理模式
    mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    
    print(f"📊 当前模式: {mode_manager.get_mode_info()['mode_config']['name']}")
    print(f"📊 模式描述: {mode_manager.get_mode_info()['mode_config']['description']}")
    
    print(f"\n🔄 执行独立图层处理...")
    start_time = time.time()
    
    result = mode_manager.process_entities(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理成功: {result['success']}")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(result['entities'])}")
    print(f"   模式信息: {result.get('mode_info', '')}")
    
    # 显示处理报告
    if 'processing_report' in result:
        report = result['processing_report']
        print(f"\n📊 详细报告:")
        print(f"   处理图层: {report['summary']['total_layers']} 个")
        print(f"   墙体图层: {report['summary']['wall_layers']} 个")
        print(f"   其他图层: {report['summary']['other_layers']} 个")
        print(f"   整体减少率: {report['summary']['overall_reduction_rate']:.1%}")
    
    # 显示验证结果
    if 'validation' in result:
        validation = result['validation']
        print(f"\n🔍 验证结果:")
        print(f"   验证通过: {validation['is_valid']}")
        print(f"   图层保留率: {validation['layer_integrity']['preservation_rate']:.1%}")
    
    return result


def run_comprehensive_independent_test():
    """运行综合独立图层处理测试"""
    print("🚀 开始独立图层处理综合测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'processor_test_result': None,
        'manager_test_result': None,
        'summary': {}
    }
    
    try:
        # 1. 测试独立图层处理器
        results['processor_test_result'] = test_independent_layer_processor()
        
        # 2. 测试模式管理器中的独立模式
        results['manager_test_result'] = test_independent_mode_in_manager()
        
        # 3. 生成总结
        total_time = time.time() - start_time
        
        # 分析处理效果
        processor_success = results['processor_test_result'] is not None
        manager_success = results['manager_test_result'] is not None and results['manager_test_result']['success']
        
        # 分析性能
        processor_time = results['processor_test_result']['processing_time'] if processor_success else 0
        manager_time = results['manager_test_result']['processing_time'] if manager_success else 0
        
        results['summary'] = {
            'total_test_time': total_time,
            'processor_test_success': processor_success,
            'manager_test_success': manager_success,
            'all_tests_passed': processor_success and manager_success,
            'processor_processing_time': processor_time,
            'manager_processing_time': manager_time,
            'performance_excellent': processor_time < 0.1 and manager_time < 0.1
        }
        
        print(f"\n🎉 独立图层处理综合测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   处理器测试: {'通过' if processor_success else '失败'}")
        print(f"   管理器测试: {'通过' if manager_success else '失败'}")
        print(f"   性能表现: {'优秀' if results['summary']['performance_excellent'] else '良好'}")
        print(f"   所有测试通过: {'是' if results['summary']['all_tests_passed'] else '否'}")
        
        # 保存测试结果
        with open('independent_layer_processing_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: independent_layer_processing_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_independent_test()
    sys.exit(0 if success else 1)

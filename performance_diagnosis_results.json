{"test_time": 1753722172.350518, "processing_test": {"processor_time": 0.03597378730773926, "manager_time": 0.041974782943725586, "input_entities": 484, "output_entities": 454, "processed_entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0, 0], [3, 0]], "start_point": [0, 0], "end_point": [3, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[5, 0], [8, 0]], "start_point": [5, 0], "end_point": [8, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[10, 0], [13, 0]], "start_point": [10, 0], "end_point": [13, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[15, 0], [18, 0]], "start_point": [15, 0], "end_point": [18, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[20, 0], [23, 0]], "start_point": [20, 0], "end_point": [23, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[25, 0], [28, 0]], "start_point": [25, 0], "end_point": [28, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[30, 0], [33, 0]], "start_point": [30, 0], "end_point": [33, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[35, 0], [38, 0]], "start_point": [35, 0], "end_point": [38, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[40, 0], [43, 0]], "start_point": [40, 0], "end_point": [43, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[45, 0], [48, 0]], "start_point": [45, 0], "end_point": [48, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[50, 0], [53, 0]], "start_point": [50, 0], "end_point": [53, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[55, 0], [58, 0]], "start_point": [55, 0], "end_point": [58, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[60, 0], [63, 0]], "start_point": [60, 0], "end_point": [63, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[65, 0], [68, 0]], "start_point": [65, 0], "end_point": [68, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[70, 0], [73, 0]], "start_point": [70, 0], "end_point": [73, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[75, 0], [78, 0]], "start_point": [75, 0], "end_point": [78, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[80, 0], [83, 0]], "start_point": [80, 0], "end_point": [83, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[85, 0], [88, 0]], "start_point": [85, 0], "end_point": [88, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[90, 0], [93, 0]], "start_point": [90, 0], "end_point": [93, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[95, 0], [98, 0]], "start_point": [95, 0], "end_point": [98, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[100, 0], [103, 0]], "start_point": [100, 0], "end_point": [103, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[105, 0], [108, 0]], "start_point": [105, 0], "end_point": [108, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[110, 0], [113, 0]], "start_point": [110, 0], "end_point": [113, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[115, 0], [118, 0]], "start_point": [115, 0], "end_point": [118, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.379463, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[120, 0], [123, 0]], "start_point": [120, 0], "end_point": [123, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[125, 0], [128, 0]], "start_point": [125, 0], "end_point": [128, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[130, 0], [133, 0]], "start_point": [130, 0], "end_point": [133, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[135, 0], [138, 0]], "start_point": [135, 0], "end_point": [138, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[140, 0], [143, 0]], "start_point": [140, 0], "end_point": [143, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[145, 0], [148, 0]], "start_point": [145, 0], "end_point": [148, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[150, 0], [153, 0]], "start_point": [150, 0], "end_point": [153, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[155, 0], [158, 0]], "start_point": [155, 0], "end_point": [158, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[160, 0], [163, 0]], "start_point": [160, 0], "end_point": [163, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[165, 0], [168, 0]], "start_point": [165, 0], "end_point": [168, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[170, 0], [173, 0]], "start_point": [170, 0], "end_point": [173, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[175, 0], [178, 0]], "start_point": [175, 0], "end_point": [178, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[180, 0], [183, 0]], "start_point": [180, 0], "end_point": [183, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[185, 0], [188, 0]], "start_point": [185, 0], "end_point": [188, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[190, 0], [193, 0]], "start_point": [190, 0], "end_point": [193, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[195, 0], [198, 0]], "start_point": [195, 0], "end_point": [198, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[200, 0], [203, 0]], "start_point": [200, 0], "end_point": [203, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[205, 0], [208, 0]], "start_point": [205, 0], "end_point": [208, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[210, 0], [213, 0]], "start_point": [210, 0], "end_point": [213, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[215, 0], [218, 0]], "start_point": [215, 0], "end_point": [218, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[220, 0], [223, 0]], "start_point": [220, 0], "end_point": [223, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[225, 0], [228, 0]], "start_point": [225, 0], "end_point": [228, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[230, 0], [233, 0]], "start_point": [230, 0], "end_point": [233, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[235, 0], [238, 0]], "start_point": [235, 0], "end_point": [238, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[240, 0], [243, 0]], "start_point": [240, 0], "end_point": [243, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[245, 0], [248, 0]], "start_point": [245, 0], "end_point": [248, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[250, 0], [253, 0]], "start_point": [250, 0], "end_point": [253, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[255, 0], [258, 0]], "start_point": [255, 0], "end_point": [258, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[260, 0], [263, 0]], "start_point": [260, 0], "end_point": [263, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[265, 0], [268, 0]], "start_point": [265, 0], "end_point": [268, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[270, 0], [273, 0]], "start_point": [270, 0], "end_point": [273, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[275, 0], [278, 0]], "start_point": [275, 0], "end_point": [278, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[280, 0], [283, 0]], "start_point": [280, 0], "end_point": [283, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[285, 0], [288, 0]], "start_point": [285, 0], "end_point": [288, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[290, 0], [293, 0]], "start_point": [290, 0], "end_point": [293, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[295, 0], [298, 0]], "start_point": [295, 0], "end_point": [298, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[300, 0], [303, 0]], "start_point": [300, 0], "end_point": [303, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[305, 0], [308, 0]], "start_point": [305, 0], "end_point": [308, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[310, 0], [313, 0]], "start_point": [310, 0], "end_point": [313, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[315, 0], [318, 0]], "start_point": [315, 0], "end_point": [318, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[320, 0], [323, 0]], "start_point": [320, 0], "end_point": [323, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[325, 0], [328, 0]], "start_point": [325, 0], "end_point": [328, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[330, 0], [333, 0]], "start_point": [330, 0], "end_point": [333, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[335, 0], [338, 0]], "start_point": [335, 0], "end_point": [338, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[340, 0], [343, 0]], "start_point": [340, 0], "end_point": [343, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[345, 0], [348, 0]], "start_point": [345, 0], "end_point": [348, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[350, 0], [353, 0]], "start_point": [350, 0], "end_point": [353, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[355, 0], [358, 0]], "start_point": [355, 0], "end_point": [358, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[360, 0], [363, 0]], "start_point": [360, 0], "end_point": [363, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[365, 0], [368, 0]], "start_point": [365, 0], "end_point": [368, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[370, 0], [373, 0]], "start_point": [370, 0], "end_point": [373, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.380474, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[375, 0], [378, 0]], "start_point": [375, 0], "end_point": [378, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[380, 0], [383, 0]], "start_point": [380, 0], "end_point": [383, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[385, 0], [388, 0]], "start_point": [385, 0], "end_point": [388, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[390, 0], [393, 0]], "start_point": [390, 0], "end_point": [393, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[395, 0], [398, 0]], "start_point": [395, 0], "end_point": [398, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[400, 0], [403, 0]], "start_point": [400, 0], "end_point": [403, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[405, 0], [408, 0]], "start_point": [405, 0], "end_point": [408, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[410, 0], [413, 0]], "start_point": [410, 0], "end_point": [413, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[415, 0], [418, 0]], "start_point": [415, 0], "end_point": [418, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[420, 0], [423, 0]], "start_point": [420, 0], "end_point": [423, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[425, 0], [428, 0]], "start_point": [425, 0], "end_point": [428, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[430, 0], [433, 0]], "start_point": [430, 0], "end_point": [433, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[435, 0], [438, 0]], "start_point": [435, 0], "end_point": [438, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[440, 0], [443, 0]], "start_point": [440, 0], "end_point": [443, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[445, 0], [448, 0]], "start_point": [445, 0], "end_point": [448, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[450, 0], [453, 0]], "start_point": [450, 0], "end_point": [453, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[455, 0], [458, 0]], "start_point": [455, 0], "end_point": [458, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[460, 0], [463, 0]], "start_point": [460, 0], "end_point": [463, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[465, 0], [468, 0]], "start_point": [465, 0], "end_point": [468, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[470, 0], [473, 0]], "start_point": [470, 0], "end_point": [473, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[475, 0], [478, 0]], "start_point": [475, 0], "end_point": [478, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[480, 0], [483, 0]], "start_point": [480, 0], "end_point": [483, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[485, 0], [488, 0]], "start_point": [485, 0], "end_point": [488, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[490, 0], [493, 0]], "start_point": [490, 0], "end_point": [493, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[495, 0], [498, 0]], "start_point": [495, 0], "end_point": [498, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[500, 0], [503, 0]], "start_point": [500, 0], "end_point": [503, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[505, 0], [508, 0]], "start_point": [505, 0], "end_point": [508, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[510, 0], [513, 0]], "start_point": [510, 0], "end_point": [513, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[515, 0], [518, 0]], "start_point": [515, 0], "end_point": [518, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[520, 0], [523, 0]], "start_point": [520, 0], "end_point": [523, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[525, 0], [528, 0]], "start_point": [525, 0], "end_point": [528, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[530, 0], [533, 0]], "start_point": [530, 0], "end_point": [533, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[535, 0], [538, 0]], "start_point": [535, 0], "end_point": [538, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[540, 0], [543, 0]], "start_point": [540, 0], "end_point": [543, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[545, 0], [548, 0]], "start_point": [545, 0], "end_point": [548, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[550, 0], [553, 0]], "start_point": [550, 0], "end_point": [553, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[555, 0], [558, 0]], "start_point": [555, 0], "end_point": [558, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[560, 0], [563, 0]], "start_point": [560, 0], "end_point": [563, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[565, 0], [568, 0]], "start_point": [565, 0], "end_point": [568, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[570, 0], [573, 0]], "start_point": [570, 0], "end_point": [573, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[575, 0], [578, 0]], "start_point": [575, 0], "end_point": [578, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[580, 0], [583, 0]], "start_point": [580, 0], "end_point": [583, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[585, 0], [588, 0]], "start_point": [585, 0], "end_point": [588, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[590, 0], [593, 0]], "start_point": [590, 0], "end_point": [593, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[595, 0], [598, 0]], "start_point": [595, 0], "end_point": [598, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[600, 0], [603, 0]], "start_point": [600, 0], "end_point": [603, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.381501, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[605, 0], [608, 0]], "start_point": [605, 0], "end_point": [608, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[610, 0], [613, 0]], "start_point": [610, 0], "end_point": [613, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[615, 0], [618, 0]], "start_point": [615, 0], "end_point": [618, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[620, 0], [623, 0]], "start_point": [620, 0], "end_point": [623, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[625, 0], [628, 0]], "start_point": [625, 0], "end_point": [628, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[630, 0], [633, 0]], "start_point": [630, 0], "end_point": [633, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[635, 0], [638, 0]], "start_point": [635, 0], "end_point": [638, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[640, 0], [643, 0]], "start_point": [640, 0], "end_point": [643, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[645, 0], [648, 0]], "start_point": [645, 0], "end_point": [648, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[650, 0], [653, 0]], "start_point": [650, 0], "end_point": [653, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[655, 0], [658, 0]], "start_point": [655, 0], "end_point": [658, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[660, 0], [663, 0]], "start_point": [660, 0], "end_point": [663, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[665, 0], [668, 0]], "start_point": [665, 0], "end_point": [668, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[670, 0], [673, 0]], "start_point": [670, 0], "end_point": [673, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[675, 0], [678, 0]], "start_point": [675, 0], "end_point": [678, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[680, 0], [683, 0]], "start_point": [680, 0], "end_point": [683, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[685, 0], [688, 0]], "start_point": [685, 0], "end_point": [688, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[690, 0], [693, 0]], "start_point": [690, 0], "end_point": [693, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[695, 0], [698, 0]], "start_point": [695, 0], "end_point": [698, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[700, 0], [703, 0]], "start_point": [700, 0], "end_point": [703, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[705, 0], [708, 0]], "start_point": [705, 0], "end_point": [708, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[710, 0], [713, 0]], "start_point": [710, 0], "end_point": [713, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[715, 0], [718, 0]], "start_point": [715, 0], "end_point": [718, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[720, 0], [723, 0]], "start_point": [720, 0], "end_point": [723, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[725, 0], [728, 0]], "start_point": [725, 0], "end_point": [728, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[730, 0], [733, 0]], "start_point": [730, 0], "end_point": [733, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[735, 0], [738, 0]], "start_point": [735, 0], "end_point": [738, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[740, 0], [743, 0]], "start_point": [740, 0], "end_point": [743, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[745, 0], [748, 0]], "start_point": [745, 0], "end_point": [748, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[750, 0], [753, 0]], "start_point": [750, 0], "end_point": [753, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[755, 0], [758, 0]], "start_point": [755, 0], "end_point": [758, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[760, 0], [763, 0]], "start_point": [760, 0], "end_point": [763, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[765, 0], [768, 0]], "start_point": [765, 0], "end_point": [768, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[770, 0], [773, 0]], "start_point": [770, 0], "end_point": [773, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[775, 0], [778, 0]], "start_point": [775, 0], "end_point": [778, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[780, 0], [783, 0]], "start_point": [780, 0], "end_point": [783, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3824997, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[785, 0], [788, 0]], "start_point": [785, 0], "end_point": [788, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[790, 0], [793, 0]], "start_point": [790, 0], "end_point": [793, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[795, 0], [798, 0]], "start_point": [795, 0], "end_point": [798, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[800, 0], [803, 0]], "start_point": [800, 0], "end_point": [803, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[805, 0], [808, 0]], "start_point": [805, 0], "end_point": [808, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[810, 0], [813, 0]], "start_point": [810, 0], "end_point": [813, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[815, 0], [818, 0]], "start_point": [815, 0], "end_point": [818, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[820, 0], [823, 0]], "start_point": [820, 0], "end_point": [823, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[825, 0], [828, 0]], "start_point": [825, 0], "end_point": [828, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[830, 0], [833, 0]], "start_point": [830, 0], "end_point": [833, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[835, 0], [838, 0]], "start_point": [835, 0], "end_point": [838, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[840, 0], [843, 0]], "start_point": [840, 0], "end_point": [843, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[845, 0], [848, 0]], "start_point": [845, 0], "end_point": [848, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[850, 0], [853, 0]], "start_point": [850, 0], "end_point": [853, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[855, 0], [858, 0]], "start_point": [855, 0], "end_point": [858, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[860, 0], [863, 0]], "start_point": [860, 0], "end_point": [863, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[865, 0], [868, 0]], "start_point": [865, 0], "end_point": [868, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[870, 0], [873, 0]], "start_point": [870, 0], "end_point": [873, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[875, 0], [878, 0]], "start_point": [875, 0], "end_point": [878, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[880, 0], [883, 0]], "start_point": [880, 0], "end_point": [883, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[885, 0], [888, 0]], "start_point": [885, 0], "end_point": [888, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[890, 0], [893, 0]], "start_point": [890, 0], "end_point": [893, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[895, 0], [898, 0]], "start_point": [895, 0], "end_point": [898, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[900, 0], [903, 0]], "start_point": [900, 0], "end_point": [903, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[905, 0], [908, 0]], "start_point": [905, 0], "end_point": [908, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[910, 0], [913, 0]], "start_point": [910, 0], "end_point": [913, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[915, 0], [918, 0]], "start_point": [915, 0], "end_point": [918, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[920, 0], [923, 0]], "start_point": [920, 0], "end_point": [923, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[925, 0], [928, 0]], "start_point": [925, 0], "end_point": [928, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[930, 0], [933, 0]], "start_point": [930, 0], "end_point": [933, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[935, 0], [938, 0]], "start_point": [935, 0], "end_point": [938, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[940, 0], [943, 0]], "start_point": [940, 0], "end_point": [943, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[945, 0], [948, 0]], "start_point": [945, 0], "end_point": [948, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[950, 0], [953, 0]], "start_point": [950, 0], "end_point": [953, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[955, 0], [958, 0]], "start_point": [955, 0], "end_point": [958, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[960, 0], [963, 0]], "start_point": [960, 0], "end_point": [963, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[965, 0], [968, 0]], "start_point": [965, 0], "end_point": [968, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[970, 0], [973, 0]], "start_point": [970, 0], "end_point": [973, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[975, 0], [978, 0]], "start_point": [975, 0], "end_point": [978, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[980, 0], [983, 0]], "start_point": [980, 0], "end_point": [983, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[985, 0], [988, 0]], "start_point": [985, 0], "end_point": [988, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[990, 0], [993, 0]], "start_point": [990, 0], "end_point": [993, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[995, 0], [998, 0]], "start_point": [995, 0], "end_point": [998, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1000, 0], [1003, 0]], "start_point": [1000, 0], "end_point": [1003, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1005, 0], [1008, 0]], "start_point": [1005, 0], "end_point": [1008, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1010, 0], [1013, 0]], "start_point": [1010, 0], "end_point": [1013, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1015, 0], [1018, 0]], "start_point": [1015, 0], "end_point": [1018, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1020, 0], [1023, 0]], "start_point": [1020, 0], "end_point": [1023, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1025, 0], [1028, 0]], "start_point": [1025, 0], "end_point": [1028, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1030, 0], [1033, 0]], "start_point": [1030, 0], "end_point": [1033, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[1035, 0], [1038, 0]], "start_point": [1035, 0], "end_point": [1038, 0], "merged_from_count": 208, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753722172.3665109, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_0", "type": "LINE", "layer": "A-WINDOW", "start_point": [0, 10], "end_point": [4, 10], "color": 2, "points": [[0, 10], [4, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "ea02cc2a990ce12c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_1", "type": "LINE", "layer": "A-WINDOW", "start_point": [8, 10], "end_point": [12, 10], "color": 2, "points": [[8, 10], [12, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "ba723fdd7398ec8d", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_2", "type": "LINE", "layer": "A-WINDOW", "start_point": [16, 10], "end_point": [20, 10], "color": 2, "points": [[16, 10], [20, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "ab482a85d3ab8455", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_3", "type": "LINE", "layer": "A-WINDOW", "start_point": [24, 10], "end_point": [28, 10], "color": 2, "points": [[24, 10], [28, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "dc7cbf663543e113", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3835025, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_4", "type": "LINE", "layer": "A-WINDOW", "start_point": [32, 10], "end_point": [36, 10], "color": 2, "points": [[32, 10], [36, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "c161d7cb78eb6b4e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_5", "type": "LINE", "layer": "A-WINDOW", "start_point": [40, 10], "end_point": [44, 10], "color": 2, "points": [[40, 10], [44, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "0fafb16fada65f45", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_6", "type": "LINE", "layer": "A-WINDOW", "start_point": [48, 10], "end_point": [52, 10], "color": 2, "points": [[48, 10], [52, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "50b05fd019b294e6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_7", "type": "LINE", "layer": "A-WINDOW", "start_point": [56, 10], "end_point": [60, 10], "color": 2, "points": [[56, 10], [60, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "4ad1c8bab9445ffb", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_8", "type": "LINE", "layer": "A-WINDOW", "start_point": [64, 10], "end_point": [68, 10], "color": 2, "points": [[64, 10], [68, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "ac915272c565926f", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_9", "type": "LINE", "layer": "A-WINDOW", "start_point": [72, 10], "end_point": [76, 10], "color": 2, "points": [[72, 10], [76, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "7d9c2ccbc5e7a9a5", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_11", "type": "LINE", "layer": "A-WINDOW", "start_point": [88, 10], "end_point": [92, 10], "color": 2, "points": [[88, 10], [92, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "a891a00ca6a3ea68", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_12", "type": "LINE", "layer": "A-WINDOW", "start_point": [96, 10], "end_point": [100, 10], "color": 2, "points": [[96, 10], [100, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "06af3cd287e7db3e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_13", "type": "LINE", "layer": "A-WINDOW", "start_point": [104, 10], "end_point": [108, 10], "color": 2, "points": [[104, 10], [108, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3684695, "dedup_context": "other_layer", "dedup_hash": "5e265ce4cb97de9c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_14", "type": "LINE", "layer": "A-WINDOW", "start_point": [112, 10], "end_point": [116, 10], "color": 2, "points": [[112, 10], [116, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "48b3b6d4aa9ffa62", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_15", "type": "LINE", "layer": "A-WINDOW", "start_point": [120, 10], "end_point": [124, 10], "color": 2, "points": [[120, 10], [124, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "9a1147256a6d92a8", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_16", "type": "LINE", "layer": "A-WINDOW", "start_point": [128, 10], "end_point": [132, 10], "color": 2, "points": [[128, 10], [132, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "ec4f1789e9f71b37", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_17", "type": "LINE", "layer": "A-WINDOW", "start_point": [136, 10], "end_point": [140, 10], "color": 2, "points": [[136, 10], [140, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "66d7e510578c92f0", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_18", "type": "LINE", "layer": "A-WINDOW", "start_point": [144, 10], "end_point": [148, 10], "color": 2, "points": [[144, 10], [148, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "9307d08438bfb37d", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_19", "type": "LINE", "layer": "A-WINDOW", "start_point": [152, 10], "end_point": [156, 10], "color": 2, "points": [[152, 10], [156, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "cb87354cef9850b5", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_21", "type": "LINE", "layer": "A-WINDOW", "start_point": [168, 10], "end_point": [172, 10], "color": 2, "points": [[168, 10], [172, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "17ed5a9bc6ff3190", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_22", "type": "LINE", "layer": "A-WINDOW", "start_point": [176, 10], "end_point": [180, 10], "color": 2, "points": [[176, 10], [180, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "e3b92bbcf82fb931", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_23", "type": "LINE", "layer": "A-WINDOW", "start_point": [184, 10], "end_point": [188, 10], "color": 2, "points": [[184, 10], [188, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "e38d8b5dc6a93a3d", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_24", "type": "LINE", "layer": "A-WINDOW", "start_point": [192, 10], "end_point": [196, 10], "color": 2, "points": [[192, 10], [196, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "510e4a3f1ba54204", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_25", "type": "LINE", "layer": "A-WINDOW", "start_point": [200, 10], "end_point": [204, 10], "color": 2, "points": [[200, 10], [204, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "5c55da1968c4a950", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_26", "type": "LINE", "layer": "A-WINDOW", "start_point": [208, 10], "end_point": [212, 10], "color": 2, "points": [[208, 10], [212, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "72c863fb8caefd85", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_27", "type": "LINE", "layer": "A-WINDOW", "start_point": [216, 10], "end_point": [220, 10], "color": 2, "points": [[216, 10], [220, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "191885f10ed428f4", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_28", "type": "LINE", "layer": "A-WINDOW", "start_point": [224, 10], "end_point": [228, 10], "color": 2, "points": [[224, 10], [228, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "ec3e576e082b2a28", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_29", "type": "LINE", "layer": "A-WINDOW", "start_point": [232, 10], "end_point": [236, 10], "color": 2, "points": [[232, 10], [236, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "b83f215f3cec598d", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_31", "type": "LINE", "layer": "A-WINDOW", "start_point": [248, 10], "end_point": [252, 10], "color": 2, "points": [[248, 10], [252, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "b75debcffa739339", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_32", "type": "LINE", "layer": "A-WINDOW", "start_point": [256, 10], "end_point": [260, 10], "color": 2, "points": [[256, 10], [260, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "a78f9e66cfd7d4be", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_33", "type": "LINE", "layer": "A-WINDOW", "start_point": [264, 10], "end_point": [268, 10], "color": 2, "points": [[264, 10], [268, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "fe0a09b427c80fc4", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_34", "type": "LINE", "layer": "A-WINDOW", "start_point": [272, 10], "end_point": [276, 10], "color": 2, "points": [[272, 10], [276, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "eaffdea690adeb82", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_35", "type": "LINE", "layer": "A-WINDOW", "start_point": [280, 10], "end_point": [284, 10], "color": 2, "points": [[280, 10], [284, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "cbe2173e6a7a4400", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_36", "type": "LINE", "layer": "A-WINDOW", "start_point": [288, 10], "end_point": [292, 10], "color": 2, "points": [[288, 10], [292, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "4875cdf0242bb5f7", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_37", "type": "LINE", "layer": "A-WINDOW", "start_point": [296, 10], "end_point": [300, 10], "color": 2, "points": [[296, 10], [300, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "038c40082a95944d", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_38", "type": "LINE", "layer": "A-WINDOW", "start_point": [304, 10], "end_point": [308, 10], "color": 2, "points": [[304, 10], [308, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "f944d976af74ea54", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_39", "type": "LINE", "layer": "A-WINDOW", "start_point": [312, 10], "end_point": [316, 10], "color": 2, "points": [[312, 10], [316, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "ecfc4a4455238245", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_41", "type": "LINE", "layer": "A-WINDOW", "start_point": [328, 10], "end_point": [332, 10], "color": 2, "points": [[328, 10], [332, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "fd915aed6d36667b", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_42", "type": "LINE", "layer": "A-WINDOW", "start_point": [336, 10], "end_point": [340, 10], "color": 2, "points": [[336, 10], [340, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "ba76b5ab10fc4581", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_43", "type": "LINE", "layer": "A-WINDOW", "start_point": [344, 10], "end_point": [348, 10], "color": 2, "points": [[344, 10], [348, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "f5e272beb6dbe5d9", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_44", "type": "LINE", "layer": "A-WINDOW", "start_point": [352, 10], "end_point": [356, 10], "color": 2, "points": [[352, 10], [356, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "8077d7dac5b495e8", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3845012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_45", "type": "LINE", "layer": "A-WINDOW", "start_point": [360, 10], "end_point": [364, 10], "color": 2, "points": [[360, 10], [364, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "7b0b3b2d055ff178", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_46", "type": "LINE", "layer": "A-WINDOW", "start_point": [368, 10], "end_point": [372, 10], "color": 2, "points": [[368, 10], [372, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "c4915688a089dccc", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_47", "type": "LINE", "layer": "A-WINDOW", "start_point": [376, 10], "end_point": [380, 10], "color": 2, "points": [[376, 10], [380, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "7e79ff292795a623", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_48", "type": "LINE", "layer": "A-WINDOW", "start_point": [384, 10], "end_point": [388, 10], "color": 2, "points": [[384, 10], [388, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "c1849e5643f48c56", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_49", "type": "LINE", "layer": "A-WINDOW", "start_point": [392, 10], "end_point": [396, 10], "color": 2, "points": [[392, 10], [396, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "f8a9600c47cbd9b5", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_51", "type": "LINE", "layer": "A-WINDOW", "start_point": [408, 10], "end_point": [412, 10], "color": 2, "points": [[408, 10], [412, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "e1fb14ce01494207", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_52", "type": "LINE", "layer": "A-WINDOW", "start_point": [416, 10], "end_point": [420, 10], "color": 2, "points": [[416, 10], [420, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.369471, "dedup_context": "other_layer", "dedup_hash": "00e32a4cf14029b6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_53", "type": "LINE", "layer": "A-WINDOW", "start_point": [424, 10], "end_point": [428, 10], "color": 2, "points": [[424, 10], [428, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "a05580f6eface720", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_54", "type": "LINE", "layer": "A-WINDOW", "start_point": [432, 10], "end_point": [436, 10], "color": 2, "points": [[432, 10], [436, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "77e86f764f2b7ab9", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_55", "type": "LINE", "layer": "A-WINDOW", "start_point": [440, 10], "end_point": [444, 10], "color": 2, "points": [[440, 10], [444, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "79ae337faf7dc853", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_56", "type": "LINE", "layer": "A-WINDOW", "start_point": [448, 10], "end_point": [452, 10], "color": 2, "points": [[448, 10], [452, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "0034acfa40df0b13", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_57", "type": "LINE", "layer": "A-WINDOW", "start_point": [456, 10], "end_point": [460, 10], "color": 2, "points": [[456, 10], [460, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "1c4488e5d2e862b0", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_58", "type": "LINE", "layer": "A-WINDOW", "start_point": [464, 10], "end_point": [468, 10], "color": 2, "points": [[464, 10], [468, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "0e89aaedc85ef7d9", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_59", "type": "LINE", "layer": "A-WINDOW", "start_point": [472, 10], "end_point": [476, 10], "color": 2, "points": [[472, 10], [476, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "9ad2fba96b64a917", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_61", "type": "LINE", "layer": "A-WINDOW", "start_point": [488, 10], "end_point": [492, 10], "color": 2, "points": [[488, 10], [492, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "560acb0851950563", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_62", "type": "LINE", "layer": "A-WINDOW", "start_point": [496, 10], "end_point": [500, 10], "color": 2, "points": [[496, 10], [500, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "e2eb31faf5d6c61a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_63", "type": "LINE", "layer": "A-WINDOW", "start_point": [504, 10], "end_point": [508, 10], "color": 2, "points": [[504, 10], [508, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "26dd7776e7e20098", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_64", "type": "LINE", "layer": "A-WINDOW", "start_point": [512, 10], "end_point": [516, 10], "color": 2, "points": [[512, 10], [516, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "282d5923107e10bd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_65", "type": "LINE", "layer": "A-WINDOW", "start_point": [520, 10], "end_point": [524, 10], "color": 2, "points": [[520, 10], [524, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "6e8fa144323cd0d7", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_66", "type": "LINE", "layer": "A-WINDOW", "start_point": [528, 10], "end_point": [532, 10], "color": 2, "points": [[528, 10], [532, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "197158faf74e3044", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_67", "type": "LINE", "layer": "A-WINDOW", "start_point": [536, 10], "end_point": [540, 10], "color": 2, "points": [[536, 10], [540, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "07489037b6da27e6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_68", "type": "LINE", "layer": "A-WINDOW", "start_point": [544, 10], "end_point": [548, 10], "color": 2, "points": [[544, 10], [548, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "86d35108da790c9f", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_69", "type": "LINE", "layer": "A-WINDOW", "start_point": [552, 10], "end_point": [556, 10], "color": 2, "points": [[552, 10], [556, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "ca1759cffb5d2e0a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_71", "type": "LINE", "layer": "A-WINDOW", "start_point": [568, 10], "end_point": [572, 10], "color": 2, "points": [[568, 10], [572, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "49da4bf9f50470d9", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_72", "type": "LINE", "layer": "A-WINDOW", "start_point": [576, 10], "end_point": [580, 10], "color": 2, "points": [[576, 10], [580, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "ea556660793500b6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_73", "type": "LINE", "layer": "A-WINDOW", "start_point": [584, 10], "end_point": [588, 10], "color": 2, "points": [[584, 10], [588, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "3710015143364ae7", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_74", "type": "LINE", "layer": "A-WINDOW", "start_point": [592, 10], "end_point": [596, 10], "color": 2, "points": [[592, 10], [596, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "9b7048b54b716e5c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_75", "type": "LINE", "layer": "A-WINDOW", "start_point": [600, 10], "end_point": [604, 10], "color": 2, "points": [[600, 10], [604, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "b6125f759ae638d6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_76", "type": "LINE", "layer": "A-WINDOW", "start_point": [608, 10], "end_point": [612, 10], "color": 2, "points": [[608, 10], [612, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "e30bf256054306c6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_77", "type": "LINE", "layer": "A-WINDOW", "start_point": [616, 10], "end_point": [620, 10], "color": 2, "points": [[616, 10], [620, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "ad4e3dc9f40ce7df", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_78", "type": "LINE", "layer": "A-WINDOW", "start_point": [624, 10], "end_point": [628, 10], "color": 2, "points": [[624, 10], [628, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "117277ce4e1de4dd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_79", "type": "LINE", "layer": "A-WINDOW", "start_point": [632, 10], "end_point": [636, 10], "color": 2, "points": [[632, 10], [636, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "b5d4b128fd9a0388", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_81", "type": "LINE", "layer": "A-WINDOW", "start_point": [648, 10], "end_point": [652, 10], "color": 2, "points": [[648, 10], [652, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "cbee12d12a1b5330", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_82", "type": "LINE", "layer": "A-WINDOW", "start_point": [656, 10], "end_point": [660, 10], "color": 2, "points": [[656, 10], [660, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "722e62986ba7658e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_83", "type": "LINE", "layer": "A-WINDOW", "start_point": [664, 10], "end_point": [668, 10], "color": 2, "points": [[664, 10], [668, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "a7d136f12bace95a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_84", "type": "LINE", "layer": "A-WINDOW", "start_point": [672, 10], "end_point": [676, 10], "color": 2, "points": [[672, 10], [676, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "69bb8c57d8d3afba", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_85", "type": "LINE", "layer": "A-WINDOW", "start_point": [680, 10], "end_point": [684, 10], "color": 2, "points": [[680, 10], [684, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "5b616587229b6b73", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_86", "type": "LINE", "layer": "A-WINDOW", "start_point": [688, 10], "end_point": [692, 10], "color": 2, "points": [[688, 10], [692, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "112a0bbeb5154ec9", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3854992, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_87", "type": "LINE", "layer": "A-WINDOW", "start_point": [696, 10], "end_point": [700, 10], "color": 2, "points": [[696, 10], [700, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "07af986ff660c0de", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_88", "type": "LINE", "layer": "A-WINDOW", "start_point": [704, 10], "end_point": [708, 10], "color": 2, "points": [[704, 10], [708, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "ff9be9380933d6e7", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_89", "type": "LINE", "layer": "A-WINDOW", "start_point": [712, 10], "end_point": [716, 10], "color": 2, "points": [[712, 10], [716, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "8fa030c8bca8d13c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_91", "type": "LINE", "layer": "A-WINDOW", "start_point": [728, 10], "end_point": [732, 10], "color": 2, "points": [[728, 10], [732, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3704693, "dedup_context": "other_layer", "dedup_hash": "d1a518d882a5dbcd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_92", "type": "LINE", "layer": "A-WINDOW", "start_point": [736, 10], "end_point": [740, 10], "color": 2, "points": [[736, 10], [740, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "013f6b9b17b6eefd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_93", "type": "LINE", "layer": "A-WINDOW", "start_point": [744, 10], "end_point": [748, 10], "color": 2, "points": [[744, 10], [748, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "32d25bbedf44226c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_94", "type": "LINE", "layer": "A-WINDOW", "start_point": [752, 10], "end_point": [756, 10], "color": 2, "points": [[752, 10], [756, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "c5a522707a3a3cc5", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_95", "type": "LINE", "layer": "A-WINDOW", "start_point": [760, 10], "end_point": [764, 10], "color": 2, "points": [[760, 10], [764, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "ca24f883f7d40bfa", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_96", "type": "LINE", "layer": "A-WINDOW", "start_point": [768, 10], "end_point": [772, 10], "color": 2, "points": [[768, 10], [772, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fe5357297cdcb1d5", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_97", "type": "LINE", "layer": "A-WINDOW", "start_point": [776, 10], "end_point": [780, 10], "color": 2, "points": [[776, 10], [780, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fbac531057c4935a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_98", "type": "LINE", "layer": "A-WINDOW", "start_point": [784, 10], "end_point": [788, 10], "color": 2, "points": [[784, 10], [788, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "4426c5d6189da0cb", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_99", "type": "LINE", "layer": "A-WINDOW", "start_point": [792, 10], "end_point": [796, 10], "color": 2, "points": [[792, 10], [796, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "243c99030a5af5da", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_101", "type": "LINE", "layer": "A-WINDOW", "start_point": [808, 10], "end_point": [812, 10], "color": 2, "points": [[808, 10], [812, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "2e1cc847a6047005", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_102", "type": "LINE", "layer": "A-WINDOW", "start_point": [816, 10], "end_point": [820, 10], "color": 2, "points": [[816, 10], [820, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "b2b188f33f242743", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_103", "type": "LINE", "layer": "A-WINDOW", "start_point": [824, 10], "end_point": [828, 10], "color": 2, "points": [[824, 10], [828, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "de2c699ed0f29e89", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_104", "type": "LINE", "layer": "A-WINDOW", "start_point": [832, 10], "end_point": [836, 10], "color": 2, "points": [[832, 10], [836, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "d8244f6142a19b9e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_105", "type": "LINE", "layer": "A-WINDOW", "start_point": [840, 10], "end_point": [844, 10], "color": 2, "points": [[840, 10], [844, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fa6501e3c2ca8eb8", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_106", "type": "LINE", "layer": "A-WINDOW", "start_point": [848, 10], "end_point": [852, 10], "color": 2, "points": [[848, 10], [852, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "770b849c5b888d77", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_107", "type": "LINE", "layer": "A-WINDOW", "start_point": [856, 10], "end_point": [860, 10], "color": 2, "points": [[856, 10], [860, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "89f81fdc85ba4cdc", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_108", "type": "LINE", "layer": "A-WINDOW", "start_point": [864, 10], "end_point": [868, 10], "color": 2, "points": [[864, 10], [868, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "398549be390f6f4e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_109", "type": "LINE", "layer": "A-WINDOW", "start_point": [872, 10], "end_point": [876, 10], "color": 2, "points": [[872, 10], [876, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "08c02099233d1866", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_111", "type": "LINE", "layer": "A-WINDOW", "start_point": [888, 10], "end_point": [892, 10], "color": 2, "points": [[888, 10], [892, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "95b3abc986dde3e0", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_112", "type": "LINE", "layer": "A-WINDOW", "start_point": [896, 10], "end_point": [900, 10], "color": 2, "points": [[896, 10], [900, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "3e2dd86e68e453cd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_113", "type": "LINE", "layer": "A-WINDOW", "start_point": [904, 10], "end_point": [908, 10], "color": 2, "points": [[904, 10], [908, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "30b28912b2031e22", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_114", "type": "LINE", "layer": "A-WINDOW", "start_point": [912, 10], "end_point": [916, 10], "color": 2, "points": [[912, 10], [916, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fc546091f22a4a30", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_115", "type": "LINE", "layer": "A-WINDOW", "start_point": [920, 10], "end_point": [924, 10], "color": 2, "points": [[920, 10], [924, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fbe71042d8487d7e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_116", "type": "LINE", "layer": "A-WINDOW", "start_point": [928, 10], "end_point": [932, 10], "color": 2, "points": [[928, 10], [932, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fc0adf9bbde41b58", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_117", "type": "LINE", "layer": "A-WINDOW", "start_point": [936, 10], "end_point": [940, 10], "color": 2, "points": [[936, 10], [940, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "b03ac9c144b036cc", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_118", "type": "LINE", "layer": "A-WINDOW", "start_point": [944, 10], "end_point": [948, 10], "color": 2, "points": [[944, 10], [948, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "1dc58f820098df22", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_119", "type": "LINE", "layer": "A-WINDOW", "start_point": [952, 10], "end_point": [956, 10], "color": 2, "points": [[952, 10], [956, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "d8ed18e77426af5b", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_121", "type": "LINE", "layer": "A-WINDOW", "start_point": [968, 10], "end_point": [972, 10], "color": 2, "points": [[968, 10], [972, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "fa7f2edd74557d41", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_122", "type": "LINE", "layer": "A-WINDOW", "start_point": [976, 10], "end_point": [980, 10], "color": 2, "points": [[976, 10], [980, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "4b99ebb58c3d99c6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_123", "type": "LINE", "layer": "A-WINDOW", "start_point": [984, 10], "end_point": [988, 10], "color": 2, "points": [[984, 10], [988, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "723eeb216216d8d1", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_124", "type": "LINE", "layer": "A-WINDOW", "start_point": [992, 10], "end_point": [996, 10], "color": 2, "points": [[992, 10], [996, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "40fc2b6c1a1fa265", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_125", "type": "LINE", "layer": "A-WINDOW", "start_point": [1000, 10], "end_point": [1004, 10], "color": 2, "points": [[1000, 10], [1004, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "c417ba54621a4666", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_126", "type": "LINE", "layer": "A-WINDOW", "start_point": [1008, 10], "end_point": [1012, 10], "color": 2, "points": [[1008, 10], [1012, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "baef365c7dd6c8fb", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_127", "type": "LINE", "layer": "A-WINDOW", "start_point": [1016, 10], "end_point": [1020, 10], "color": 2, "points": [[1016, 10], [1020, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.37147, "dedup_context": "other_layer", "dedup_hash": "9edb1b02337c8578", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_128", "type": "LINE", "layer": "A-WINDOW", "start_point": [1024, 10], "end_point": [1028, 10], "color": 2, "points": [[1024, 10], [1028, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "fa43d4e1701c5d4e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3864963, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_129", "type": "LINE", "layer": "A-WINDOW", "start_point": [1032, 10], "end_point": [1036, 10], "color": 2, "points": [[1032, 10], [1036, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "069678497604e1e1", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_131", "type": "LINE", "layer": "A-WINDOW", "start_point": [1048, 10], "end_point": [1052, 10], "color": 2, "points": [[1048, 10], [1052, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "1c6947562e13d4ca", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_132", "type": "LINE", "layer": "A-WINDOW", "start_point": [1056, 10], "end_point": [1060, 10], "color": 2, "points": [[1056, 10], [1060, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "f260196f80f0b20c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_133", "type": "LINE", "layer": "A-WINDOW", "start_point": [1064, 10], "end_point": [1068, 10], "color": 2, "points": [[1064, 10], [1068, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "0e85ddb7b67214b2", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_134", "type": "LINE", "layer": "A-WINDOW", "start_point": [1072, 10], "end_point": [1076, 10], "color": 2, "points": [[1072, 10], [1076, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "64032e3e9a40b690", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_135", "type": "LINE", "layer": "A-WINDOW", "start_point": [1080, 10], "end_point": [1084, 10], "color": 2, "points": [[1080, 10], [1084, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "b7535f1750f861d6", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_136", "type": "LINE", "layer": "A-WINDOW", "start_point": [1088, 10], "end_point": [1092, 10], "color": 2, "points": [[1088, 10], [1092, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "b4c698dceeb49bec", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_137", "type": "LINE", "layer": "A-WINDOW", "start_point": [1096, 10], "end_point": [1100, 10], "color": 2, "points": [[1096, 10], [1100, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "519289e589ba5675", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_138", "type": "LINE", "layer": "A-WINDOW", "start_point": [1104, 10], "end_point": [1108, 10], "color": 2, "points": [[1104, 10], [1108, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "bd4024c3e43896c0", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_139", "type": "LINE", "layer": "A-WINDOW", "start_point": [1112, 10], "end_point": [1116, 10], "color": 2, "points": [[1112, 10], [1116, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "04c7ed888fea6ded", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_141", "type": "LINE", "layer": "A-WINDOW", "start_point": [1128, 10], "end_point": [1132, 10], "color": 2, "points": [[1128, 10], [1132, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "c6baa7b5e4861edb", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_142", "type": "LINE", "layer": "A-WINDOW", "start_point": [1136, 10], "end_point": [1140, 10], "color": 2, "points": [[1136, 10], [1140, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "123bc1ef75a2b90a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_143", "type": "LINE", "layer": "A-WINDOW", "start_point": [1144, 10], "end_point": [1148, 10], "color": 2, "points": [[1144, 10], [1148, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "9956f0abf2631a94", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_144", "type": "LINE", "layer": "A-WINDOW", "start_point": [1152, 10], "end_point": [1156, 10], "color": 2, "points": [[1152, 10], [1156, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "f8dca39a3ab7e487", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_145", "type": "LINE", "layer": "A-WINDOW", "start_point": [1160, 10], "end_point": [1164, 10], "color": 2, "points": [[1160, 10], [1164, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "5f1eea6e34ec805c", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_146", "type": "LINE", "layer": "A-WINDOW", "start_point": [1168, 10], "end_point": [1172, 10], "color": 2, "points": [[1168, 10], [1172, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "40857f43e112ba2a", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_147", "type": "LINE", "layer": "A-WINDOW", "start_point": [1176, 10], "end_point": [1180, 10], "color": 2, "points": [[1176, 10], [1180, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "f3dbd76896baabbd", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_148", "type": "LINE", "layer": "A-WINDOW", "start_point": [1184, 10], "end_point": [1188, 10], "color": 2, "points": [[1184, 10], [1188, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "2137bf5a42bb407e", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_149", "type": "LINE", "layer": "A-WINDOW", "start_point": [1192, 10], "end_point": [1196, 10], "color": 2, "points": [[1192, 10], [1196, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "a94aff30539e816b", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_151", "type": "LINE", "layer": "A-WINDOW", "start_point": [1208, 10], "end_point": [1212, 10], "color": 2, "points": [[1208, 10], [1212, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "c3cb23a314f06d91", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_152", "type": "LINE", "layer": "A-WINDOW", "start_point": [1216, 10], "end_point": [1220, 10], "color": 2, "points": [[1216, 10], [1220, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "61d53fd93292a5d7", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "window_153", "type": "LINE", "layer": "A-WINDOW", "start_point": [1224, 10], "end_point": [1228, 10], "color": 2, "points": [[1224, 10], [1228, 10]], "original_layer": "A-WINDOW", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3724697, "dedup_context": "other_layer", "dedup_hash": "1291e1eeafa62d92", "layer_processing_type": "simple_deduplication", "processed_layer": "A-WINDOW", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_0", "type": "LINE", "layer": "0", "start_point": [0, 20], "end_point": [2, 20], "color": 3, "points": [[0, 20], [2, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "3f125ff3767cc27b", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_1", "type": "LINE", "layer": "0", "start_point": [6, 20], "end_point": [8, 20], "color": 3, "points": [[6, 20], [8, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "1ee1ef231d5082ab", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_2", "type": "LINE", "layer": "0", "start_point": [12, 20], "end_point": [14, 20], "color": 3, "points": [[12, 20], [14, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "6c73f6e0408ddee9", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_3", "type": "LINE", "layer": "0", "start_point": [18, 20], "end_point": [20, 20], "color": 3, "points": [[18, 20], [20, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "86a6b6785ea1f97c", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_4", "type": "LINE", "layer": "0", "start_point": [24, 20], "end_point": [26, 20], "color": 3, "points": [[24, 20], [26, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "cf838656054f82c6", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_5", "type": "LINE", "layer": "0", "start_point": [30, 20], "end_point": [32, 20], "color": 3, "points": [[30, 20], [32, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "da400cea77e80e1a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_6", "type": "LINE", "layer": "0", "start_point": [36, 20], "end_point": [38, 20], "color": 3, "points": [[36, 20], [38, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "3265303d038fcb7a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_7", "type": "LINE", "layer": "0", "start_point": [42, 20], "end_point": [44, 20], "color": 3, "points": [[42, 20], [44, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "5c6a06270939ffae", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_9", "type": "LINE", "layer": "0", "start_point": [54, 20], "end_point": [56, 20], "color": 3, "points": [[54, 20], [56, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "8a303df4a4deeb6f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_10", "type": "LINE", "layer": "0", "start_point": [60, 20], "end_point": [62, 20], "color": 3, "points": [[60, 20], [62, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "54ccba978f0be96d", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_11", "type": "LINE", "layer": "0", "start_point": [66, 20], "end_point": [68, 20], "color": 3, "points": [[66, 20], [68, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "1e56ffb67710a846", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_12", "type": "LINE", "layer": "0", "start_point": [72, 20], "end_point": [74, 20], "color": 3, "points": [[72, 20], [74, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "13b683c892082e38", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_13", "type": "LINE", "layer": "0", "start_point": [78, 20], "end_point": [80, 20], "color": 3, "points": [[78, 20], [80, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "1563ced4adf5efc5", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_14", "type": "LINE", "layer": "0", "start_point": [84, 20], "end_point": [86, 20], "color": 3, "points": [[84, 20], [86, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "1f7693e1b3ecab1c", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3875012, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_15", "type": "LINE", "layer": "0", "start_point": [90, 20], "end_point": [92, 20], "color": 3, "points": [[90, 20], [92, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "a4e673b55b86d730", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_17", "type": "LINE", "layer": "0", "start_point": [102, 20], "end_point": [104, 20], "color": 3, "points": [[102, 20], [104, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "b98271bc641ee670", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_18", "type": "LINE", "layer": "0", "start_point": [108, 20], "end_point": [110, 20], "color": 3, "points": [[108, 20], [110, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3744667, "dedup_context": "other_layer", "dedup_hash": "c40c81a8204f5701", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_19", "type": "LINE", "layer": "0", "start_point": [114, 20], "end_point": [116, 20], "color": 3, "points": [[114, 20], [116, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "8b38becf4b421f10", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_20", "type": "LINE", "layer": "0", "start_point": [120, 20], "end_point": [122, 20], "color": 3, "points": [[120, 20], [122, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "03807b59e1c38bcd", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_21", "type": "LINE", "layer": "0", "start_point": [126, 20], "end_point": [128, 20], "color": 3, "points": [[126, 20], [128, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "fcf5b170e4c11d3b", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_22", "type": "LINE", "layer": "0", "start_point": [132, 20], "end_point": [134, 20], "color": 3, "points": [[132, 20], [134, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "a7fb03ca45f580a8", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_23", "type": "LINE", "layer": "0", "start_point": [138, 20], "end_point": [140, 20], "color": 3, "points": [[138, 20], [140, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "74f6e500b9e61c88", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_25", "type": "LINE", "layer": "0", "start_point": [150, 20], "end_point": [152, 20], "color": 3, "points": [[150, 20], [152, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "6468772c31cb0fc6", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_26", "type": "LINE", "layer": "0", "start_point": [156, 20], "end_point": [158, 20], "color": 3, "points": [[156, 20], [158, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "633b3b189d1edf8a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_27", "type": "LINE", "layer": "0", "start_point": [162, 20], "end_point": [164, 20], "color": 3, "points": [[162, 20], [164, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "d477aaa6dfee8741", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_28", "type": "LINE", "layer": "0", "start_point": [168, 20], "end_point": [170, 20], "color": 3, "points": [[168, 20], [170, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "4d551cfc05314d9f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_29", "type": "LINE", "layer": "0", "start_point": [174, 20], "end_point": [176, 20], "color": 3, "points": [[174, 20], [176, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "38a4e9c55ed410fa", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_30", "type": "LINE", "layer": "0", "start_point": [180, 20], "end_point": [182, 20], "color": 3, "points": [[180, 20], [182, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "aab58e7046208d1b", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_31", "type": "LINE", "layer": "0", "start_point": [186, 20], "end_point": [188, 20], "color": 3, "points": [[186, 20], [188, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "5810fb69e867415d", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_33", "type": "LINE", "layer": "0", "start_point": [198, 20], "end_point": [200, 20], "color": 3, "points": [[198, 20], [200, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "439d029ca0a5acb0", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_34", "type": "LINE", "layer": "0", "start_point": [204, 20], "end_point": [206, 20], "color": 3, "points": [[204, 20], [206, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "bef69d884886f6ae", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_35", "type": "LINE", "layer": "0", "start_point": [210, 20], "end_point": [212, 20], "color": 3, "points": [[210, 20], [212, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "5d50ff0ea9b703a4", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_36", "type": "LINE", "layer": "0", "start_point": [216, 20], "end_point": [218, 20], "color": 3, "points": [[216, 20], [218, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "12527037e9d5e713", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_37", "type": "LINE", "layer": "0", "start_point": [222, 20], "end_point": [224, 20], "color": 3, "points": [[222, 20], [224, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "e6da108d37c0c927", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_38", "type": "LINE", "layer": "0", "start_point": [228, 20], "end_point": [230, 20], "color": 3, "points": [[228, 20], [230, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "684f18cf86677c60", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_39", "type": "LINE", "layer": "0", "start_point": [234, 20], "end_point": [236, 20], "color": 3, "points": [[234, 20], [236, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "0734a075f8beb67f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_41", "type": "LINE", "layer": "0", "start_point": [246, 20], "end_point": [248, 20], "color": 3, "points": [[246, 20], [248, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "a012b6a7b713d3cf", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_42", "type": "LINE", "layer": "0", "start_point": [252, 20], "end_point": [254, 20], "color": 3, "points": [[252, 20], [254, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "c0a216ce33460ca7", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_43", "type": "LINE", "layer": "0", "start_point": [258, 20], "end_point": [260, 20], "color": 3, "points": [[258, 20], [260, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "a0766fc44db1bcfd", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_44", "type": "LINE", "layer": "0", "start_point": [264, 20], "end_point": [266, 20], "color": 3, "points": [[264, 20], [266, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "6c37936c10b29a69", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_45", "type": "LINE", "layer": "0", "start_point": [270, 20], "end_point": [272, 20], "color": 3, "points": [[270, 20], [272, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "df8ab02d2d01437c", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_46", "type": "LINE", "layer": "0", "start_point": [276, 20], "end_point": [278, 20], "color": 3, "points": [[276, 20], [278, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "999d9e517e2fcb3a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_47", "type": "LINE", "layer": "0", "start_point": [282, 20], "end_point": [284, 20], "color": 3, "points": [[282, 20], [284, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "c82d2a8c0213e9ca", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_49", "type": "LINE", "layer": "0", "start_point": [294, 20], "end_point": [296, 20], "color": 3, "points": [[294, 20], [296, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.375474, "dedup_context": "other_layer", "dedup_hash": "2134d1bd5d683815", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_50", "type": "LINE", "layer": "0", "start_point": [300, 20], "end_point": [302, 20], "color": 3, "points": [[300, 20], [302, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "27d3d348123be03e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_51", "type": "LINE", "layer": "0", "start_point": [306, 20], "end_point": [308, 20], "color": 3, "points": [[306, 20], [308, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "72bdb5e9e736f598", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_52", "type": "LINE", "layer": "0", "start_point": [312, 20], "end_point": [314, 20], "color": 3, "points": [[312, 20], [314, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "dbd2fe6fe620c50f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_53", "type": "LINE", "layer": "0", "start_point": [318, 20], "end_point": [320, 20], "color": 3, "points": [[318, 20], [320, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "ddfa76b22f4b2ee5", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_54", "type": "LINE", "layer": "0", "start_point": [324, 20], "end_point": [326, 20], "color": 3, "points": [[324, 20], [326, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "eadb802f7c6ecd3a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_55", "type": "LINE", "layer": "0", "start_point": [330, 20], "end_point": [332, 20], "color": 3, "points": [[330, 20], [332, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "f93fc852445cfd7b", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_57", "type": "LINE", "layer": "0", "start_point": [342, 20], "end_point": [344, 20], "color": 3, "points": [[342, 20], [344, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "85ad28428a1a8377", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.388458, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_58", "type": "LINE", "layer": "0", "start_point": [348, 20], "end_point": [350, 20], "color": 3, "points": [[348, 20], [350, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "9975236e16553acb", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_59", "type": "LINE", "layer": "0", "start_point": [354, 20], "end_point": [356, 20], "color": 3, "points": [[354, 20], [356, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "684ae1a89ab1c0d0", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_60", "type": "LINE", "layer": "0", "start_point": [360, 20], "end_point": [362, 20], "color": 3, "points": [[360, 20], [362, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "070a4b8960bbaf12", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_61", "type": "LINE", "layer": "0", "start_point": [366, 20], "end_point": [368, 20], "color": 3, "points": [[366, 20], [368, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "3438914ceba7a1d6", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_62", "type": "LINE", "layer": "0", "start_point": [372, 20], "end_point": [374, 20], "color": 3, "points": [[372, 20], [374, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "8523a29a31f711a8", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_63", "type": "LINE", "layer": "0", "start_point": [378, 20], "end_point": [380, 20], "color": 3, "points": [[378, 20], [380, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "862043a96960887c", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_65", "type": "LINE", "layer": "0", "start_point": [390, 20], "end_point": [392, 20], "color": 3, "points": [[390, 20], [392, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "949050fea990fe49", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_66", "type": "LINE", "layer": "0", "start_point": [396, 20], "end_point": [398, 20], "color": 3, "points": [[396, 20], [398, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "4321455df0d62d99", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_67", "type": "LINE", "layer": "0", "start_point": [402, 20], "end_point": [404, 20], "color": 3, "points": [[402, 20], [404, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "0cadf31be7fa8f2e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_68", "type": "LINE", "layer": "0", "start_point": [408, 20], "end_point": [410, 20], "color": 3, "points": [[408, 20], [410, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "d36f286bf823e32d", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_69", "type": "LINE", "layer": "0", "start_point": [414, 20], "end_point": [416, 20], "color": 3, "points": [[414, 20], [416, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "0debb84e12627e49", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_70", "type": "LINE", "layer": "0", "start_point": [420, 20], "end_point": [422, 20], "color": 3, "points": [[420, 20], [422, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "47774e7d832326c0", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_71", "type": "LINE", "layer": "0", "start_point": [426, 20], "end_point": [428, 20], "color": 3, "points": [[426, 20], [428, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "4a8df58536d630f9", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_73", "type": "LINE", "layer": "0", "start_point": [438, 20], "end_point": [440, 20], "color": 3, "points": [[438, 20], [440, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "ce25cbe7ae923d8e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_74", "type": "LINE", "layer": "0", "start_point": [444, 20], "end_point": [446, 20], "color": 3, "points": [[444, 20], [446, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "42da1e2343d108d4", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_75", "type": "LINE", "layer": "0", "start_point": [450, 20], "end_point": [452, 20], "color": 3, "points": [[450, 20], [452, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "182ea0d64f34871a", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_76", "type": "LINE", "layer": "0", "start_point": [456, 20], "end_point": [458, 20], "color": 3, "points": [[456, 20], [458, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "cf815191b9483be2", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_77", "type": "LINE", "layer": "0", "start_point": [462, 20], "end_point": [464, 20], "color": 3, "points": [[462, 20], [464, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "81d234fdc875b3c2", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_78", "type": "LINE", "layer": "0", "start_point": [468, 20], "end_point": [470, 20], "color": 3, "points": [[468, 20], [470, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "27ed898e557ce164", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_79", "type": "LINE", "layer": "0", "start_point": [474, 20], "end_point": [476, 20], "color": 3, "points": [[474, 20], [476, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3764653, "dedup_context": "other_layer", "dedup_hash": "d78abba8b4405712", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_81", "type": "LINE", "layer": "0", "start_point": [486, 20], "end_point": [488, 20], "color": 3, "points": [[486, 20], [488, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "41845d67360ae904", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_82", "type": "LINE", "layer": "0", "start_point": [492, 20], "end_point": [494, 20], "color": 3, "points": [[492, 20], [494, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "52d4871f20964bc9", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_83", "type": "LINE", "layer": "0", "start_point": [498, 20], "end_point": [500, 20], "color": 3, "points": [[498, 20], [500, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "d0dfde55b443556f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_84", "type": "LINE", "layer": "0", "start_point": [504, 20], "end_point": [506, 20], "color": 3, "points": [[504, 20], [506, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "0be9ad4dd70eefcc", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_85", "type": "LINE", "layer": "0", "start_point": [510, 20], "end_point": [512, 20], "color": 3, "points": [[510, 20], [512, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "b4b2b1a7cbb079e4", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_86", "type": "LINE", "layer": "0", "start_point": [516, 20], "end_point": [518, 20], "color": 3, "points": [[516, 20], [518, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "c03056768d73393d", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_87", "type": "LINE", "layer": "0", "start_point": [522, 20], "end_point": [524, 20], "color": 3, "points": [[522, 20], [524, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "46b4a953e6d4d1e8", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_89", "type": "LINE", "layer": "0", "start_point": [534, 20], "end_point": [536, 20], "color": 3, "points": [[534, 20], [536, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "802cbb3c34489935", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_90", "type": "LINE", "layer": "0", "start_point": [540, 20], "end_point": [542, 20], "color": 3, "points": [[540, 20], [542, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "d2b034e26614b760", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_91", "type": "LINE", "layer": "0", "start_point": [546, 20], "end_point": [548, 20], "color": 3, "points": [[546, 20], [548, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "f00f7ce143a40bad", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_92", "type": "LINE", "layer": "0", "start_point": [552, 20], "end_point": [554, 20], "color": 3, "points": [[552, 20], [554, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "5c9516f2bde8516e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_93", "type": "LINE", "layer": "0", "start_point": [558, 20], "end_point": [560, 20], "color": 3, "points": [[558, 20], [560, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "f625e64631bba90c", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_94", "type": "LINE", "layer": "0", "start_point": [564, 20], "end_point": [566, 20], "color": 3, "points": [[564, 20], [566, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "0c7242eb224340a5", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_95", "type": "LINE", "layer": "0", "start_point": [570, 20], "end_point": [572, 20], "color": 3, "points": [[570, 20], [572, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "1902abfb926c866d", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_97", "type": "LINE", "layer": "0", "start_point": [582, 20], "end_point": [584, 20], "color": 3, "points": [[582, 20], [584, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "3e48bf4eb0ad03de", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_98", "type": "LINE", "layer": "0", "start_point": [588, 20], "end_point": [590, 20], "color": 3, "points": [[588, 20], [590, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "9fd9dcfd8daa69b6", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_99", "type": "LINE", "layer": "0", "start_point": [594, 20], "end_point": [596, 20], "color": 3, "points": [[594, 20], [596, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "6e228c3d6813b171", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_100", "type": "LINE", "layer": "0", "start_point": [600, 20], "end_point": [602, 20], "color": 3, "points": [[600, 20], [602, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "d0ff58706f637f8b", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_101", "type": "LINE", "layer": "0", "start_point": [606, 20], "end_point": [608, 20], "color": 3, "points": [[606, 20], [608, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "481564101b7099cf", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.3895023, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_102", "type": "LINE", "layer": "0", "start_point": [612, 20], "end_point": [614, 20], "color": 3, "points": [[612, 20], [614, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "f3cec16d2b30c4d1", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_103", "type": "LINE", "layer": "0", "start_point": [618, 20], "end_point": [620, 20], "color": 3, "points": [[618, 20], [620, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "4265e7ae43c825d0", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_105", "type": "LINE", "layer": "0", "start_point": [630, 20], "end_point": [632, 20], "color": 3, "points": [[630, 20], [632, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "05a29a925bb8f4af", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_106", "type": "LINE", "layer": "0", "start_point": [636, 20], "end_point": [638, 20], "color": 3, "points": [[636, 20], [638, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "59f12bcffc156cc8", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_107", "type": "LINE", "layer": "0", "start_point": [642, 20], "end_point": [644, 20], "color": 3, "points": [[642, 20], [644, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "efa1ca085d58f477", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_108", "type": "LINE", "layer": "0", "start_point": [648, 20], "end_point": [650, 20], "color": 3, "points": [[648, 20], [650, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "e639e70dafd36e66", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_109", "type": "LINE", "layer": "0", "start_point": [654, 20], "end_point": [656, 20], "color": 3, "points": [[654, 20], [656, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "26a7829140ad369f", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_110", "type": "LINE", "layer": "0", "start_point": [660, 20], "end_point": [662, 20], "color": 3, "points": [[660, 20], [662, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "21b5ba8ceec3fb2e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_111", "type": "LINE", "layer": "0", "start_point": [666, 20], "end_point": [668, 20], "color": 3, "points": [[666, 20], [668, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3774643, "dedup_context": "other_layer", "dedup_hash": "5b269713422e2103", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_113", "type": "LINE", "layer": "0", "start_point": [678, 20], "end_point": [680, 20], "color": 3, "points": [[678, 20], [680, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "1e395b0ab87a64f3", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_114", "type": "LINE", "layer": "0", "start_point": [684, 20], "end_point": [686, 20], "color": 3, "points": [[684, 20], [686, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "50bf0c94eca064f5", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_115", "type": "LINE", "layer": "0", "start_point": [690, 20], "end_point": [692, 20], "color": 3, "points": [[690, 20], [692, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "352d4cd75d907276", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_116", "type": "LINE", "layer": "0", "start_point": [696, 20], "end_point": [698, 20], "color": 3, "points": [[696, 20], [698, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "4c60c2b7ada946c2", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_117", "type": "LINE", "layer": "0", "start_point": [702, 20], "end_point": [704, 20], "color": 3, "points": [[702, 20], [704, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "61d17f1e61d03ffe", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_118", "type": "LINE", "layer": "0", "start_point": [708, 20], "end_point": [710, 20], "color": 3, "points": [[708, 20], [710, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "807fba7f9949512e", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_119", "type": "LINE", "layer": "0", "start_point": [714, 20], "end_point": [716, 20], "color": 3, "points": [[714, 20], [716, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "5a3111deb71fe3da", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}, {"id": "layer0_121", "type": "LINE", "layer": "0", "start_point": [726, 20], "end_point": [728, 20], "color": 3, "points": [[726, 20], [728, 20]], "original_layer": "0", "processing_stage": "final_output", "processed_by": "OptimizedDeduplicator", "processing_type": "fast_deduplication", "dedup_timestamp": 1753722172.3784637, "dedup_context": "other_layer", "dedup_hash": "2284290bc4907505", "layer_processing_type": "simple_deduplication", "processed_layer": "0", "output_timestamp": 1753722172.390492, "processed_by_system": "IndependentLayerProcessor"}]}, "visualization_test": null, "data_operations_test": {"copy_time": 0.008002519607543945, "serialize_time": 0.0019958019256591797, "deserialize_time": 0.001039266586303711}, "bottleneck_analysis": {"processing": "fast", "data_copy": "fast", "main_bottlenecks": []}}
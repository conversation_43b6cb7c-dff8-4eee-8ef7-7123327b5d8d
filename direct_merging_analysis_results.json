{"test_time": 1753713044.4241645, "threshold_test_results": [{"threshold": 5, "processing_time": 0.0010285377502441406, "analysis": {"total_groups": 4, "group_details": [{"group_index": 0, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 1, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 2, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 3, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}], "cross_layer_groups": [], "layer_statistics": {}, "entity_distribution": {}}, "raw_groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "id": "wall_main"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "id": "wall_perpendicular"}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "id": "construction_overlay"}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "id": "door_opening"}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 0], "end_point": [0, -20], "color": 4, "points": [[0, 0], [0, -20]], "id": "railing_connection"}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}]}, {"threshold": 10, "processing_time": 0.0020012855529785156, "analysis": {"total_groups": 4, "group_details": [{"group_index": 0, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 1, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 2, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 3, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}], "cross_layer_groups": [], "layer_statistics": {}, "entity_distribution": {}}, "raw_groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "id": "wall_main"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "id": "wall_perpendicular"}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "id": "construction_overlay"}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "id": "door_opening"}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 0], "end_point": [0, -20], "color": 4, "points": [[0, 0], [0, -20]], "id": "railing_connection"}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}]}, {"threshold": 20, "processing_time": 0.0009999275207519531, "analysis": {"total_groups": 4, "group_details": [{"group_index": 0, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 1, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 2, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 3, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}], "cross_layer_groups": [], "layer_statistics": {}, "entity_distribution": {}}, "raw_groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "id": "wall_main"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "id": "wall_perpendicular"}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "id": "construction_overlay"}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "id": "door_opening"}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 0], "end_point": [0, -20], "color": 4, "points": [[0, 0], [0, -20]], "id": "railing_connection"}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}]}, {"threshold": 50, "processing_time": 0.0009984970092773438, "analysis": {"total_groups": 4, "group_details": [{"group_index": 0, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 1, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 2, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 3, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}], "cross_layer_groups": [], "layer_statistics": {}, "entity_distribution": {}}, "raw_groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "id": "wall_main"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "id": "wall_perpendicular"}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "id": "construction_overlay"}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "id": "door_opening"}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 0], "end_point": [0, -20], "color": 4, "points": [[0, 0], [0, -20]], "id": "railing_connection"}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}]}, {"threshold": 100, "processing_time": 0.001001119613647461, "analysis": {"total_groups": 4, "group_details": [{"group_index": 0, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 1, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 2, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}, {"group_index": 3, "entity_count": 6, "layers": [], "types": [], "entities": [], "is_cross_layer": false}], "cross_layer_groups": [], "layer_statistics": {}, "entity_distribution": {}}, "raw_groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "id": "wall_main"}, {"type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "id": "wall_perpendicular"}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "id": "construction_overlay"}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "id": "door_opening"}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-RAIL", "start_point": [0, 0], "end_point": [0, -20], "color": 4, "points": [[0, 0], [0, -20]], "id": "railing_connection"}], "label": "railing_A-RAIL_0", "group_type": "railing", "layer": "A-RAIL", "status": "pending", "confidence": 0.8}]}], "line_merger_results": {"original_lines": 5, "merged_lines": 3, "processing_time": 0.006032228469848633, "merger_stats": {"original_lines": 5, "merged_lines": 2, "final_lines": 3, "processing_time": 0.006032228469848633, "iterations_performed": 2, "iteration_details": [{"iteration": 1, "input_count": 5, "output_count": 3, "merged_count": 2}, {"iteration": 2, "input_count": 3, "output_count": 3, "merged_count": 0}]}, "potential_cross_layer_merging": true}, "summary": {"thresholds_tested": 5, "max_cross_layer_groups": 0, "min_cross_layer_groups": 0, "cross_layer_issue_detected": false, "line_merger_cross_layer_potential": true}}
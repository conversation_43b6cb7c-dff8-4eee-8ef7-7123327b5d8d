#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强CAD数据处理器
基于测试结果改进，集成图层感知线条合并和增强处理架构
"""

import copy
import time
from typing import List, Dict, Any, Optional

try:
    from cad_data_processor import CADDataProcessor
    CAD_PROCESSOR_AVAILABLE = True
except ImportError:
    CAD_PROCESSOR_AVAILABLE = False
    print("⚠️ 原始CAD数据处理器不可用")

try:
    from layer_aware_line_merger import LayerAwareLineMerger
    LAYER_AWARE_MERGER_AVAILABLE = True
except ImportError:
    LAYER_AWARE_MERGER_AVAILABLE = False
    print("⚠️ 图层感知线条合并器不可用")

try:
    from integrated_processing_manager import IntegratedProcessingManager
    INTEGRATED_MANAGER_AVAILABLE = True
except ImportError:
    INTEGRATED_MANAGER_AVAILABLE = False
    print("⚠️ 集成处理管理器不可用")


class EnhancedCADProcessor:
    """
    增强CAD数据处理器
    
    核心改进：
    1. 集成图层感知线条合并
    2. 严格的图层分离处理
    3. 可选的增强处理架构
    4. 完整的处理追踪和验证
    """
    
    def __init__(self, use_enhanced_architecture=False):
        """
        初始化增强CAD数据处理器
        
        Args:
            use_enhanced_architecture: 是否使用增强处理架构
        """
        self.use_enhanced_architecture = use_enhanced_architecture
        
        # 初始化组件
        if CAD_PROCESSOR_AVAILABLE:
            self.traditional_processor = CADDataProcessor()
        else:
            self.traditional_processor = None
        
        if LAYER_AWARE_MERGER_AVAILABLE:
            self.layer_aware_merger = LayerAwareLineMerger()
        else:
            self.layer_aware_merger = None
        
        if INTEGRATED_MANAGER_AVAILABLE and use_enhanced_architecture:
            self.integrated_manager = IntegratedProcessingManager()
        else:
            self.integrated_manager = None
        
        # 处理配置
        self.processing_config = {
            'enable_layer_aware_merging': True,
            'enable_cross_layer_validation': True,
            'preserve_layer_info': True,
            'layer_separation_priority': True,
            'validate_processing_integrity': True,
            'processing_mode': 'enhanced' if use_enhanced_architecture else 'traditional_improved'
        }
        
        # 处理统计
        self.processing_stats = {
            'total_files_processed': 0,
            'total_entities_processed': 0,
            'layer_separation_success_rate': 0.0,
            'cross_layer_issues_detected': 0,
            'processing_time_total': 0.0
        }
        
        print(f"🎯 增强CAD数据处理器初始化完成 (模式: {self.processing_config['processing_mode']})")
    
    def process_entities(self, entities: List[Dict[str, Any]], 
                        file_path: str = None, 
                        distance_threshold: float = 20) -> Dict[str, Any]:
        """
        处理实体的主要方法
        
        Args:
            entities: 输入实体列表
            file_path: 文件路径
            distance_threshold: 距离阈值
            
        Returns:
            处理结果字典
        """
        print(f"\n🚀 开始增强CAD数据处理: {len(entities)} 个实体")
        start_time = time.time()
        
        try:
            # 选择处理方式
            if self.use_enhanced_architecture and self.integrated_manager:
                result = self._process_with_enhanced_architecture(entities, file_path)
            else:
                result = self._process_with_improved_traditional(entities, distance_threshold)
            
            # 验证处理结果
            validation_result = self._validate_processing_result(entities, result)
            result['validation'] = validation_result
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_processing_stats(entities, result, processing_time)
            
            print(f"✅ 增强CAD数据处理完成，耗时 {processing_time:.3f} 秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 增强CAD数据处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 返回错误结果
            return {
                'success': False,
                'error': str(e),
                'entities': entities,
                'groups': [],
                'labeled_entities': [],
                'processing_mode': self.processing_config['processing_mode'],
                'processing_time': time.time() - start_time
            }
    
    def _process_with_enhanced_architecture(self, entities: List[Dict[str, Any]], 
                                          file_path: str = None) -> Dict[str, Any]:
        """使用增强处理架构"""
        print("   📈 使用增强处理架构")
        
        result = self.integrated_manager.process_file_entities(entities, file_path)
        
        # 转换为统一格式
        return {
            'success': result.get('success', True),
            'entities': result.get('entities', []),
            'groups': result.get('groups', []),
            'labeled_entities': result.get('labeled_entities', []),
            'auto_labeled_entities': result.get('labeled_entities', []),
            'processing_mode': 'enhanced_architecture',
            'enhanced_data': {
                'containers': result.get('containers', []),
                'layer_summary': result.get('layer_summary', {}),
                'integrity_check': result.get('integrity_check', {}),
                'processing_summary': result.get('processing_summary', {})
            }
        }
    
    def _process_with_improved_traditional(self, entities: List[Dict[str, Any]], 
                                         distance_threshold: float) -> Dict[str, Any]:
        """使用改进的传统处理方式"""
        print("   📊 使用改进的传统处理方式")
        
        # 阶段1: 图层感知线条合并
        if self.processing_config['enable_layer_aware_merging'] and self.layer_aware_merger:
            print("   🔧 执行图层感知线条合并...")
            merged_entities = self.layer_aware_merger.merge_entities_by_layer(entities)
            merge_stats = self.layer_aware_merger.get_merge_statistics()
        else:
            print("   ⚠️ 跳过线条合并（组件不可用）")
            merged_entities = entities
            merge_stats = {}
        
        # 阶段2: 实体分组
        if self.traditional_processor:
            print("   🔗 执行实体分组...")
            groups = self.traditional_processor.group_entities(
                merged_entities, 
                distance_threshold=distance_threshold, 
                debug=False
            )
        else:
            print("   ⚠️ 跳过实体分组（组件不可用）")
            groups = [merged_entities] if merged_entities else []
        
        # 阶段3: 自动标注
        labeled_entities = self._auto_label_entities(merged_entities, groups)
        
        return {
            'success': True,
            'entities': merged_entities,
            'groups': groups,
            'labeled_entities': labeled_entities,
            'auto_labeled_entities': labeled_entities,
            'processing_mode': 'traditional_improved',
            'merge_statistics': merge_stats,
            'total_entities': len(merged_entities),
            'total_groups': len(groups),
            'total_labeled': len(labeled_entities)
        }
    
    def _auto_label_entities(self, entities: List[Dict[str, Any]], 
                           groups: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """自动标注实体"""
        labeled = []
        
        for group in groups:
            for entity in group:
                if isinstance(entity, dict):
                    entity_copy = copy.deepcopy(entity)
                    
                    # 基于图层的自动标注
                    layer = entity_copy.get('layer', '').lower()
                    if 'wall' in layer or '墙' in layer:
                        entity_copy['label'] = 'wall'
                        entity_copy['confidence'] = 0.9
                    elif 'door' in layer or 'window' in layer or '门' in layer or '窗' in layer:
                        entity_copy['label'] = 'door_window'
                        entity_copy['confidence'] = 0.85
                    elif 'rail' in layer or '栏杆' in layer:
                        entity_copy['label'] = 'railing'
                        entity_copy['confidence'] = 0.8
                    elif 'text' in layer or '文字' in layer:
                        entity_copy['label'] = 'text'
                        entity_copy['confidence'] = 0.95
                    elif 'dim' in layer or '标注' in layer:
                        entity_copy['label'] = 'dimension'
                        entity_copy['confidence'] = 0.9
                    else:
                        entity_copy['label'] = 'other'
                        entity_copy['confidence'] = 0.5
                    
                    entity_copy['auto_labeled'] = True
                    entity_copy['labeled_by'] = 'EnhancedCADProcessor'
                    entity_copy['labeling_timestamp'] = time.time()
                    
                    labeled.append(entity_copy)
        
        return labeled
    
    def _validate_processing_result(self, original_entities: List[Dict[str, Any]], 
                                  result: Dict[str, Any]) -> Dict[str, Any]:
        """验证处理结果"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'layer_integrity': {},
            'cross_layer_check': {}
        }
        
        if not self.processing_config['enable_cross_layer_validation']:
            validation['cross_layer_check']['skipped'] = True
            return validation
        
        try:
            # 检查图层完整性
            original_layers = set()
            processed_layers = set()
            
            for entity in original_entities:
                if isinstance(entity, dict) and 'layer' in entity:
                    original_layers.add(entity['layer'])
            
            processed_entities = result.get('entities', [])
            for entity in processed_entities:
                if isinstance(entity, dict) and 'layer' in entity:
                    processed_layers.add(entity['layer'])
            
            # 验证图层保留
            missing_layers = original_layers - processed_layers
            if missing_layers:
                validation['errors'].append(f"丢失图层: {missing_layers}")
                validation['is_valid'] = False
            
            validation['layer_integrity'] = {
                'original_layers': len(original_layers),
                'processed_layers': len(processed_layers),
                'missing_layers': list(missing_layers),
                'preservation_rate': len(processed_layers) / len(original_layers) if original_layers else 1.0
            }
            
            # 检查跨图层合并
            cross_layer_groups = 0
            groups = result.get('groups', [])
            
            for group in groups:
                if isinstance(group, list):
                    group_layers = set()
                    for entity in group:
                        if isinstance(entity, dict) and 'layer' in entity:
                            group_layers.add(entity['layer'])
                    
                    if len(group_layers) > 1:
                        cross_layer_groups += 1
            
            validation['cross_layer_check'] = {
                'total_groups': len(groups),
                'cross_layer_groups': cross_layer_groups,
                'cross_layer_rate': cross_layer_groups / len(groups) if groups else 0.0,
                'is_clean': cross_layer_groups == 0
            }
            
            if cross_layer_groups > 0:
                validation['warnings'].append(f"发现 {cross_layer_groups} 个跨图层组")
            
        except Exception as e:
            validation['errors'].append(f"验证过程中发生错误: {str(e)}")
            validation['is_valid'] = False
        
        return validation
    
    def _update_processing_stats(self, original_entities: List[Dict[str, Any]], 
                               result: Dict[str, Any], processing_time: float):
        """更新处理统计"""
        self.processing_stats['total_files_processed'] += 1
        self.processing_stats['total_entities_processed'] += len(original_entities)
        self.processing_stats['processing_time_total'] += processing_time
        
        # 更新图层分离成功率
        validation = result.get('validation', {})
        layer_integrity = validation.get('layer_integrity', {})
        preservation_rate = layer_integrity.get('preservation_rate', 1.0)
        
        # 计算累积平均值
        total_files = self.processing_stats['total_files_processed']
        current_rate = self.processing_stats['layer_separation_success_rate']
        self.processing_stats['layer_separation_success_rate'] = (
            (current_rate * (total_files - 1) + preservation_rate) / total_files
        )
        
        # 更新跨图层问题计数
        cross_layer_check = validation.get('cross_layer_check', {})
        if cross_layer_check.get('cross_layer_groups', 0) > 0:
            self.processing_stats['cross_layer_issues_detected'] += 1
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        summary = {
            'processing_config': self.processing_config,
            'processing_stats': self.processing_stats,
            'component_status': {
                'traditional_processor': CAD_PROCESSOR_AVAILABLE,
                'layer_aware_merger': LAYER_AWARE_MERGER_AVAILABLE,
                'integrated_manager': INTEGRATED_MANAGER_AVAILABLE
            }
        }
        
        if self.layer_aware_merger:
            summary['merge_statistics'] = self.layer_aware_merger.get_merge_statistics()
        
        return summary
    
    def set_processing_config(self, config: Dict[str, Any]):
        """设置处理配置"""
        self.processing_config.update(config)
        print(f"🔧 处理配置已更新: {config}")


# 工厂函数
def create_enhanced_processor(use_enhanced_architecture=False) -> EnhancedCADProcessor:
    """创建增强CAD数据处理器"""
    return EnhancedCADProcessor(use_enhanced_architecture)


def create_traditional_improved_processor() -> EnhancedCADProcessor:
    """创建改进的传统处理器"""
    return EnhancedCADProcessor(use_enhanced_architecture=False)


def create_full_enhanced_processor() -> EnhancedCADProcessor:
    """创建完全增强处理器"""
    return EnhancedCADProcessor(use_enhanced_architecture=True)

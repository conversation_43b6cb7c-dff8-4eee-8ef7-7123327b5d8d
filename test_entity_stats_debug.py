#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实体统计调试功能测试脚本

这个脚本演示了新增的实体统计调试功能的使用方法。
"""

import sys
import os
import tkinter as tk

def test_entity_stats_debug():
    """测试实体统计调试功能"""
    
    print("🔍 实体统计调试功能测试")
    print("=" * 50)
    
    try:
        # 导入应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 成功导入应用模块")
        
        # 创建应用实例
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("🔧 创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("✅ 应用实例创建成功")
        
        # 测试1: 手动触发统计
        print("\n📊 测试1: 手动触发统计")
        app._debug_entity_stats("手动测试触发")
        
        # 测试2: 模拟实体数据变化
        print("\n📊 测试2: 模拟实体数据变化")
        
        # 模拟添加一些实体数据
        if hasattr(app, 'processor') and app.processor:
            # 创建模拟实体数据
            mock_entities = [
                {'type': 'LINE', 'label': None, 'auto_labeled': False},
                {'type': 'LINE', 'label': None, 'auto_labeled': False},
                {'type': 'CIRCLE', 'label': None, 'auto_labeled': False},
                {'type': 'ARC', 'label': 'wall', 'auto_labeled': False},
                {'type': 'ARC', 'label': 'wall', 'auto_labeled': False},
            ]
            
            # 设置模拟数据
            app.processor.current_file_entities = mock_entities
            print("🔧 设置模拟实体数据")
            
            # 触发统计
            app._debug_entity_stats("模拟数据加载")
            
            # 模拟标注操作
            print("\n📊 测试3: 模拟标注操作")
            
            # 模拟手动标注
            mock_entities[0]['label'] = 'door_window'
            mock_entities[1]['label'] = 'door_window'
            
            # 添加到已标注列表
            if not hasattr(app.processor, 'labeled_entities'):
                app.processor.labeled_entities = []
            app.processor.labeled_entities.extend([mock_entities[0], mock_entities[1]])
            
            app._debug_entity_stats("模拟手动标注door_window")
            
            # 模拟自动标注
            print("\n📊 测试4: 模拟自动标注")
            mock_entities[2]['label'] = 'furniture'
            mock_entities[2]['auto_labeled'] = True
            
            if not hasattr(app.processor, 'auto_labeled_entities'):
                app.processor.auto_labeled_entities = []
            app.processor.auto_labeled_entities.append(mock_entities[2])
            
            app._debug_entity_stats("模拟自动标注furniture")
            
        # 测试5: 测试变化检测
        print("\n📊 测试5: 测试变化检测")
        app._debug_entity_stats("重复调用测试变化检测")
        
        # 清理
        root.destroy()
        print("\n✅ 测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_debug_toggle():
    """测试调试功能的开启/关闭"""
    
    print("\n🔧 测试调试功能开关")
    print("=" * 30)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 测试关闭调试
        print("🔇 关闭调试功能")
        app.entity_stats_debug = False
        app._debug_entity_stats("调试已关闭测试")
        
        # 测试开启调试
        print("\n🔊 开启调试功能")
        app.entity_stats_debug = True
        app._debug_entity_stats("调试已开启测试")
        
        root.destroy()
        print("✅ 开关测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 开关测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🚀 CAD分类标注工具 - 实体统计调试功能测试")
    print("=" * 60)
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 检查必要文件
    required_files = ['main_enhanced_with_v2_fill.py', 'main_enhanced.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    print("✅ 必要文件检查通过")
    
    # 运行测试
    test_results = []
    
    print("\n" + "="*60)
    test_results.append(test_entity_stats_debug())
    
    print("\n" + "="*60)
    test_results.append(test_debug_toggle())
    
    # 总结
    print("\n" + "="*60)
    print("📋 测试结果总结:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！实体统计调试功能工作正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

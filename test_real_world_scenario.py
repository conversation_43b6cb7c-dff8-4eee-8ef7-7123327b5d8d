#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实世界场景
模拟用户实际使用时的完整流程，包括可视化更新
"""

import os
import sys
import time
import threading
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from independent_layer_processor import IndependentLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    print("✅ 处理模块导入成功")
except ImportError as e:
    print(f"❌ 处理模块导入失败: {e}")
    sys.exit(1)

# 尝试导入可视化相关模块
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
    print("✅ Matplotlib导入成功")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib不可用，将模拟可视化操作")


class MockVisualizer:
    """模拟可视化器，测试可视化更新的性能影响"""
    
    def __init__(self):
        """初始化模拟可视化器"""
        self.draw_calls = 0
        self.overview_calls = 0
        self.clear_calls = 0
        self.update_calls = 0
        
        if MATPLOTLIB_AVAILABLE:
            self.fig, (self.ax_detail, self.ax_overview) = plt.subplots(1, 2, figsize=(12, 6))
            self.canvas = None
        else:
            self.ax_detail = MockAxis()
            self.ax_overview = MockAxis()
    
    def clear_all(self):
        """清除所有绘图"""
        self.clear_calls += 1
        time.sleep(0.001)  # 模拟清除操作的时间
        
        if MATPLOTLIB_AVAILABLE:
            self.ax_detail.clear()
            self.ax_overview.clear()
    
    def draw_entities(self, entities):
        """绘制实体列表（模拟真实可视化器的行为）"""
        self.draw_calls += 1
        
        if not entities:
            print("⚠️ 没有实体数据需要绘制")
            return
        
        print(f"🎨 绘制实体列表，共 {len(entities)} 个实体")
        
        # 模拟真实绘制的时间开销
        start_time = time.time()
        
        if MATPLOTLIB_AVAILABLE:
            self.ax_detail.clear()
            self.ax_detail.set_aspect('equal')
            self.ax_detail.grid(True, linestyle='--', alpha=0.3)
            
            # 模拟逐个绘制实体
            for i, entity in enumerate(entities):
                try:
                    # 模拟获取坐标和绘制
                    if entity.get('type') == 'LINE' and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            x = [p[0] for p in points[:2]]
                            y = [p[1] for p in points[:2]]
                            color = 'blue' if entity.get('layer') == 'A-WALL' else 'red'
                            self.ax_detail.plot(x, y, color=color, linewidth=1.0, alpha=0.8)
                    
                    # 模拟每个实体的绘制时间
                    if i % 50 == 0:  # 每50个实体暂停一下，模拟matplotlib的批处理
                        time.sleep(0.001)
                        
                except Exception as e:
                    print(f"绘制实体 {i+1} 失败: {e}")
        else:
            # 模拟绘制时间
            time.sleep(0.001 * len(entities))
        
        draw_time = time.time() - start_time
        print(f"✅ 实体列表绘制完成，耗时: {draw_time:.3f} 秒")
    
    def visualize_overview(self, all_entities, current_group_entities=None, 
                         labeled_entities=None, processor=None, **kwargs):
        """可视化全图概览（模拟真实可视化器的行为）"""
        self.overview_calls += 1
        
        print(f"🌍 可视化器：开始绘制全图概览")
        print(f"  - 总实体数: {len(all_entities) if all_entities else 0}")
        print(f"  - 当前组实体数: {len(current_group_entities) if current_group_entities else 0}")
        print(f"  - 已标注实体数: {len(labeled_entities) if labeled_entities else 0}")
        
        if not all_entities:
            print("  ⚠️ 没有实体数据，跳过绘制")
            return
        
        start_time = time.time()
        
        if MATPLOTLIB_AVAILABLE:
            self.ax_overview.clear()
            self.ax_overview.set_aspect('equal')
            self.ax_overview.grid(True, linestyle='--', alpha=0.3)
            
            # 模拟真实的组查找和绘制过程
            all_groups = getattr(processor, 'all_groups', []) if processor else []
            
            for i, entity in enumerate(all_entities):
                try:
                    # 模拟 _find_entity_group_index 的开销
                    if all_groups:
                        for group_index, group in enumerate(all_groups):
                            # 模拟组查找的时间开销
                            time.sleep(0.00001)  # 每次查找0.01毫秒
                            break
                    
                    # 模拟绘制
                    if entity.get('type') == 'LINE' and 'points' in entity:
                        points = entity['points']
                        if len(points) >= 2:
                            x = [p[0] for p in points[:2]]
                            y = [p[1] for p in points[:2]]
                            
                            # 根据实体状态选择颜色
                            if current_group_entities and entity in current_group_entities:
                                color = 'red'
                            elif labeled_entities and entity in labeled_entities:
                                color = 'green'
                            else:
                                color = 'lightgray'
                            
                            self.ax_overview.plot(x, y, color=color, linewidth=0.5, alpha=0.7)
                    
                    # 模拟批处理
                    if i % 100 == 0:
                        time.sleep(0.001)
                        
                except Exception as e:
                    print(f"概览绘制实体 {i+1} 失败: {e}")
        else:
            # 模拟概览绘制时间（包括组查找开销）
            time.sleep(0.00001 * len(all_entities) * 5)  # 模拟组查找的O(n*m)开销
            time.sleep(0.001 * len(all_entities))  # 模拟绘制时间
        
        overview_time = time.time() - start_time
        print(f"✅ 全图概览绘制完成，耗时: {overview_time:.3f} 秒")
    
    def update_canvas(self):
        """更新画布"""
        self.update_calls += 1
        
        if MATPLOTLIB_AVAILABLE and self.canvas:
            start_time = time.time()
            self.canvas.draw()
            update_time = time.time() - start_time
            print(f"🖼️ 画布更新完成，耗时: {update_time:.3f} 秒")
        else:
            time.sleep(0.005)  # 模拟画布更新时间
            print(f"🖼️ 画布更新完成（模拟）")
    
    def get_stats(self):
        """获取统计信息"""
        return {
            'draw_calls': self.draw_calls,
            'overview_calls': self.overview_calls,
            'clear_calls': self.clear_calls,
            'update_calls': self.update_calls
        }


class MockAxis:
    """模拟matplotlib轴对象"""
    
    def clear(self):
        pass
    
    def set_aspect(self, aspect):
        pass
    
    def grid(self, *args, **kwargs):
        pass
    
    def plot(self, x, y, *args, **kwargs):
        pass


def create_realistic_test_data():
    """创建真实的测试数据"""
    entities = []
    
    # A-WALL图层（208个实体）
    for i in range(208):
        entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5.0, 0.0],
            'end_point': [i * 5.0 + 3.0, 0.0],
            'color': 1,
            'points': [[i * 5.0, 0.0], [i * 5.0 + 3.0, 0.0]]
        })
    
    # A-WINDOW图层（154个实体，包含重复）
    for i in range(154):
        if i % 10 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'window_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [base_index * 8.0, 10.0],
                'end_point': [base_index * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[base_index * 8.0, 10.0], [base_index * 8.0 + 4.0, 10.0]]
            })
        else:
            entities.append({
                'id': f'window_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 8.0, 10.0],
                'end_point': [i * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[i * 8.0, 10.0], [i * 8.0 + 4.0, 10.0]]
            })
    
    # 0图层（122个实体，包含重复）
    for i in range(122):
        if i % 8 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'layer0_{i}_duplicate',
                'type': 'LINE',
                'layer': '0',
                'start_point': [base_index * 6.0, 20.0],
                'end_point': [base_index * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[base_index * 6.0, 20.0], [base_index * 6.0 + 2.0, 20.0]]
            })
        else:
            entities.append({
                'id': f'layer0_{i}',
                'type': 'LINE',
                'layer': '0',
                'start_point': [i * 6.0, 20.0],
                'end_point': [i * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[i * 6.0, 20.0], [i * 6.0 + 2.0, 20.0]]
            })
    
    return entities


def test_real_world_line_processing():
    """测试真实世界的线条处理场景"""
    print("🧪 测试真实世界线条处理场景")
    print("=" * 80)
    
    # 步骤1: 创建测试数据
    print("📊 步骤1: 创建测试数据")
    step1_start = time.time()
    test_entities = create_realistic_test_data()
    step1_time = time.time() - step1_start
    print(f"   ✅ 数据创建完成: {step1_time:.3f} 秒，{len(test_entities)} 个实体")
    
    # 步骤2: 创建可视化器
    print("📊 步骤2: 创建可视化器")
    step2_start = time.time()
    visualizer = MockVisualizer()
    step2_time = time.time() - step2_start
    print(f"   ✅ 可视化器创建完成: {step2_time:.3f} 秒")
    
    # 步骤3: 初始可视化（显示原始数据）
    print("📊 步骤3: 初始可视化（显示原始数据）")
    step3_start = time.time()
    visualizer.clear_all()
    visualizer.draw_entities(test_entities)
    visualizer.visualize_overview(test_entities)
    visualizer.update_canvas()
    step3_time = time.time() - step3_start
    print(f"   ✅ 初始可视化完成: {step3_time:.3f} 秒")
    
    # 步骤4: 创建处理器
    print("📊 步骤4: 创建线条处理器")
    step4_start = time.time()
    mode_manager = LineProcessingModeManager()
    mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    step4_time = time.time() - step4_start
    print(f"   ✅ 处理器创建完成: {step4_time:.3f} 秒")
    
    # 步骤5: 执行线条处理
    print("📊 步骤5: 执行线条处理")
    step5_start = time.time()
    result = mode_manager.process_entities(test_entities)
    processed_entities = result.get('entities', [])
    step5_time = time.time() - step5_start
    print(f"   ✅ 线条处理完成: {step5_time:.3f} 秒，{len(processed_entities)} 个实体")
    
    # 步骤6: 更新可视化（显示处理后数据）
    print("📊 步骤6: 更新可视化（显示处理后数据）")
    step6_start = time.time()
    visualizer.clear_all()
    visualizer.draw_entities(processed_entities)
    visualizer.visualize_overview(processed_entities)
    visualizer.update_canvas()
    step6_time = time.time() - step6_start
    print(f"   ✅ 可视化更新完成: {step6_time:.3f} 秒")
    
    # 步骤7: 模拟用户交互（多次可视化更新）
    print("📊 步骤7: 模拟用户交互（多次可视化更新）")
    step7_start = time.time()
    
    # 模拟用户点击、选择等操作导致的多次可视化更新
    for i in range(3):
        print(f"   🖱️ 模拟用户交互 {i+1}/3")
        visualizer.clear_all()
        
        # 模拟选择不同的当前组
        current_group = processed_entities[i*50:(i+1)*50] if i*50 < len(processed_entities) else []
        labeled_entities = processed_entities[:i*20] if i*20 < len(processed_entities) else []
        
        visualizer.draw_entities(processed_entities)
        visualizer.visualize_overview(processed_entities, current_group, labeled_entities)
        visualizer.update_canvas()
        
        time.sleep(0.01)  # 模拟用户操作间隔
    
    step7_time = time.time() - step7_start
    print(f"   ✅ 用户交互模拟完成: {step7_time:.3f} 秒")
    
    # 总结
    total_time = step1_time + step2_time + step3_time + step4_time + step5_time + step6_time + step7_time
    
    print(f"\n📈 真实世界场景时间分解:")
    print(f"   步骤1 - 数据创建: {step1_time:.3f} 秒 ({step1_time/total_time*100:.1f}%)")
    print(f"   步骤2 - 可视化器创建: {step2_time:.3f} 秒 ({step2_time/total_time*100:.1f}%)")
    print(f"   步骤3 - 初始可视化: {step3_time:.3f} 秒 ({step3_time/total_time*100:.1f}%)")
    print(f"   步骤4 - 处理器创建: {step4_time:.3f} 秒 ({step4_time/total_time*100:.1f}%)")
    print(f"   步骤5 - 线条处理: {step5_time:.3f} 秒 ({step5_time/total_time*100:.1f}%)")
    print(f"   步骤6 - 可视化更新: {step6_time:.3f} 秒 ({step6_time/total_time*100:.1f}%)")
    print(f"   步骤7 - 用户交互: {step7_time:.3f} 秒 ({step7_time/total_time*100:.1f}%)")
    print(f"   总时间: {total_time:.3f} 秒")
    
    # 可视化统计
    viz_stats = visualizer.get_stats()
    print(f"\n📊 可视化调用统计:")
    print(f"   绘制调用: {viz_stats['draw_calls']} 次")
    print(f"   概览调用: {viz_stats['overview_calls']} 次")
    print(f"   清除调用: {viz_stats['clear_calls']} 次")
    print(f"   更新调用: {viz_stats['update_calls']} 次")
    
    # 识别瓶颈
    steps = [
        ('数据创建', step1_time),
        ('可视化器创建', step2_time),
        ('初始可视化', step3_time),
        ('处理器创建', step4_time),
        ('线条处理', step5_time),
        ('可视化更新', step6_time),
        ('用户交互', step7_time)
    ]
    
    slowest_step = max(steps, key=lambda x: x[1])
    print(f"\n🚨 性能瓶颈识别:")
    print(f"   最慢步骤: {slowest_step[0]} ({slowest_step[1]:.3f} 秒)")
    
    # 可视化相关时间
    visualization_time = step3_time + step6_time + step7_time
    processing_time = step5_time
    
    print(f"\n📊 时间分类:")
    print(f"   纯处理时间: {processing_time:.3f} 秒 ({processing_time/total_time*100:.1f}%)")
    print(f"   可视化时间: {visualization_time:.3f} 秒 ({visualization_time/total_time*100:.1f}%)")
    print(f"   其他时间: {total_time - processing_time - visualization_time:.3f} 秒")
    
    if visualization_time > processing_time * 2:
        print(f"   ⚠️ 可视化时间是处理时间的 {visualization_time/processing_time:.1f} 倍")
        print(f"   🎯 建议优化可视化性能")
    
    return {
        'total_time': total_time,
        'processing_time': processing_time,
        'visualization_time': visualization_time,
        'bottleneck': slowest_step,
        'viz_stats': viz_stats
    }


def run_real_world_test():
    """运行真实世界测试"""
    print("🚀 开始真实世界线条处理测试")
    print("=" * 100)
    
    overall_start = time.time()
    
    try:
        # 执行真实世界测试
        results = test_real_world_line_processing()
        
        overall_time = time.time() - overall_start
        
        print(f"\n🎉 真实世界线条处理测试完成")
        print("=" * 100)
        print(f"   总测试时间: {overall_time:.2f} 秒")
        
        # 最终分析
        print(f"\n📊 最终性能分析:")
        print(f"   处理效率: {results['processing_time']:.3f} 秒")
        print(f"   可视化开销: {results['visualization_time']:.3f} 秒")
        print(f"   可视化/处理比: {results['visualization_time']/results['processing_time']:.1f}:1")
        
        if results['visualization_time'] > 0.5:
            print(f"   ❌ 可视化时间过长: {results['visualization_time']:.3f} 秒")
            print(f"   🎯 这可能是用户感受到等待时间过长的原因")
        elif results['processing_time'] > 0.5:
            print(f"   ❌ 处理时间过长: {results['processing_time']:.3f} 秒")
        else:
            print(f"   ✅ 整体性能良好")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_real_world_test()
    
    if success:
        print(f"\n🎊 真实世界测试成功！")
        print(f"   已识别出用户体验中的性能瓶颈")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)

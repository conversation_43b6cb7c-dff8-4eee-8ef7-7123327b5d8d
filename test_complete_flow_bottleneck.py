#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整流程瓶颈
从点击线条处理到数据输出的每个环节进行详细测试
"""

import os
import sys
import time
import json
import copy
import gc
from typing import List, Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from independent_layer_processor import IndependentLayerProcessor
    from line_processing_modes import LineProcessingModeManager, LineProcessingMode
    print("✅ 处理模块导入成功")
except ImportError as e:
    print(f"❌ 处理模块导入失败: {e}")
    sys.exit(1)


def create_test_data():
    """创建测试数据"""
    entities = []
    
    # A-WALL图层（208个实体）
    for i in range(208):
        entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5.0, 0.0],
            'end_point': [i * 5.0 + 3.0, 0.0],
            'color': 1,
            'points': [[i * 5.0, 0.0], [i * 5.0 + 3.0, 0.0]],
            'linetype': 'CONTINUOUS',
            'lineweight': 0.25,
            'handle': f'wall_handle_{i}',
            'owner': 'MODEL_SPACE'
        })
    
    # A-WINDOW图层（154个实体，包含重复）
    for i in range(154):
        if i % 10 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'window_{i}_duplicate',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [base_index * 8.0, 10.0],
                'end_point': [base_index * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[base_index * 8.0, 10.0], [base_index * 8.0 + 4.0, 10.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.18,
                'handle': f'window_handle_{i}_dup',
                'owner': 'MODEL_SPACE'
            })
        else:
            entities.append({
                'id': f'window_{i}',
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start_point': [i * 8.0, 10.0],
                'end_point': [i * 8.0 + 4.0, 10.0],
                'color': 2,
                'points': [[i * 8.0, 10.0], [i * 8.0 + 4.0, 10.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.18,
                'handle': f'window_handle_{i}',
                'owner': 'MODEL_SPACE'
            })
    
    # 0图层（122个实体，包含重复）
    for i in range(122):
        if i % 8 == 0 and i > 0:
            # 重复实体
            base_index = i - 1
            entities.append({
                'id': f'layer0_{i}_duplicate',
                'type': 'LINE',
                'layer': '0',
                'start_point': [base_index * 6.0, 20.0],
                'end_point': [base_index * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[base_index * 6.0, 20.0], [base_index * 6.0 + 2.0, 20.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.13,
                'handle': f'layer0_handle_{i}_dup',
                'owner': 'MODEL_SPACE'
            })
        else:
            entities.append({
                'id': f'layer0_{i}',
                'type': 'LINE',
                'layer': '0',
                'start_point': [i * 6.0, 20.0],
                'end_point': [i * 6.0 + 2.0, 20.0],
                'color': 3,
                'points': [[i * 6.0, 20.0], [i * 6.0 + 2.0, 20.0]],
                'linetype': 'CONTINUOUS',
                'lineweight': 0.13,
                'handle': f'layer0_handle_{i}',
                'owner': 'MODEL_SPACE'
            })
    
    return entities


def test_data_validation():
    """测试数据验证"""
    print("🧪 测试1: 数据验证")
    print("=" * 60)
    
    start_time = time.time()
    
    # 创建测试数据
    test_entities = create_test_data()
    creation_time = time.time() - start_time
    
    print(f"📊 数据创建时间: {creation_time:.3f} 秒")
    print(f"📊 实体总数: {len(test_entities)}")
    
    # 验证数据结构
    validation_start = time.time()
    
    valid_entities = 0
    invalid_entities = 0
    missing_fields = []
    
    required_fields = ['id', 'type', 'layer', 'points']
    
    for i, entity in enumerate(test_entities):
        if isinstance(entity, dict):
            valid = True
            for field in required_fields:
                if field not in entity:
                    missing_fields.append(f"实体{i}: 缺少字段 {field}")
                    valid = False
            
            if valid:
                valid_entities += 1
            else:
                invalid_entities += 1
        else:
            invalid_entities += 1
            missing_fields.append(f"实体{i}: 不是字典类型")
    
    validation_time = time.time() - validation_start
    
    print(f"📊 数据验证时间: {validation_time:.3f} 秒")
    print(f"📊 有效实体: {valid_entities}")
    print(f"📊 无效实体: {invalid_entities}")
    
    if missing_fields:
        print(f"⚠️ 发现数据问题:")
        for issue in missing_fields[:5]:  # 只显示前5个问题
            print(f"   {issue}")
        if len(missing_fields) > 5:
            print(f"   ... 还有 {len(missing_fields) - 5} 个问题")
    else:
        print(f"✅ 数据结构验证通过")
    
    return {
        'creation_time': creation_time,
        'validation_time': validation_time,
        'total_entities': len(test_entities),
        'valid_entities': valid_entities,
        'invalid_entities': invalid_entities,
        'data_valid': invalid_entities == 0,
        'test_entities': test_entities
    }


def test_data_operations():
    """测试数据操作（读取、复制、序列化等）"""
    print("\n🧪 测试2: 数据操作性能")
    print("=" * 60)
    
    # 获取测试数据
    data_result = test_data_validation()
    test_entities = data_result['test_entities']
    
    # 测试深拷贝
    print("📊 测试深拷贝操作...")
    deepcopy_start = time.time()
    copied_entities = copy.deepcopy(test_entities)
    deepcopy_time = time.time() - deepcopy_start
    print(f"   深拷贝时间: {deepcopy_time:.3f} 秒")
    
    # 测试浅拷贝
    print("📊 测试浅拷贝操作...")
    shallowcopy_start = time.time()
    shallow_copied = [entity.copy() for entity in test_entities]
    shallowcopy_time = time.time() - shallowcopy_start
    print(f"   浅拷贝时间: {shallowcopy_time:.3f} 秒")
    
    # 测试JSON序列化
    print("📊 测试JSON序列化...")
    json_start = time.time()
    json_data = json.dumps(test_entities, default=str)
    json_time = time.time() - json_start
    print(f"   JSON序列化时间: {json_time:.3f} 秒")
    print(f"   JSON数据大小: {len(json_data)} 字符")
    
    # 测试JSON反序列化
    print("📊 测试JSON反序列化...")
    parse_start = time.time()
    parsed_entities = json.loads(json_data)
    parse_time = time.time() - parse_start
    print(f"   JSON反序列化时间: {parse_time:.3f} 秒")
    
    # 测试数据访问
    print("📊 测试数据访问性能...")
    access_start = time.time()
    layer_count = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        layer_count[layer] = layer_count.get(layer, 0) + 1
    access_time = time.time() - access_start
    print(f"   数据访问时间: {access_time:.3f} 秒")
    print(f"   图层统计: {layer_count}")
    
    # 测试内存清理
    print("📊 测试内存清理...")
    gc_start = time.time()
    del copied_entities, shallow_copied, parsed_entities
    gc.collect()
    gc_time = time.time() - gc_start
    print(f"   内存清理时间: {gc_time:.3f} 秒")
    
    return {
        'deepcopy_time': deepcopy_time,
        'shallowcopy_time': shallowcopy_time,
        'json_serialize_time': json_time,
        'json_parse_time': parse_time,
        'data_access_time': access_time,
        'gc_time': gc_time,
        'json_size': len(json_data)
    }


def test_processor_initialization():
    """测试处理器初始化"""
    print("\n🧪 测试3: 处理器初始化")
    print("=" * 60)
    
    # 测试独立图层处理器初始化
    print("📊 测试独立图层处理器初始化...")
    init_start = time.time()
    processor = IndependentLayerProcessor()
    init_time = time.time() - init_start
    print(f"   初始化时间: {init_time:.3f} 秒")
    
    # 测试模式管理器初始化
    print("📊 测试模式管理器初始化...")
    mode_init_start = time.time()
    mode_manager = LineProcessingModeManager()
    mode_init_time = time.time() - mode_init_start
    print(f"   模式管理器初始化时间: {mode_init_time:.3f} 秒")
    
    # 测试模式设置
    print("📊 测试模式设置...")
    mode_set_start = time.time()
    mode_manager.set_mode(LineProcessingMode.INDEPENDENT)
    mode_set_time = time.time() - mode_set_start
    print(f"   模式设置时间: {mode_set_time:.3f} 秒")
    
    return {
        'processor_init_time': init_time,
        'mode_manager_init_time': mode_init_time,
        'mode_set_time': mode_set_time,
        'processor': processor,
        'mode_manager': mode_manager
    }


def test_processing_flow():
    """测试处理流程"""
    print("\n🧪 测试4: 处理流程")
    print("=" * 60)
    
    # 获取测试数据和处理器
    data_result = test_data_validation()
    test_entities = data_result['test_entities']
    
    init_result = test_processor_initialization()
    mode_manager = init_result['mode_manager']
    
    # 测试数据预处理
    print("📊 测试数据预处理...")
    preprocess_start = time.time()
    # 模拟数据预处理（可能包括验证、转换等）
    processed_entities = []
    for entity in test_entities:
        # 模拟一些预处理操作
        processed_entity = entity.copy()
        processed_entity['preprocessed'] = True
        processed_entity['timestamp'] = time.time()
        processed_entities.append(processed_entity)
    preprocess_time = time.time() - preprocess_start
    print(f"   数据预处理时间: {preprocess_time:.3f} 秒")
    
    # 测试核心处理
    print("📊 测试核心处理...")
    core_start = time.time()
    result = mode_manager.process_entities(processed_entities)
    core_time = time.time() - core_start
    print(f"   核心处理时间: {core_time:.3f} 秒")
    
    # 验证处理结果
    print("📊 验证处理结果...")
    verify_start = time.time()
    success = result.get('success', False)
    output_entities = result.get('entities', [])
    message = result.get('message', '')
    verify_time = time.time() - verify_start
    print(f"   结果验证时间: {verify_time:.3f} 秒")
    print(f"   处理成功: {success}")
    print(f"   输入实体: {len(processed_entities)}")
    print(f"   输出实体: {len(output_entities)}")
    print(f"   处理消息: {message}")
    
    # 测试结果后处理
    print("📊 测试结果后处理...")
    postprocess_start = time.time()
    # 模拟结果后处理（可能包括格式转换、验证等）
    final_entities = []
    for entity in output_entities:
        final_entity = entity.copy()
        final_entity['postprocessed'] = True
        final_entity['final_timestamp'] = time.time()
        final_entities.append(final_entity)
    postprocess_time = time.time() - postprocess_start
    print(f"   结果后处理时间: {postprocess_time:.3f} 秒")
    
    return {
        'preprocess_time': preprocess_time,
        'core_processing_time': core_time,
        'verify_time': verify_time,
        'postprocess_time': postprocess_time,
        'success': success,
        'input_count': len(processed_entities),
        'output_count': len(output_entities),
        'final_entities': final_entities
    }


def test_data_output():
    """测试数据输出"""
    print("\n🧪 测试5: 数据输出")
    print("=" * 60)
    
    # 获取处理结果
    process_result = test_processing_flow()
    final_entities = process_result['final_entities']
    
    # 测试数据格式化
    print("📊 测试数据格式化...")
    format_start = time.time()
    formatted_data = {
        'entities': final_entities,
        'metadata': {
            'total_count': len(final_entities),
            'processing_timestamp': time.time(),
            'version': '1.0'
        },
        'statistics': {
            'input_count': process_result['input_count'],
            'output_count': process_result['output_count'],
            'reduction_count': process_result['input_count'] - process_result['output_count']
        }
    }
    format_time = time.time() - format_start
    print(f"   数据格式化时间: {format_time:.3f} 秒")
    
    # 测试数据序列化
    print("📊 测试数据序列化...")
    serialize_start = time.time()
    serialized_data = json.dumps(formatted_data, default=str, ensure_ascii=False)
    serialize_time = time.time() - serialize_start
    print(f"   数据序列化时间: {serialize_time:.3f} 秒")
    print(f"   序列化数据大小: {len(serialized_data)} 字符")
    
    # 测试文件写入
    print("📊 测试文件写入...")
    write_start = time.time()
    with open('test_output.json', 'w', encoding='utf-8') as f:
        json.dump(formatted_data, f, default=str, ensure_ascii=False, indent=2)
    write_time = time.time() - write_start
    print(f"   文件写入时间: {write_time:.3f} 秒")
    
    # 测试文件读取验证
    print("📊 测试文件读取验证...")
    read_start = time.time()
    with open('test_output.json', 'r', encoding='utf-8') as f:
        loaded_data = json.load(f)
    read_time = time.time() - read_start
    print(f"   文件读取时间: {read_time:.3f} 秒")
    print(f"   读取数据验证: {len(loaded_data.get('entities', [])) == len(final_entities)}")
    
    # 清理测试文件
    try:
        os.remove('test_output.json')
    except:
        pass
    
    return {
        'format_time': format_time,
        'serialize_time': serialize_time,
        'write_time': write_time,
        'read_time': read_time,
        'data_size': len(serialized_data)
    }


def run_complete_bottleneck_test():
    """运行完整的瓶颈测试"""
    print("🚀 开始完整流程瓶颈测试")
    print("=" * 100)
    
    overall_start = time.time()
    
    try:
        # 1. 数据验证测试
        data_result = test_data_validation()
        
        # 2. 数据操作测试
        ops_result = test_data_operations()
        
        # 3. 处理器初始化测试
        init_result = test_processor_initialization()
        
        # 4. 处理流程测试
        process_result = test_processing_flow()
        
        # 5. 数据输出测试
        output_result = test_data_output()
        
        overall_time = time.time() - overall_start
        
        print(f"\n🎉 完整流程瓶颈测试完成")
        print("=" * 100)
        print(f"   总测试时间: {overall_time:.2f} 秒")
        
        # 综合分析
        print(f"\n📊 性能瓶颈分析:")
        
        # 收集所有时间数据
        all_times = [
            ('数据创建', data_result['creation_time']),
            ('数据验证', data_result['validation_time']),
            ('深拷贝操作', ops_result['deepcopy_time']),
            ('JSON序列化', ops_result['json_serialize_time']),
            ('JSON反序列化', ops_result['json_parse_time']),
            ('处理器初始化', init_result['processor_init_time']),
            ('模式管理器初始化', init_result['mode_manager_init_time']),
            ('数据预处理', process_result['preprocess_time']),
            ('核心处理', process_result['core_processing_time']),
            ('结果验证', process_result['verify_time']),
            ('结果后处理', process_result['postprocess_time']),
            ('数据格式化', output_result['format_time']),
            ('数据序列化', output_result['serialize_time']),
            ('文件写入', output_result['write_time']),
            ('文件读取', output_result['read_time'])
        ]
        
        # 按时间排序
        all_times.sort(key=lambda x: x[1], reverse=True)
        
        print(f"   性能排序（从慢到快）:")
        for i, (name, time_val) in enumerate(all_times):
            percentage = (time_val / overall_time) * 100
            print(f"   {i+1:2d}. {name}: {time_val:.3f} 秒 ({percentage:.1f}%)")
        
        # 识别主要瓶颈
        top_3_bottlenecks = all_times[:3]
        total_top3_time = sum(t[1] for t in top_3_bottlenecks)
        
        print(f"\n🚨 主要性能瓶颈:")
        for i, (name, time_val) in enumerate(top_3_bottlenecks):
            print(f"   {i+1}. {name}: {time_val:.3f} 秒")
        
        print(f"   前3大瓶颈占总时间: {(total_top3_time/overall_time)*100:.1f}%")
        
        # 数据问题检查
        print(f"\n📋 数据质量检查:")
        print(f"   数据结构有效: {data_result['data_valid']}")
        print(f"   处理成功: {process_result['success']}")
        print(f"   数据完整性: {process_result['output_count']}/{process_result['input_count']}")
        
        # 建议
        print(f"\n💡 优化建议:")
        if top_3_bottlenecks[0][1] > 1.0:
            print(f"   ⚠️ 主要瓶颈 '{top_3_bottlenecks[0][0]}' 耗时过长 ({top_3_bottlenecks[0][1]:.3f}秒)")
            print(f"   🎯 建议优先优化此环节")
        
        if ops_result['deepcopy_time'] > 0.5:
            print(f"   ⚠️ 深拷贝操作耗时较长，考虑使用浅拷贝")
        
        if ops_result['json_serialize_time'] > 0.5:
            print(f"   ⚠️ JSON序列化耗时较长，考虑优化数据结构")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_complete_bottleneck_test()
    
    if success:
        print(f"\n🎊 完整流程瓶颈测试成功！")
        print(f"   已识别出具体的性能瓶颈位置")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)

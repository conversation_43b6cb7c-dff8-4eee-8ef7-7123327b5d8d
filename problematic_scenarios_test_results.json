{"test_time": 1753712960.2993116, "scenario_results": [{"scenario_name": "完全重合线条场景 (阈值10)", "input_entities": 3, "input_layers": 3, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0, "distance_threshold": 10}, {"scenario_name": "完全重合线条场景 (阈值20)", "input_entities": 3, "input_layers": 3, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0, "distance_threshold": 20}, {"scenario_name": "完全重合线条场景 (阈值50)", "input_entities": 3, "input_layers": 3, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0, "distance_threshold": 50}, {"scenario_name": "端点精确重合场景 (阈值10)", "input_entities": 5, "input_layers": 4, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0019981861114501953, "distance_threshold": 10}, {"scenario_name": "端点精确重合场景 (阈值20)", "input_entities": 5, "input_layers": 4, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0, "distance_threshold": 20}, {"scenario_name": "端点精确重合场景 (阈值50)", "input_entities": 5, "input_layers": 4, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0010018348693847656, "distance_threshold": 50}, {"scenario_name": "距离阈值内场景 (阈值10)", "input_entities": 4, "input_layers": 4, "output_groups": 2, "pure_layer_groups": 2, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.00099945068359375, "distance_threshold": 10}, {"scenario_name": "距离阈值内场景 (阈值20)", "input_entities": 4, "input_layers": 4, "output_groups": 2, "pure_layer_groups": 2, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0, "distance_threshold": 20}, {"scenario_name": "距离阈值内场景 (阈值50)", "input_entities": 4, "input_layers": 4, "output_groups": 2, "pure_layer_groups": 2, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0009987354278564453, "distance_threshold": 50}, {"scenario_name": "混合实体类型场景 (阈值10)", "input_entities": 5, "input_layers": 5, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0010311603546142578, "distance_threshold": 10}, {"scenario_name": "混合实体类型场景 (阈值20)", "input_entities": 5, "input_layers": 5, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0010027885437011719, "distance_threshold": 20}, {"scenario_name": "混合实体类型场景 (阈值50)", "input_entities": 5, "input_layers": 5, "output_groups": 3, "pure_layer_groups": 3, "cross_layer_groups": 0, "cross_layer_issues": [], "processing_time": 0.0010039806365966797, "distance_threshold": 50}], "line_merger_results": [{"scenario": "完全重合线条", "input_lines": 3, "output_lines": 1, "processing_time": 0.005032539367675781, "merger_stats": {"original_lines": 3, "merged_lines": 2, "final_lines": 1, "processing_time": 0.005032539367675781, "iterations_performed": 2, "iteration_details": [{"iteration": 1, "input_count": 3, "output_count": 1, "merged_count": 2}, {"iteration": 2, "input_count": 1, "output_count": 1, "merged_count": 0}]}}, {"scenario": "端点连接线条", "input_lines": 3, "output_lines": 3, "processing_time": 0.0009996891021728516, "merger_stats": {"original_lines": 3, "merged_lines": 0, "final_lines": 3, "processing_time": 0.0009996891021728516, "iterations_performed": 1, "iteration_details": [{"iteration": 1, "input_count": 3, "output_count": 3, "merged_count": 0}]}}, {"scenario": "平行接近线条", "input_lines": 3, "output_lines": 1, "processing_time": 0.0060389041900634766, "merger_stats": {"original_lines": 3, "merged_lines": 2, "final_lines": 1, "processing_time": 0.0060389041900634766, "iterations_performed": 2, "iteration_details": [{"iteration": 1, "input_count": 3, "output_count": 1, "merged_count": 2}, {"iteration": 2, "input_count": 1, "output_count": 1, "merged_count": 0}]}}], "summary": {"total_scenarios_tested": 12, "scenarios_with_cross_layer_issues": 0, "cross_layer_issue_rate": 0.0, "total_test_time": 0.08994150161743164}}
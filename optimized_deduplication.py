#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的去重算法
解决简单去重O(n²)复杂度导致的性能问题
"""

import copy
import time
import hashlib
from typing import List, Dict, Any, Set


class OptimizedDeduplicator:
    """
    优化的去重器
    
    使用快速哈希去重算法，将复杂度从O(n²)降低到O(n)
    """
    
    def __init__(self, coordinate_precision=2):
        """
        初始化优化去重器
        
        Args:
            coordinate_precision: 坐标精度（小数点后位数）
        """
        self.coordinate_precision = coordinate_precision
        self.dedup_stats = {
            'total_processed': 0,
            'total_removed': 0,
            'processing_time': 0.0,
            'hash_collisions': 0
        }
    
    def deduplicate_entities(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """
        高效去重实体
        
        Args:
            entities: 输入实体列表
            layer_name: 图层名称
            context: 处理上下文
            
        Returns:
            去重后的实体列表
        """
        if not entities:
            return []
        
        print(f"       🚀 开始高效去重处理 ({context})...")
        start_time = time.time()
        
        # 使用快速哈希去重
        seen_hashes = set()
        deduplicated = []
        hash_collisions = 0
        
        for entity in entities:
            # 计算实体的快速哈希
            entity_hash = self._calculate_fast_hash(entity)
            
            if entity_hash not in seen_hashes:
                seen_hashes.add(entity_hash)
                
                # 添加去重标记
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = 'OptimizedDeduplicator'
                entity_copy['processing_type'] = 'fast_deduplication'
                entity_copy['dedup_timestamp'] = time.time()
                entity_copy['dedup_context'] = context
                entity_copy['processing_stage'] = 'deduplicated'
                entity_copy['dedup_hash'] = entity_hash
                
                deduplicated.append(entity_copy)
            else:
                # 检测哈希冲突（可选）
                if self._is_hash_collision(entity, deduplicated, entity_hash):
                    hash_collisions += 1
                    # 哈希冲突时，仍然保留实体
                    entity_copy = copy.deepcopy(entity)
                    entity_copy['processed_by'] = 'OptimizedDeduplicator'
                    entity_copy['processing_type'] = 'hash_collision_preserved'
                    entity_copy['dedup_timestamp'] = time.time()
                    entity_copy['dedup_context'] = context
                    entity_copy['processing_stage'] = 'deduplicated'
                    entity_copy['dedup_hash'] = entity_hash + '_collision'
                    
                    deduplicated.append(entity_copy)
        
        processing_time = time.time() - start_time
        removed_count = len(entities) - len(deduplicated)
        
        # 更新统计
        self.dedup_stats['total_processed'] += len(entities)
        self.dedup_stats['total_removed'] += removed_count
        self.dedup_stats['processing_time'] += processing_time
        self.dedup_stats['hash_collisions'] += hash_collisions
        
        if removed_count > 0:
            print(f"         🗑️ 高效去重移除实体: {removed_count} 个")
        if hash_collisions > 0:
            print(f"         ⚠️ 哈希冲突: {hash_collisions} 个")
        print(f"         ⚡ 去重时间: {processing_time:.3f} 秒")
        
        return deduplicated
    
    def _calculate_fast_hash(self, entity: Dict[str, Any]) -> str:
        """
        计算实体的快速哈希
        
        使用关键特征生成哈希，平衡速度和准确性
        """
        # 提取关键特征
        features = []
        
        # 1. 基本属性
        features.append(entity.get('type', ''))
        features.append(entity.get('layer', ''))
        
        # 2. 坐标信息（简化处理）
        if 'points' in entity:
            points = entity['points']
            if isinstance(points, list) and points:
                # 只使用第一个和最后一个点，减少计算量
                if len(points) >= 1:
                    first_point = self._round_point(points[0])
                    features.append(f"first:{first_point}")
                if len(points) >= 2:
                    last_point = self._round_point(points[-1])
                    features.append(f"last:{last_point}")
                # 添加点数量
                features.append(f"count:{len(points)}")
        
        if 'start_point' in entity:
            start_point = self._round_point(entity['start_point'])
            features.append(f"start:{start_point}")
        
        if 'end_point' in entity:
            end_point = self._round_point(entity['end_point'])
            features.append(f"end:{end_point}")
        
        # 3. 位置信息
        if 'position' in entity:
            position = self._round_point(entity['position'])
            features.append(f"pos:{position}")
        
        if 'center' in entity:
            center = self._round_point(entity['center'])
            features.append(f"center:{center}")
            # 添加半径
            if 'radius' in entity:
                radius = round(entity['radius'], self.coordinate_precision)
                features.append(f"radius:{radius}")
        
        # 4. 文字内容
        if 'text' in entity:
            features.append(f"text:{entity['text']}")
        
        # 5. 颜色
        if 'color' in entity:
            features.append(f"color:{entity['color']}")
        
        # 生成快速哈希
        feature_string = '|'.join(features)
        return hashlib.md5(feature_string.encode('utf-8')).hexdigest()[:16]  # 使用前16位
    
    def _round_point(self, point) -> str:
        """四舍五入坐标点"""
        if isinstance(point, (list, tuple)) and len(point) >= 2:
            x = round(point[0], self.coordinate_precision)
            y = round(point[1], self.coordinate_precision)
            return f"({x},{y})"
        return str(point)
    
    def _is_hash_collision(self, entity: Dict[str, Any], 
                          deduplicated: List[Dict[str, Any]], 
                          entity_hash: str) -> bool:
        """
        检测哈希冲突（可选的精确验证）
        
        当性能要求极高时，可以跳过此检查
        """
        # 简化的冲突检测：只检查最近的几个实体
        recent_entities = deduplicated[-5:] if len(deduplicated) > 5 else deduplicated
        
        for existing_entity in recent_entities:
            if existing_entity.get('dedup_hash', '').startswith(entity_hash):
                # 进行简单的精确比较
                if self._simple_compare(entity, existing_entity):
                    return False  # 确实是重复，不是冲突
                else:
                    return True   # 哈希相同但实体不同，是冲突
        
        return False
    
    def _simple_compare(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> bool:
        """简单的实体比较"""
        # 基本属性比较
        if entity1.get('type') != entity2.get('type'):
            return False
        if entity1.get('layer') != entity2.get('layer'):
            return False
        
        # 简化的坐标比较
        if 'points' in entity1 and 'points' in entity2:
            points1, points2 = entity1['points'], entity2['points']
            if len(points1) != len(points2):
                return False
            # 只比较第一个和最后一个点
            if points1 and points2:
                if not self._points_equal(points1[0], points2[0]):
                    return False
                if len(points1) > 1 and not self._points_equal(points1[-1], points2[-1]):
                    return False
        
        return True
    
    def _points_equal(self, p1, p2, tolerance=0.01) -> bool:
        """比较两个点是否相等"""
        if isinstance(p1, (list, tuple)) and isinstance(p2, (list, tuple)):
            if len(p1) >= 2 and len(p2) >= 2:
                return (abs(p1[0] - p2[0]) <= tolerance and 
                       abs(p1[1] - p2[1]) <= tolerance)
        return False
    
    def get_dedup_statistics(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        return {
            'dedup_stats': self.dedup_stats,
            'algorithm': 'fast_hash_deduplication',
            'complexity': 'O(n)',
            'coordinate_precision': self.coordinate_precision
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.dedup_stats = {
            'total_processed': 0,
            'total_removed': 0,
            'processing_time': 0.0,
            'hash_collisions': 0
        }


class HybridDeduplicator:
    """
    混合去重器
    
    根据实体数量自动选择最优算法：
    - 小数据集（<50）：使用精确比较
    - 大数据集（>=50）：使用快速哈希
    """
    
    def __init__(self, threshold=50):
        """
        初始化混合去重器
        
        Args:
            threshold: 切换算法的阈值
        """
        self.threshold = threshold
        self.optimized_deduplicator = OptimizedDeduplicator()
    
    def deduplicate_entities(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """
        混合去重实体
        
        根据实体数量自动选择最优算法
        """
        if not entities:
            return []
        
        entity_count = len(entities)
        
        if entity_count < self.threshold:
            print(f"       🎯 使用精确去重 (实体数: {entity_count})")
            return self._precise_deduplicate(entities, layer_name, context)
        else:
            print(f"       🚀 使用快速去重 (实体数: {entity_count})")
            return self.optimized_deduplicator.deduplicate_entities(entities, layer_name, context)
    
    def _precise_deduplicate(self, entities: List[Dict[str, Any]], 
                           layer_name: str, context: str) -> List[Dict[str, Any]]:
        """精确去重（用于小数据集）"""
        start_time = time.time()
        
        deduplicated = []
        seen_entities = []
        
        for entity in entities:
            is_duplicate = False
            
            # 精确比较
            for seen_entity in seen_entities:
                if self._is_precise_duplicate(entity, seen_entity):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = 'HybridDeduplicator'
                entity_copy['processing_type'] = 'precise_deduplication'
                entity_copy['dedup_timestamp'] = time.time()
                entity_copy['dedup_context'] = context
                entity_copy['processing_stage'] = 'deduplicated'
                
                deduplicated.append(entity_copy)
                seen_entities.append(entity)
        
        processing_time = time.time() - start_time
        removed_count = len(entities) - len(deduplicated)
        
        if removed_count > 0:
            print(f"         🗑️ 精确去重移除实体: {removed_count} 个")
        print(f"         ⚡ 去重时间: {processing_time:.3f} 秒")
        
        return deduplicated
    
    def _is_precise_duplicate(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> bool:
        """精确的重复判断"""
        # 基本属性检查
        if entity1.get('type') != entity2.get('type'):
            return False
        if entity1.get('layer') != entity2.get('layer'):
            return False
        
        # 坐标比较
        if 'points' in entity1 and 'points' in entity2:
            points1, points2 = entity1['points'], entity2['points']
            if len(points1) != len(points2):
                return False
            for p1, p2 in zip(points1, points2):
                if not self._points_equal(p1, p2):
                    return False
        
        # 其他属性比较
        for attr in ['start_point', 'end_point', 'position', 'center', 'text', 'color']:
            if attr in entity1 and attr in entity2:
                if attr in ['start_point', 'end_point', 'position', 'center']:
                    if not self._points_equal(entity1[attr], entity2[attr]):
                        return False
                else:
                    if entity1[attr] != entity2[attr]:
                        return False
        
        return True
    
    def _points_equal(self, p1, p2, tolerance=0.01) -> bool:
        """比较两个点是否相等"""
        if isinstance(p1, (list, tuple)) and isinstance(p2, (list, tuple)):
            if len(p1) >= 2 and len(p2) >= 2:
                return (abs(p1[0] - p2[0]) <= tolerance and 
                       abs(p1[1] - p2[1]) <= tolerance)
        return p1 == p2
    
    def get_dedup_statistics(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        return {
            'algorithm': 'hybrid_deduplication',
            'threshold': self.threshold,
            'optimized_stats': self.optimized_deduplicator.get_dedup_statistics()
        }


# 工厂函数
def create_optimized_deduplicator(coordinate_precision=2) -> OptimizedDeduplicator:
    """创建优化去重器"""
    return OptimizedDeduplicator(coordinate_precision)


def create_hybrid_deduplicator(threshold=50) -> HybridDeduplicator:
    """创建混合去重器"""
    return HybridDeduplicator(threshold)

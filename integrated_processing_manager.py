#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集成处理流程管理器
整合图层数据容器、处理管道、线条处理器和数据传递机制
确保各图层数据分别处理且信息类型保留传递
"""

import time
from typing import List, Dict, Any, Optional

from layer_data_container import LayerDataContainer, LayerType
from layer_processing_pipeline import LayerProcessingPipeline
from enhanced_line_processor import LayerAwareLineProcessor
from data_transfer_mechanism import DataTransferMechanism, TransferStage


class IntegratedProcessingManager:
    """
    集成处理流程管理器
    
    功能：
    1. 统一管理整个处理流程
    2. 确保图层数据分离处理
    3. 保证信息类型不丢失
    4. 提供完整的处理追踪
    """
    
    def __init__(self):
        """初始化集成处理管理器"""
        # 初始化各个组件
        self.pipeline = LayerProcessingPipeline()
        self.line_processor = LayerAwareLineProcessor()
        self.transfer_mechanism = DataTransferMechanism()
        
        # 处理统计
        self.processing_stats = {
            'total_files_processed': 0,
            'total_entities_processed': 0,
            'total_containers_created': 0,
            'total_processing_time': 0.0,
            'stage_times': {}
        }
        
        print("🎯 集成处理流程管理器初始化完成")
    
    def process_file_entities(self, entities: List[Dict[str, Any]], 
                            file_path: str = None) -> Dict[str, Any]:
        """
        处理文件实体的完整流程
        
        Args:
            entities: 实体列表
            file_path: 文件路径
            
        Returns:
            处理结果字典
        """
        print(f"\n🚀 开始处理文件实体: {len(entities)} 个实体")
        if file_path:
            print(f"   文件路径: {file_path}")
        
        start_time = time.time()
        
        try:
            # 阶段1: 创建图层容器
            print(f"\n📋 阶段1: 创建图层容器")
            stage_start = time.time()
            
            containers = self.pipeline.create_containers_from_entities(entities)
            
            # 传递到线条处理阶段
            containers = self.transfer_mechanism.transfer_containers(
                containers, 
                TransferStage.FILE_LOADING, 
                TransferStage.LINE_PROCESSING,
                {'file_path': file_path}
            )
            
            stage_time = time.time() - stage_start
            self.processing_stats['stage_times']['container_creation'] = stage_time
            print(f"   ✅ 容器创建完成，耗时 {stage_time:.2f} 秒")
            
            # 阶段2: 线条处理
            print(f"\n🔧 阶段2: 线条处理")
            stage_start = time.time()
            
            processed_containers = self.line_processor.process_containers(containers)
            
            # 传递到分组阶段
            processed_containers = self.transfer_mechanism.transfer_containers(
                processed_containers,
                TransferStage.LINE_PROCESSING,
                TransferStage.ENTITY_GROUPING
            )
            
            stage_time = time.time() - stage_start
            self.processing_stats['stage_times']['line_processing'] = stage_time
            print(f"   ✅ 线条处理完成，耗时 {stage_time:.2f} 秒")
            
            # 阶段3: 实体分组
            print(f"\n🔗 阶段3: 实体分组")
            stage_start = time.time()
            
            grouped_containers = self.pipeline.process_containers(processed_containers)
            
            # 传递到自动标注阶段
            grouped_containers = self.transfer_mechanism.transfer_containers(
                grouped_containers,
                TransferStage.ENTITY_GROUPING,
                TransferStage.AUTO_LABELING
            )
            
            stage_time = time.time() - stage_start
            self.processing_stats['stage_times']['entity_grouping'] = stage_time
            print(f"   ✅ 实体分组完成，耗时 {stage_time:.2f} 秒")
            
            # 阶段4: 数据整合和验证
            print(f"\n🔍 阶段4: 数据整合和验证")
            stage_start = time.time()
            
            final_result = self._integrate_and_validate_results(grouped_containers, file_path)
            
            stage_time = time.time() - stage_start
            self.processing_stats['stage_times']['integration_validation'] = stage_time
            print(f"   ✅ 数据整合验证完成，耗时 {stage_time:.2f} 秒")
            
            # 更新总体统计
            total_time = time.time() - start_time
            self.processing_stats['total_files_processed'] += 1
            self.processing_stats['total_entities_processed'] += len(entities)
            self.processing_stats['total_containers_created'] += len(containers)
            self.processing_stats['total_processing_time'] += total_time
            
            print(f"\n🎉 文件处理完成，总耗时 {total_time:.2f} 秒")
            
            return final_result
            
        except Exception as e:
            print(f"\n❌ 文件处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 返回错误结果
            return {
                'success': False,
                'error': str(e),
                'containers': [],
                'entities': entities,
                'groups': [],
                'labeled_entities': [],
                'processing_time': time.time() - start_time
            }
    
    def _integrate_and_validate_results(self, containers: List[LayerDataContainer], 
                                      file_path: str = None) -> Dict[str, Any]:
        """
        整合和验证处理结果
        
        Args:
            containers: 处理后的容器列表
            file_path: 文件路径
            
        Returns:
            整合后的结果字典
        """
        print(f"   🔍 整合 {len(containers)} 个容器的结果")
        
        # 收集所有数据
        all_entities = []
        all_groups = []
        all_labeled_entities = []
        layer_summary = {}
        
        for container in containers:
            # 验证容器完整性
            validation_result = container.validate_data_integrity()
            
            if not validation_result['is_valid']:
                print(f"     ⚠️ 容器验证失败: {container.layer_name} - {validation_result['errors']}")
            
            # 收集实体
            container_entities = container.get_all_entities()
            all_entities.extend(container_entities)
            
            # 收集分组
            all_groups.extend(container.groups)
            
            # 收集标注实体
            all_labeled_entities.extend(container.labeled_entities)
            
            # 生成图层摘要
            layer_summary[container.layer_name] = {
                'layer_type': container.layer_type.value,
                'entity_count': len(container_entities),
                'group_count': len(container.groups),
                'labeled_count': len(container.labeled_entities),
                'processing_summary': container.get_processing_summary()
            }
        
        # 验证数据完整性
        integrity_check = self._check_data_integrity(all_entities, all_groups, all_labeled_entities)
        
        # 生成处理摘要
        processing_summary = {
            'pipeline_summary': self.pipeline.get_pipeline_summary(),
            'line_processor_summary': self.line_processor.get_processing_summary(),
            'transfer_summary': self.transfer_mechanism.get_transfer_summary(),
            'manager_stats': self.processing_stats
        }
        
        result = {
            'success': True,
            'file_path': file_path,
            'containers': containers,
            'entities': all_entities,
            'groups': all_groups,
            'labeled_entities': all_labeled_entities,
            'layer_summary': layer_summary,
            'integrity_check': integrity_check,
            'processing_summary': processing_summary,
            'total_entities': len(all_entities),
            'total_groups': len(all_groups),
            'total_labeled': len(all_labeled_entities),
            'total_layers': len(containers)
        }
        
        print(f"     ✅ 整合完成: {len(all_entities)} 实体, {len(all_groups)} 组, {len(all_labeled_entities)} 标注")
        
        return result
    
    def _check_data_integrity(self, entities: List[Dict[str, Any]], 
                            groups: List[List[Dict[str, Any]]], 
                            labeled_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        检查数据完整性
        
        Args:
            entities: 所有实体
            groups: 所有分组
            labeled_entities: 所有标注实体
            
        Returns:
            完整性检查结果
        """
        integrity_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 检查实体类型信息
        entities_without_type = 0
        entities_without_layer = 0
        
        for entity in entities:
            if isinstance(entity, dict):
                if 'container_layer_type' not in entity:
                    entities_without_type += 1
                if 'layer' not in entity:
                    entities_without_layer += 1
        
        if entities_without_type > 0:
            integrity_result['warnings'].append(f"{entities_without_type} 个实体缺少容器类型信息")
        
        if entities_without_layer > 0:
            integrity_result['warnings'].append(f"{entities_without_layer} 个实体缺少图层信息")
        
        # 检查分组数据
        group_entity_count = sum(len(group) for group in groups)
        
        # 统计信息
        integrity_result['statistics'] = {
            'total_entities': len(entities),
            'total_groups': len(groups),
            'total_labeled': len(labeled_entities),
            'group_entity_count': group_entity_count,
            'entities_without_type': entities_without_type,
            'entities_without_layer': entities_without_layer
        }
        
        return integrity_result
    
    def get_manager_summary(self) -> Dict[str, Any]:
        """获取管理器摘要"""
        return {
            'processing_stats': self.processing_stats,
            'component_summaries': {
                'pipeline': self.pipeline.get_pipeline_summary(),
                'line_processor': self.line_processor.get_processing_summary(),
                'transfer_mechanism': self.transfer_mechanism.get_transfer_summary()
            }
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            'total_files_processed': 0,
            'total_entities_processed': 0,
            'total_containers_created': 0,
            'total_processing_time': 0.0,
            'stage_times': {}
        }
        print("📊 统计信息已重置")

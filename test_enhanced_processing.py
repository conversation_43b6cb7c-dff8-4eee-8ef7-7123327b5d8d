#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强处理架构
验证各图层数据分离处理和信息传递的效果
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from layer_data_container import LayerDataContainer, LayerType
    from layer_processing_pipeline import LayerProcessingPipeline
    from enhanced_line_processor import LayerAwareLineProcessor
    from data_transfer_mechanism import DataTransferMechanism, TransferStage
    from integrated_processing_manager import IntegratedProcessingManager
    from processing_integration_adapter import ProcessingIntegrationAdapter
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_test_entities() -> List[Dict[str, Any]]:
    """创建测试实体数据"""
    test_entities = [
        # 墙体实体
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [0, 0],
            'end_point': [100, 0],
            'color': 1
        },
        {
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [100, 0],
            'end_point': [100, 100],
            'color': 1
        },
        {
            'type': 'LINE',
            'layer': 'WALL-INNER',
            'start_point': [0, 100],
            'end_point': [100, 100],
            'color': 1
        },
        
        # 门窗实体
        {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [50, 0],
            'end_point': [70, 0],
            'color': 2
        },
        {
            'type': 'ARC',
            'layer': 'A-DOOR',
            'center': [60, 0],
            'radius': 10,
            'color': 2
        },
        {
            'type': 'LINE',
            'layer': 'A-WIND',
            'start_point': [100, 30],
            'end_point': [100, 50],
            'color': 3
        },
        
        # 栏杆实体
        {
            'type': 'LINE',
            'layer': 'A-RAIL',
            'start_point': [0, 200],
            'end_point': [50, 200],
            'color': 4
        },
        {
            'type': 'LINE',
            'layer': 'RAILING',
            'start_point': [50, 200],
            'end_point': [100, 200],
            'color': 4
        },
        
        # 文字实体
        {
            'type': 'TEXT',
            'layer': 'A-ANNO-TEXT',
            'text': '房间1',
            'position': [50, 50],
            'height': 5,
            'color': 5
        },
        {
            'type': 'MTEXT',
            'layer': 'TEXT-LAYER',
            'text': '说明文字',
            'position': [150, 50],
            'height': 3,
            'color': 5
        },
        
        # 标注实体
        {
            'type': 'DIMENSION',
            'layer': 'A-DIMS',
            'def_points': [[0, 0], [100, 0], [50, -20]],
            'text': '100',
            'color': 6
        },
        {
            'type': 'LINEAR_DIMENSION',
            'layer': 'DIM-LAYER',
            'def_points': [[100, 0], [100, 100], [120, 50]],
            'text': '100',
            'color': 6
        },
        
        # 其他实体
        {
            'type': 'CIRCLE',
            'layer': 'FURNITURE',
            'center': [200, 200],
            'radius': 20,
            'color': 7
        },
        {
            'type': 'POLYLINE',
            'layer': 'MISC',
            'points': [[300, 300], [350, 300], [350, 350], [300, 350]],
            'closed': True,
            'color': 8
        }
    ]
    
    return test_entities


def test_layer_data_container():
    """测试图层数据容器"""
    print("\n🧪 测试图层数据容器")
    print("=" * 50)
    
    # 创建测试实体
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [0, 0], 'end_point': [100, 0]},
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [100, 0], 'end_point': [100, 100]}
    ]
    
    # 创建容器
    container = LayerDataContainer(
        layer_name='A-WALL',
        layer_type=LayerType.WALL,
        original_entities=wall_entities
    )
    
    print(f"✅ 容器创建成功: {container}")
    
    # 测试添加处理后实体
    processed_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [0, 0], 'end_point': [100, 0], 'processed': True}
    ]
    container.add_entities(processed_entities, "processed")
    
    # 测试分组
    groups = [wall_entities]
    container.set_groups(groups, "TestProcessor")
    
    # 测试标注
    labeled_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'label': 'wall', 'auto_labeled': True}
    ]
    container.set_labeled_entities(labeled_entities, "TestProcessor")
    
    # 验证数据完整性
    validation = container.validate_data_integrity()
    print(f"   数据完整性验证: {'✅ 通过' if validation['is_valid'] else '❌ 失败'}")
    if not validation['is_valid']:
        print(f"   错误: {validation['errors']}")
    
    # 获取处理摘要
    summary = container.get_processing_summary()
    print(f"   处理摘要: {summary['data_counts']}")
    
    return container


def test_layer_processing_pipeline():
    """测试图层处理管道"""
    print("\n🧪 测试图层处理管道")
    print("=" * 50)
    
    # 创建管道
    pipeline = LayerProcessingPipeline()
    
    # 创建测试实体
    test_entities = create_test_entities()
    
    # 从实体创建容器
    containers = pipeline.create_containers_from_entities(test_entities)
    print(f"✅ 创建了 {len(containers)} 个容器")
    
    # 处理容器
    processed_containers = pipeline.process_containers(containers)
    print(f"✅ 处理了 {len(processed_containers)} 个容器")
    
    # 验证处理结果
    for container in processed_containers:
        validation = container.validate_data_integrity()
        status = "✅ 通过" if validation['is_valid'] else "❌ 失败"
        print(f"   {container.layer_name} ({container.layer_type.value}): {status}")
    
    return processed_containers


def test_enhanced_line_processor():
    """测试增强线条处理器"""
    print("\n🧪 测试增强线条处理器")
    print("=" * 50)
    
    # 创建处理器
    line_processor = LayerAwareLineProcessor()
    
    # 创建测试容器
    wall_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [0, 0], 'end_point': [100, 0]},
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [100, 0], 'end_point': [100, 100]}
    ]
    
    container = LayerDataContainer(
        layer_name='A-WALL',
        layer_type=LayerType.WALL,
        original_entities=wall_entities
    )
    
    # 处理容器
    processed_container = line_processor.process_single_container(container)
    
    print(f"✅ 线条处理完成")
    print(f"   原始实体: {len(container.original_entities)}")
    print(f"   处理后实体: {len(processed_container.processed_entities)}")
    
    return processed_container


def test_data_transfer_mechanism():
    """测试数据传递机制"""
    print("\n🧪 测试数据传递机制")
    print("=" * 50)
    
    # 创建传递机制
    transfer_mechanism = DataTransferMechanism()
    
    # 创建测试容器
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'start_point': [0, 0], 'end_point': [100, 0]}
    ]
    
    container = LayerDataContainer(
        layer_name='A-WALL',
        layer_type=LayerType.WALL,
        original_entities=test_entities
    )
    
    # 测试传递
    transferred_container = transfer_mechanism.transfer_single_container(
        container,
        TransferStage.FILE_LOADING,
        TransferStage.LINE_PROCESSING
    )
    
    print(f"✅ 数据传递完成")
    print(f"   传递记录数: {len(transfer_mechanism.transfer_records)}")
    
    # 获取传递摘要
    summary = transfer_mechanism.get_transfer_summary()
    print(f"   传递统计: {summary['transfer_stats']}")
    
    return transferred_container


def test_integrated_processing_manager():
    """测试集成处理管理器"""
    print("\n🧪 测试集成处理管理器")
    print("=" * 50)
    
    # 创建管理器
    manager = IntegratedProcessingManager()
    
    # 创建测试实体
    test_entities = create_test_entities()
    
    # 处理实体
    result = manager.process_file_entities(test_entities, "test_file.dxf")
    
    print(f"✅ 集成处理完成")
    print(f"   处理成功: {result['success']}")
    print(f"   总实体数: {result['total_entities']}")
    print(f"   总分组数: {result['total_groups']}")
    print(f"   总标注数: {result['total_labeled']}")
    print(f"   图层数: {result['total_layers']}")
    
    # 验证数据完整性
    integrity = result['integrity_check']
    print(f"   数据完整性: {'✅ 通过' if integrity['is_valid'] else '❌ 失败'}")
    
    return result


def test_processing_integration_adapter():
    """测试处理集成适配器"""
    print("\n🧪 测试处理集成适配器")
    print("=" * 50)
    
    # 创建适配器
    adapter = ProcessingIntegrationAdapter()
    
    # 创建测试实体
    test_entities = create_test_entities()
    
    # 测试增强处理
    print("   测试增强处理模式...")
    enhanced_result = adapter.process_file_entities(test_entities, "test_file.dxf", force_legacy=False)
    
    print(f"   增强处理结果: {enhanced_result['processing_mode']}")
    print(f"   处理成功: {enhanced_result['success']}")
    print(f"   实体数: {enhanced_result['total_entities']}")
    
    # 测试传统处理
    print("   测试传统处理模式...")
    legacy_result = adapter.process_file_entities(test_entities, "test_file.dxf", force_legacy=True)
    
    print(f"   传统处理结果: {legacy_result['processing_mode']}")
    print(f"   处理成功: {legacy_result['success']}")
    print(f"   实体数: {legacy_result['total_entities']}")
    
    # 获取适配器摘要
    summary = adapter.get_adapter_summary()
    print(f"   适配器统计: {summary['adapter_stats']}")
    
    return enhanced_result, legacy_result


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始综合测试增强处理架构")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return
    
    start_time = time.time()
    
    try:
        # 测试各个组件
        print("\n📦 组件测试")
        container = test_layer_data_container()
        containers = test_layer_processing_pipeline()
        processed_container = test_enhanced_line_processor()
        transferred_container = test_data_transfer_mechanism()
        
        print("\n🎯 集成测试")
        integrated_result = test_integrated_processing_manager()
        enhanced_result, legacy_result = test_processing_integration_adapter()
        
        # 总结测试结果
        total_time = time.time() - start_time
        
        print(f"\n🎉 综合测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   测试组件: 6 个")
        print(f"   测试实体: {len(create_test_entities())} 个")
        print(f"   增强处理成功: {enhanced_result['success']}")
        print(f"   传统处理成功: {legacy_result['success']}")
        
        # 保存测试结果
        test_result = {
            'test_time': time.time(),
            'total_duration': total_time,
            'enhanced_result': enhanced_result,
            'legacy_result': legacy_result,
            'test_entities_count': len(create_test_entities())
        }
        
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)

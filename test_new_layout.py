#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的三区域布局
验证：
1. 区域1（左上）：图像控制的四个按钮
2. 区域2（左下）：配色系统
3. 区域3（右侧）：图像控制（图层控制和阴影识别）
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_layout():
    """测试新的三区域布局"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🧪 开始测试新的三区域布局...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("新三区域布局测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 检查新布局相关的组件是否存在
        layout_components = [
            'image_control_buttons_frame',  # 区域1：图像控制按钮
            'color_frame',                  # 区域2：配色系统
            'zoom_frame',                   # 区域3：图像控制
            'layer_control_container',      # 图层控制容器
            'shadow_control_container'      # 阴影控制容器
        ]
        
        print("\n📋 检查新布局组件:")
        for component_name in layout_components:
            if hasattr(app, component_name):
                component = getattr(app, component_name)
                if component and component.winfo_exists():
                    print(f"  ✅ {component_name} - 存在且可见")
                else:
                    print(f"  ⚠️ {component_name} - 存在但不可见")
            else:
                print(f"  ❌ {component_name} - 缺失")
        
        # 检查按钮功能
        button_functions = [
            '_reset_left_view',
            '_reset_right_view', 
            '_save_current_image',
            '_open_zoom_view_window'
        ]
        
        print("\n📋 检查按钮功能:")
        for func_name in button_functions:
            if hasattr(app, func_name):
                print(f"  ✅ {func_name} - 存在")
            else:
                print(f"  ❌ {func_name} - 缺失")
        
        # 创建测试控制面板
        test_frame = tk.Frame(root)
        test_frame.pack(side='bottom', fill='x', padx=10, pady=10)
        
        tk.Label(test_frame, text="新布局测试控制:", font=('Arial', 12, 'bold')).pack(anchor='w')
        
        button_frame = tk.Frame(test_frame)
        button_frame.pack(fill='x', pady=5)
        
        # 测试区域1按钮
        def test_area1_buttons():
            try:
                print("\n🧪 测试区域1（左上）按钮功能...")
                app._reset_left_view()
                app._reset_right_view()
                messagebox.showinfo("测试", "区域1（左上）按钮测试完成\n✅ 重置视图按钮正常")
            except Exception as e:
                messagebox.showerror("错误", f"区域1按钮测试失败: {e}")
        
        tk.Button(button_frame, text="测试区域1按钮", command=test_area1_buttons,
                 bg='#4CAF50', fg='white').pack(side='left', padx=2)
        
        # 测试区域2配色系统
        def test_area2_color_system():
            try:
                print("\n🧪 测试区域2（左下）配色系统...")
                if hasattr(app, 'color_frame') and app.color_frame.winfo_exists():
                    messagebox.showinfo("测试", "区域2（左下）配色系统测试完成\n✅ 配色系统界面正常")
                else:
                    messagebox.showwarning("警告", "区域2配色系统界面未找到")
            except Exception as e:
                messagebox.showerror("错误", f"区域2配色系统测试失败: {e}")
        
        tk.Button(button_frame, text="测试区域2配色", command=test_area2_color_system,
                 bg='#2196F3', fg='white').pack(side='left', padx=2)
        
        # 测试区域3图像控制
        def test_area3_image_control():
            try:
                print("\n🧪 测试区域3（右侧）图像控制...")
                if hasattr(app, 'zoom_frame') and app.zoom_frame.winfo_exists():
                    # 测试阴影识别功能
                    app._start_shadow_recognition()
                    messagebox.showinfo("测试", "区域3（右侧）图像控制测试完成\n✅ 图层控制和阴影识别正常")
                else:
                    messagebox.showwarning("警告", "区域3图像控制界面未找到")
            except Exception as e:
                messagebox.showerror("错误", f"区域3图像控制测试失败: {e}")
        
        tk.Button(button_frame, text="测试区域3控制", command=test_area3_image_control,
                 bg='#FF5722', fg='white').pack(side='left', padx=2)
        
        # 测试整体布局
        def test_overall_layout():
            try:
                print("\n🧪 测试整体布局结构...")
                layout_info = []
                
                # 检查区域1
                if hasattr(app, 'image_control_buttons_frame'):
                    layout_info.append("✅ 区域1（左上）：图像控制按钮 - 正常")
                else:
                    layout_info.append("❌ 区域1（左上）：图像控制按钮 - 缺失")
                
                # 检查区域2
                if hasattr(app, 'color_frame'):
                    layout_info.append("✅ 区域2（左下）：配色系统 - 正常")
                else:
                    layout_info.append("❌ 区域2（左下）：配色系统 - 缺失")
                
                # 检查区域3
                if hasattr(app, 'zoom_frame'):
                    layout_info.append("✅ 区域3（右侧）：图像控制 - 正常")
                else:
                    layout_info.append("❌ 区域3（右侧）：图像控制 - 缺失")
                
                messagebox.showinfo("布局测试结果", "\n".join(layout_info))
                
            except Exception as e:
                messagebox.showerror("错误", f"整体布局测试失败: {e}")
        
        tk.Button(button_frame, text="测试整体布局", command=test_overall_layout,
                 bg='#9C27B0', fg='white').pack(side='left', padx=2)
        
        print("\n✅ 新三区域布局测试环境已准备就绪")
        print("📝 请在界面中查看新的布局结构:")
        print("   - 区域1（左上）：图像控制的四个按钮")
        print("   - 区域2（左下）：配色系统")
        print("   - 区域3（右侧）：图像控制（图层控制和阴影识别）")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_layout()

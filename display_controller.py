#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
显示控制模块 - 统一管理所有图像显示输出
实现数据变化监听、配色系统集成、显示更新控制等功能
"""

import time
import threading
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime


class DataChangeType(Enum):
    """数据变化类型枚举"""
    DXF_FILE_DATA = "dxf_file_data"           # DXF文件数据更改
    GROUP_DATA = "group_data"                 # 组数据更改
    GROUP_TYPE = "group_type"                 # 组类型更改
    GROUP_FILL = "group_fill"                 # 组填充更改
    WALL_FILL = "wall_fill"                   # 墙体填充更改
    ROOM_FILL = "room_fill"                   # 房间填充更改
    COLOR_SCHEME = "color_scheme"             # 配色方案更改
    VISIBILITY = "visibility"                 # 可见性更改


@dataclass
class DataChangeEvent:
    """数据变化事件"""
    change_type: DataChangeType
    data: Any
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DisplayState:
    """显示状态"""
    last_update_time: datetime = field(default_factory=datetime.now)
    pending_updates: List[DataChangeEvent] = field(default_factory=list)
    is_updating: bool = False
    update_count: int = 0


class DisplayController:
    """显示控制器 - 统一管理所有图像显示输出"""
    
    def __init__(self, visualizer=None, canvas=None, color_system=None):
        """
        初始化显示控制器
        
        Args:
            visualizer: 可视化器实例
            canvas: 画布实例
            color_system: 配色系统实例
        """
        self.visualizer = visualizer
        self.canvas = canvas
        self.color_system = color_system
        
        # 显示状态管理
        self.display_state = DisplayState()
        
        # 数据变化监听器
        self.change_listeners: Dict[DataChangeType, List[Callable]] = {}
        
        # 配色缓存
        self.color_cache: Dict[str, str] = {}
        
        # 显示更新锁
        self._update_lock = threading.Lock()
        
        # 批量更新控制
        self.batch_update_enabled = True
        self.batch_update_delay = 0.1  # 100ms延迟批量更新
        self._batch_timer = None
        
        # 调试模式
        self.debug_mode = True
        

    
    def register_change_listener(self, change_type: DataChangeType, callback: Callable):
        """注册数据变化监听器"""
        if change_type not in self.change_listeners:
            self.change_listeners[change_type] = []
        self.change_listeners[change_type].append(callback)
        
        if self.debug_mode:
            print(f"📝 注册监听器: {change_type.value}")
    
    def notify_data_change(self, change_type: DataChangeType, data: Any, 
                          source: str = "", metadata: Dict[str, Any] = None):
        """通知数据变化"""
        if metadata is None:
            metadata = {}
            
        # 创建变化事件
        event = DataChangeEvent(
            change_type=change_type,
            data=data,
            source=source,
            metadata=metadata
        )
        
        if self.debug_mode:
            print(f"🔔 数据变化通知: {change_type.value} from {source}")
        
        # 检测是否真的有变化
        if self._has_actual_change(event):
            # 添加到待处理队列
            self.display_state.pending_updates.append(event)
            
            # 触发显示更新
            self._trigger_display_update(event)
        else:
            if self.debug_mode:
                print(f"⏭️ 跳过无变化更新: {change_type.value}")
    
    def _has_actual_change(self, event: DataChangeEvent) -> bool:
        """检测是否有实际变化"""
        try:
            # 根据变化类型进行不同的检测逻辑
            if event.change_type == DataChangeType.COLOR_SCHEME:
                return self._has_color_scheme_change(event.data)
            elif event.change_type == DataChangeType.GROUP_DATA:
                return self._has_group_data_change(event.data)
            elif event.change_type == DataChangeType.GROUP_FILL:
                return self._has_fill_change(event.data)
            else:
                # 默认认为有变化
                return True
                
        except Exception as e:
            if self.debug_mode:
                print(f"⚠️ 变化检测失败: {e}")
            return True  # 出错时默认认为有变化
    
    def _has_color_scheme_change(self, new_scheme: Dict[str, str]) -> bool:
        """检测配色方案是否有变化"""
        if not hasattr(self, '_last_color_scheme'):
            self._last_color_scheme = {}
            return True
            
        # 比较配色方案
        for key, value in new_scheme.items():
            if self._last_color_scheme.get(key) != value:
                self._last_color_scheme = new_scheme.copy()
                return True
                
        return False
    
    def _has_group_data_change(self, group_data: Any) -> bool:
        """检测组数据是否有变化"""
        # 简化实现：基于数据长度和时间戳
        current_hash = hash(str(group_data)) if group_data else 0
        
        if not hasattr(self, '_last_group_hash'):
            self._last_group_hash = current_hash
            return True
            
        if self._last_group_hash != current_hash:
            self._last_group_hash = current_hash
            return True
            
        return False
    
    def _has_fill_change(self, fill_data: Any) -> bool:
        """检测填充数据是否有变化"""
        current_hash = hash(str(fill_data)) if fill_data else 0
        
        if not hasattr(self, '_last_fill_hash'):
            self._last_fill_hash = current_hash
            return True
            
        if self._last_fill_hash != current_hash:
            self._last_fill_hash = current_hash
            return True
            
        return False
    
    def _trigger_display_update(self, event: DataChangeEvent):
        """触发显示更新"""
        if self.batch_update_enabled:
            # 批量更新模式
            self._schedule_batch_update()
        else:
            # 立即更新模式
            self._perform_display_update([event])
    
    def _schedule_batch_update(self):
        """调度批量更新"""
        # 取消之前的定时器
        if self._batch_timer:
            self._batch_timer.cancel()
        
        # 设置新的定时器
        self._batch_timer = threading.Timer(
            self.batch_update_delay, 
            self._perform_batch_update
        )
        self._batch_timer.start()
    
    def _perform_batch_update(self):
        """执行批量更新"""
        with self._update_lock:
            if self.display_state.pending_updates:
                events = self.display_state.pending_updates.copy()
                self.display_state.pending_updates.clear()
                self._perform_display_update(events)
    
    def _perform_display_update(self, events: List[DataChangeEvent]):
        """执行显示更新"""
        if self.display_state.is_updating:
            if self.debug_mode:
                print("⏳ 显示更新进行中，跳过")
            return
        
        try:
            self.display_state.is_updating = True
            self.display_state.update_count += 1
            
            if self.debug_mode:
                print(f"🎨 开始显示更新 #{self.display_state.update_count}")
                print(f"   处理 {len(events)} 个变化事件")
            
            # 1. 进入配色系统进行颜色匹配
            self._update_color_matching(events)
            
            # 2. 确定输出显示到全图预览
            self._update_overview_display(events)
            
            # 3. 更新其他相关显示
            self._update_related_displays(events)
            
            # 4. 刷新画布
            self._refresh_canvas()
            
            self.display_state.last_update_time = datetime.now()
            
            if self.debug_mode:
                print(f"✅ 显示更新完成 #{self.display_state.update_count}")
                
        except Exception as e:
            print(f"❌ 显示更新失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.display_state.is_updating = False
    
    def _update_color_matching(self, events: List[DataChangeEvent]):
        """更新配色匹配"""
        try:
            # 检查是否有配色相关的变化
            color_events = [e for e in events if e.change_type in [
                DataChangeType.COLOR_SCHEME, 
                DataChangeType.GROUP_TYPE,
                DataChangeType.GROUP_DATA
            ]]
            
            if not color_events:
                return
            
            if self.debug_mode:
                print(f"🎨 更新配色匹配: {len(color_events)} 个事件")
            
            # 清空颜色缓存
            self.color_cache.clear()
            
            # 如果有配色系统，更新配色
            if self.color_system:
                for event in color_events:
                    self._apply_color_matching(event)
                    
        except Exception as e:
            print(f"❌ 配色匹配更新失败: {e}")
    
    def _apply_color_matching(self, event: DataChangeEvent):
        """应用配色匹配（修复版 - 安全解包数据）"""
        try:
            if event.change_type == DataChangeType.COLOR_SCHEME:
                # 配色方案变化
                if hasattr(self.color_system, 'current_color_scheme'):
                    self.color_system.current_color_scheme.update(event.data)

            elif event.change_type == DataChangeType.GROUP_TYPE:
                # 组类型变化，更新对应颜色
                # 🔧 修复：安全解包数据，避免 "too many values to unpack" 错误
                try:
                    if isinstance(event.data, (tuple, list)) and len(event.data) >= 2:
                        group_index, new_type = event.data[:2]  # 只取前两个值
                        if hasattr(self.color_system, 'get_category_color'):
                            color = self.color_system.get_category_color(new_type)
                            self.color_cache[f"group_{group_index}"] = color
                    else:
                        print(f"⚠️ GROUP_TYPE事件数据格式不正确: {event.data}")
                except (ValueError, TypeError) as e:
                    print(f"⚠️ GROUP_TYPE事件数据解包失败: {e}, 数据: {event.data}")

        except Exception as e:
            print(f"❌ 应用配色匹配失败: {e}")

    def _update_overview_display(self, events: List[DataChangeEvent]):
        """更新全图预览显示"""
        try:
            if not self.visualizer:
                if self.debug_mode:
                    print("⚠️ 可视化器未设置，跳过全图预览更新")
                return

            if self.debug_mode:
                print("🌍 更新全图预览显示")

            # 获取当前显示数据
            display_data = self._collect_display_data(events)

            # 调用可视化器更新全图预览
            if hasattr(self.visualizer, 'visualize_overview'):
                self.visualizer.visualize_overview(
                    all_entities=display_data.get('all_entities', []),
                    current_group_entities=display_data.get('current_group', None),
                    labeled_entities=display_data.get('labeled_entities', []),
                    processor=display_data.get('processor', None),
                    current_group_index=display_data.get('current_group_index', None),
                    wall_fills=display_data.get('wall_fills', None),
                    wall_fill_processor=display_data.get('wall_fill_processor', None),
                    hidden_groups=display_data.get('hidden_groups', None)
                )

        except Exception as e:
            print(f"❌ 全图预览更新失败: {e}")

    def _update_related_displays(self, events: List[DataChangeEvent]):
        """更新相关显示"""
        try:
            # 更新详细视图
            self._update_detail_view(events)

            # 更新房间显示
            self._update_room_display(events)

            # 更新填充显示
            self._update_fill_display(events)

        except Exception as e:
            print(f"❌ 相关显示更新失败: {e}")

    def _update_detail_view(self, events: List[DataChangeEvent]):
        """更新详细视图"""
        try:
            # 检查是否有需要更新详细视图的事件
            detail_events = [e for e in events if e.change_type in [
                DataChangeType.GROUP_DATA,
                DataChangeType.GROUP_TYPE
            ]]

            if not detail_events or not self.visualizer:
                return

            if self.debug_mode:
                print("🔍 更新详细视图")

            # 获取最新的组数据（避免重复更新）
            latest_group_data = None
            latest_metadata = None

            for event in detail_events:
                if event.change_type == DataChangeType.GROUP_DATA:
                    latest_group_data = event.data
                    latest_metadata = event.metadata

            # 只使用最新的组数据进行一次更新
            if latest_group_data and hasattr(self.visualizer, 'visualize_entity_group'):
                self.visualizer.visualize_entity_group(
                    latest_group_data,
                    category_mapping=getattr(latest_metadata, 'category_mapping', None) if latest_metadata else None
                )

        except Exception as e:
            print(f"❌ 详细视图更新失败: {e}")

    def _update_room_display(self, events: List[DataChangeEvent]):
        """更新房间显示"""
        try:
            # 检查是否有房间相关的变化
            room_events = [e for e in events if e.change_type == DataChangeType.ROOM_FILL]

            if not room_events:
                return

            if self.debug_mode:
                print("🏠 更新房间显示")

            # 处理房间填充更新
            for event in room_events:
                room_data = event.data
                # 这里可以调用房间显示更新逻辑
                if hasattr(event.metadata, 'room_processor'):
                    room_processor = event.metadata['room_processor']
                    if hasattr(room_processor, 'update_display'):
                        room_processor.update_display(room_data)

        except Exception as e:
            print(f"❌ 房间显示更新失败: {e}")

    def _update_fill_display(self, events: List[DataChangeEvent]):
        """更新填充显示"""
        try:
            # 检查是否有填充相关的变化
            fill_events = [e for e in events if e.change_type in [
                DataChangeType.GROUP_FILL,
                DataChangeType.WALL_FILL
            ]]

            if not fill_events:
                return

            if self.debug_mode:
                print("🎨 更新填充显示")

            # 处理填充更新
            for event in fill_events:
                if event.change_type == DataChangeType.WALL_FILL:
                    # 墙体填充更新
                    wall_fills = event.data
                    if hasattr(event.metadata, 'wall_fill_processor'):
                        wall_fill_processor = event.metadata['wall_fill_processor']
                        # 更新墙体填充显示
                        self._update_wall_fill_display(wall_fills, wall_fill_processor)

                elif event.change_type == DataChangeType.GROUP_FILL:
                    # 组填充更新
                    group_fills = event.data
                    self._update_group_fill_display(group_fills)

        except Exception as e:
            print(f"❌ 填充显示更新失败: {e}")

    def _update_wall_fill_display(self, wall_fills, wall_fill_processor):
        """更新墙体填充显示"""
        try:
            if self.visualizer and hasattr(self.visualizer, 'visualize_overview'):
                # 通过全图预览显示墙体填充
                display_data = self._get_current_display_data()
                display_data['wall_fills'] = wall_fills
                display_data['wall_fill_processor'] = wall_fill_processor

                self.visualizer.visualize_overview(**display_data)

        except Exception as e:
            print(f"❌ 墙体填充显示更新失败: {e}")

    def _update_group_fill_display(self, group_fills):
        """更新组填充显示"""
        try:
            if self.debug_mode:
                print(f"🎨 更新组填充显示: {len(group_fills) if group_fills else 0} 个组")

            # 这里可以添加组填充的特殊显示逻辑
            # 目前通过全图预览统一处理

        except Exception as e:
            print(f"❌ 组填充显示更新失败: {e}")

    def _refresh_canvas(self):
        """刷新画布"""
        try:
            if self.canvas:
                if hasattr(self.canvas, 'draw'):
                    self.canvas.draw()
                elif hasattr(self.canvas, 'update'):
                    self.canvas.update()

                if self.debug_mode:
                    print("🖼️ 画布已刷新")
            else:
                if self.debug_mode:
                    print("⚠️ 画布未设置，跳过刷新")

        except Exception as e:
            print(f"❌ 画布刷新失败: {e}")

    def _collect_display_data(self, events: List[DataChangeEvent]) -> Dict[str, Any]:
        """收集显示数据"""
        display_data = {}

        try:
            # 从事件中收集数据
            for event in events:
                if event.change_type == DataChangeType.DXF_FILE_DATA:
                    display_data['all_entities'] = event.data
                elif event.change_type == DataChangeType.GROUP_DATA:
                    display_data['current_group'] = event.data
                elif event.change_type == DataChangeType.WALL_FILL:
                    display_data['wall_fills'] = event.data
                    if 'wall_fill_processor' in event.metadata:
                        display_data['wall_fill_processor'] = event.metadata['wall_fill_processor']

                # 从元数据中获取其他信息
                if event.metadata:
                    for key, value in event.metadata.items():
                        if key not in display_data:
                            display_data[key] = value

            # 如果没有从事件中获取到数据，尝试从当前状态获取
            if not display_data:
                display_data = self._get_current_display_data()

        except Exception as e:
            print(f"❌ 收集显示数据失败: {e}")
            display_data = self._get_current_display_data()

        return display_data

    def _get_current_display_data(self) -> Dict[str, Any]:
        """获取当前显示数据"""
        # 这个方法需要根据实际的数据源来实现
        # 这里提供一个基础框架
        return {
            'all_entities': [],
            'current_group': None,
            'labeled_entities': [],
            'processor': None,
            'current_group_index': None,
            'wall_fills': None,
            'wall_fill_processor': None,
            'hidden_groups': None
        }

    def set_data_source(self, data_source):
        """设置数据源"""
        self.data_source = data_source
        if self.debug_mode:
            print("📊 数据源已设置")

    def set_visualizer(self, visualizer):
        """设置可视化器"""
        self.visualizer = visualizer
        if self.debug_mode:
            print("🎨 可视化器已设置")

    def set_canvas(self, canvas):
        """设置画布"""
        self.canvas = canvas
        if self.debug_mode:
            print("🖼️ 画布已设置")

    def set_color_system(self, color_system):
        """设置配色系统"""
        self.color_system = color_system
        if self.debug_mode:
            print("🌈 配色系统已设置")

    def enable_debug_mode(self, enabled: bool = True):
        """启用/禁用调试模式"""
        self.debug_mode = enabled
        print(f"🐛 调试模式: {'启用' if enabled else '禁用'}")

    def get_display_stats(self) -> Dict[str, Any]:
        """获取显示统计信息"""
        return {
            'update_count': self.display_state.update_count,
            'last_update_time': self.display_state.last_update_time,
            'pending_updates': len(self.display_state.pending_updates),
            'is_updating': self.display_state.is_updating,
            'listeners_count': sum(len(listeners) for listeners in self.change_listeners.values()),
            'color_cache_size': len(self.color_cache)
        }

    def force_update(self):
        """强制更新显示"""
        if self.debug_mode:
            print("🔄 强制更新显示")

        # 创建一个通用更新事件
        event = DataChangeEvent(
            change_type=DataChangeType.DXF_FILE_DATA,
            data=None,
            source="force_update"
        )

        self._perform_display_update([event])

    def clear_cache(self):
        """清空缓存"""
        self.color_cache.clear()
        if hasattr(self, '_last_color_scheme'):
            delattr(self, '_last_color_scheme')
        if hasattr(self, '_last_group_hash'):
            delattr(self, '_last_group_hash')
        if hasattr(self, '_last_fill_hash'):
            delattr(self, '_last_fill_hash')

        if self.debug_mode:
            print("🧹 缓存已清空")

    def shutdown(self):
        """关闭显示控制器"""
        try:
            # 取消定时器
            if self._batch_timer:
                self._batch_timer.cancel()

            # 清空待处理更新
            self.display_state.pending_updates.clear()

            # 清空监听器
            self.change_listeners.clear()

            # 清空缓存
            self.clear_cache()

            if self.debug_mode:
                print("🔚 显示控制器已关闭")

        except Exception as e:
            print(f"❌ 关闭显示控制器失败: {e}")

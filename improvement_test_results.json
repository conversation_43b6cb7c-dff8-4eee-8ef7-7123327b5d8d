{"test_time": 1753713759.9432995, "merger_test_result": {"input_entities": 7, "output_entities": 7, "input_layers": 6, "output_layers": 6, "layer_preservation_rate": 1.0, "validation_passed": true, "processing_time": 0.01003408432006836, "merge_stats": {"merge_stats": {"total_entities_input": 7, "total_entities_output": 7, "layers_processed": 6, "layer_details": {"A-WALL": {"input_count": 2, "output_count": 2, "merge_enabled": true, "config_used": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}}, "CONSTRUCTION": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-DOOR": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}}, "EQUIPMENT": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-ANNO-TEXT": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}}, "A-DIMS": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}}, "processing_time": 0.009026765823364258, "cross_layer_prevention_count": 0}, "layer_configs": {"wall_layers": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}, "door_window_layers": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}, "railing_layers": {"patterns": ["rail", "栏杆", "护栏", "a-rail"], "merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": true}, "text_layers": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}, "dimension_layers": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}, "cross_layer_prevention": {"enabled": true, "prevention_count": 0, "description": "通过按图层分组处理，完全避免跨图层合并"}}}, "processor_test_result": {"traditional_result": {"success": true, "entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9622543, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[100, 0], [100, 100]], "start_point": [100, 0], "end_point": [100, 100], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9622543, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.965254, "layer_preserved": true, "color": 9}, {"type": "LINE", "layer": "A-DOOR", "original_layer": "A-DOOR", "points": [[30, 0], [50, 0]], "start_point": [30, 0], "end_point": [50, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9672916, "layer_preserved": true, "color": 2}, {"type": "LINE", "layer": "EQUIPMENT", "original_layer": "EQUIPMENT", "points": [[100, 0], [120, 0]], "start_point": [100, 0], "end_point": [120, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9702861, "layer_preserved": true, "color": 8}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "original_layer": "A-ANNO-TEXT", "layer_preserved": true, "processed_by": "LayerAwareLineMerger", "processing_timestamp": 1753713759.9702861, "merge_skipped_reason": "preserve_original"}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "original_layer": "A-DIMS", "layer_preserved": true, "processed_by": "LayerAwareLineMerger", "processing_timestamp": 1753713759.9712505, "merge_skipped_reason": "preserve_original"}], "groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9622543, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[100, 0], [100, 100]], "start_point": [100, 0], "end_point": [100, 100], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9622543, "layer_preserved": true, "color": 1}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.965254, "layer_preserved": true, "color": 9}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "original_layer": "A-DOOR", "points": [[30, 0], [50, 0]], "start_point": [30, 0], "end_point": [50, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713759.9672916, "layer_preserved": true, "color": 2}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}], "labeled_entities": [], "auto_labeled_entities": [], "processing_mode": "traditional_improved", "merge_statistics": {"merge_stats": {"total_entities_input": 7, "total_entities_output": 7, "layers_processed": 6, "layer_details": {"A-WALL": {"input_count": 2, "output_count": 2, "merge_enabled": true, "config_used": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}}, "CONSTRUCTION": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-DOOR": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}}, "EQUIPMENT": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-ANNO-TEXT": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}}, "A-DIMS": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}}, "processing_time": 0.010957717895507812, "cross_layer_prevention_count": 0}, "layer_configs": {"wall_layers": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}, "door_window_layers": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}, "railing_layers": {"patterns": ["rail", "栏杆", "护栏", "a-rail"], "merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": true}, "text_layers": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}, "dimension_layers": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}, "cross_layer_prevention": {"enabled": true, "prevention_count": 0, "description": "通过按图层分组处理，完全避免跨图层合并"}}, "total_entities": 7, "total_groups": 3, "total_labeled": 0, "validation": {"is_valid": true, "errors": [], "warnings": [], "layer_integrity": {"original_layers": 6, "processed_layers": 6, "missing_layers": [], "preservation_rate": 1.0}, "cross_layer_check": {"total_groups": 3, "cross_layer_groups": 0, "cross_layer_rate": 0.0, "is_clean": true}}}, "enhanced_result": {"success": true, "entities": [{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267}, {"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 5.0, "angle_threshold": 2.0, "processing_timestamp": 1753713759.9842768, "processing_stage": "processed"}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 5.0, "angle_threshold": 2.0, "processing_timestamp": 1753713759.9842768, "processing_stage": "processed"}, {"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.999267, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267, "line_processed": true, "processing_type": "basic_process", "layer_type_confirmed": "other", "processing_timestamp": 1753713759.985276, "processing_stage": "processed"}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9932723, "processing_stage": "processed", "group_index": 0}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267, "line_processed": true, "processing_type": "line_merged", "merge_threshold": 10.0, "angle_threshold": 5.0, "processing_timestamp": 1753713759.985276, "processing_stage": "processed"}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713760.000267, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753713759.9942725, "processing_stage": "processed", "group_index": 0}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0012681}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0012681, "line_processed": true, "processing_type": "basic_process", "layer_type_confirmed": "other", "processing_timestamp": 1753713759.9862423, "processing_stage": "processed"}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0012681, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9952796, "processing_stage": "processed", "group_index": 0}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "text", "processing_timestamp": 1753713759.9872744, "processing_stage": "processed"}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753713759.9962714, "processing_stage": "processed", "group_index": 0}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678, "line_processed": true, "processing_type": "preserve_original", "layer_type_confirmed": "dimension", "processing_timestamp": 1753713759.9872744, "processing_stage": "processed"}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713760.0022678, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753713759.997236, "processing_stage": "processed", "group_index": 0}], "groups": [[{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0}], [{"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9932723, "processing_stage": "processed", "group_index": 0}], [{"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753713759.9942725, "processing_stage": "processed", "group_index": 0}], [{"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9952796, "processing_stage": "processed", "group_index": 0}], [{"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753713759.9962714, "processing_stage": "processed", "group_index": 0}], [{"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713759.9912806, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753713759.997236, "processing_stage": "processed", "group_index": 0}]], "labeled_entities": [{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9932723, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753713759.9942725, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9952796, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753713759.9962714, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713759.9912806, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753713759.997236, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}], "auto_labeled_entities": [{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]], "container_layer_type": "wall", "container_layer_name": "A-WALL", "original_layer": "A-WALL", "type_information_verified": true, "type_verification_timestamp": 1753713759.9882753, "processed_by": "WallLayerProcessor", "entity_category": "wall", "processing_timestamp": 1753713759.992238, "processing_stage": "processed", "group_index": 0, "label": "wall", "auto_labeled": true, "confidence": 0.9, "labeled_by": "WallLayerProcessor"}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]], "container_layer_type": "other", "container_layer_name": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9932723, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]], "container_layer_type": "door_window", "container_layer_name": "A-DOOR", "original_layer": "A-DOOR", "type_information_verified": true, "type_verification_timestamp": 1753713759.9892764, "processed_by": "DoorWindowLayerProcessor", "entity_category": "door_window", "processing_timestamp": 1753713759.9942725, "processing_stage": "processed", "group_index": 0, "label": "door_window", "auto_labeled": true, "confidence": 0.85, "labeled_by": "DoorWindowLayerProcessor"}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]], "container_layer_type": "other", "container_layer_name": "EQUIPMENT", "original_layer": "EQUIPMENT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "OtherLayerProcessor", "entity_category": "other", "processing_timestamp": 1753713759.9952796, "processing_stage": "processed", "group_index": 0, "label": "other", "auto_labeled": true, "confidence": 0.5, "labeled_by": "OtherLayerProcessor"}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "container_layer_type": "text", "container_layer_name": "A-ANNO-TEXT", "original_layer": "A-ANNO-TEXT", "type_information_verified": true, "type_verification_timestamp": 1753713759.9902737, "processed_by": "TextLayerProcessor", "entity_category": "text", "processing_timestamp": 1753713759.9962714, "processing_stage": "processed", "group_index": 0, "label": "text", "auto_labeled": true, "confidence": 0.95, "labeled_by": "TextLayerProcessor"}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "container_layer_type": "dimension", "container_layer_name": "A-DIMS", "original_layer": "A-DIMS", "type_information_verified": true, "type_verification_timestamp": 1753713759.9912806, "processed_by": "DimensionLayerProcessor", "entity_category": "dimension", "processing_timestamp": 1753713759.997236, "processing_stage": "processed", "group_index": 0, "label": "dimension", "auto_labeled": true, "confidence": 0.9, "labeled_by": "DimensionLayerProcessor"}], "processing_mode": "enhanced_architecture", "enhanced_data": {"containers": ["LayerDataContainer(name='A-WALL', type='wall', entities=6, groups=1, stage='labeled')", "LayerDataContainer(name='CONSTRUCTION', type='other', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-DOOR', type='door_window', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='EQUIPMENT', type='other', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-ANNO-TEXT', type='text', entities=3, groups=1, stage='labeled')", "LayerDataContainer(name='A-DIMS', type='dimension', entities=3, groups=1, stage='labeled')"], "layer_summary": {"A-WALL": {"layer_type": "wall", "entity_count": 6, "group_count": 1, "labeled_count": 2, "processing_summary": {"layer_info": {"name": "A-WALL", "type": "wall", "creation_time": 1753713759.999267}, "data_counts": {"original_entities": 2, "processed_entities": 4, "groups": 1, "labeled_entities": 2}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.978282, "input_count": 2, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9842768, "input_count": 2, "output_count": 4}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.992238, "input_count": 2, "output_count": 6}, {"operation": "set_groups", "processor": "WallLayerProcessor", "timestamp": 1753713759.9932723, "input_count": 6, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "WallLayerProcessor", "timestamp": 1753713759.9932723, "input_count": 6, "output_count": 2}]}}, "CONSTRUCTION": {"layer_type": "other", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "CONSTRUCTION", "type": "other", "creation_time": 1753713759.999267}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.9792466, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.985276, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9932723, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "OtherLayerProcessor", "timestamp": 1753713759.9932723, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "OtherLayerProcessor", "timestamp": 1753713759.9932723, "input_count": 3, "output_count": 1}]}}, "A-DOOR": {"layer_type": "door_window", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-DOOR", "type": "door_window", "creation_time": 1753713760.000267}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.9792466, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9862423, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9942725, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "DoorWindowLayerProcessor", "timestamp": 1753713759.9942725, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DoorWindowLayerProcessor", "timestamp": 1753713759.9942725, "input_count": 3, "output_count": 1}]}}, "EQUIPMENT": {"layer_type": "other", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "EQUIPMENT", "type": "other", "creation_time": 1753713760.0012681}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.9792466, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9862423, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9952796, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "OtherLayerProcessor", "timestamp": 1753713759.9952796, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "OtherLayerProcessor", "timestamp": 1753713759.9962714, "input_count": 3, "output_count": 1}]}}, "A-ANNO-TEXT": {"layer_type": "text", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-ANNO-TEXT", "type": "text", "creation_time": 1753713760.0022678}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.9792466, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9872744, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9962714, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "TextLayerProcessor", "timestamp": 1753713759.9962714, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "TextLayerProcessor", "timestamp": 1753713759.9962714, "input_count": 3, "output_count": 1}]}}, "A-DIMS": {"layer_type": "dimension", "entity_count": 3, "group_count": 1, "labeled_count": 1, "processing_summary": {"layer_info": {"name": "A-DIMS", "type": "dimension", "creation_time": 1753713760.0022678}, "data_counts": {"original_entities": 1, "processed_entities": 2, "groups": 1, "labeled_entities": 1}, "processing_status": {"is_processed": true, "is_grouped": true, "is_labeled": true, "current_stage": "labeled"}, "processing_history": [{"operation": "initialize", "processor": "LayerDataContainer", "timestamp": 1753713759.9792466, "input_count": 1, "output_count": 1}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.9872744, "input_count": 1, "output_count": 2}, {"operation": "add_entities_processed", "processor": "LayerDataContainer", "timestamp": 1753713759.997236, "input_count": 1, "output_count": 3}, {"operation": "set_groups", "processor": "DimensionLayerProcessor", "timestamp": 1753713759.997236, "input_count": 3, "output_count": 1}, {"operation": "set_labeled_entities", "processor": "DimensionLayerProcessor", "timestamp": 1753713759.997236, "input_count": 3, "output_count": 1}]}}}, "integrity_check": {"is_valid": true, "errors": [], "warnings": [], "statistics": {"total_entities": 21, "total_groups": 6, "total_labeled": 7, "group_entity_count": 7, "entities_without_type": 0, "entities_without_layer": 0}}, "processing_summary": {"pipeline_summary": {"pipeline_stats": {"total_containers_processed": 6, "total_entities_processed": 0, "total_processing_time": 0.006029844284057617, "processor_stats": {"WallLayerProcessor": {"processed_containers": 1, "total_entities": 6, "processing_time": 0.0010342597961425781}, "OtherLayerProcessor": {"processed_containers": 3, "total_entities": 9, "processing_time": 0.0009918212890625}, "DoorWindowLayerProcessor": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}, "TextLayerProcessor": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}, "DimensionLayerProcessor": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}}}, "available_processors": ["LayerType.WALL", "LayerType.DOOR_WINDOW", "LayerType.RAILING", "LayerType.TEXT", "LayerType.DIMENSION", "LayerType.OTHER"], "processor_details": {"wall": {"processed_containers": 1, "total_entities": 6, "processing_time": 0.0010342597961425781}, "door_window": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}, "railing": {"processed_containers": 0, "total_entities": 0, "processing_time": 0.0}, "text": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}, "dimension": {"processed_containers": 1, "total_entities": 3, "processing_time": 0.0}, "other": {"processed_containers": 2, "total_entities": 6, "processing_time": 0.0009918212890625}}}, "line_processor_summary": {"processing_stats": {"total_entities_processed": 7, "layers_processed": 6, "processing_time": 0.003997087478637695, "layer_stats": {"A-WALL": {"entities_processed": 2, "processing_time": 0.00099945068359375}, "CONSTRUCTION": {"entities_processed": 1, "processing_time": 0.0009992122650146484}, "A-DOOR": {"entities_processed": 1, "processing_time": 0.0009663105010986328}, "EQUIPMENT": {"entities_processed": 1, "processing_time": 0.0}, "A-ANNO-TEXT": {"entities_processed": 1, "processing_time": 0.001032114028930664}, "A-DIMS": {"entities_processed": 1, "processing_time": 0.0}}}, "layer_configs": {"wall": {"merge_threshold": 5.0, "angle_threshold": 2.0, "enable_merging": true, "connection_precision": "high"}, "door_window": {"merge_threshold": 10.0, "angle_threshold": 5.0, "enable_merging": true, "connection_precision": "medium"}, "railing": {"merge_threshold": 8.0, "angle_threshold": 3.0, "enable_merging": true, "connection_precision": "medium"}, "text": {"enable_merging": false, "preserve_original": true}, "dimension": {"enable_merging": false, "preserve_original": true}, "other": {"merge_threshold": 15.0, "angle_threshold": 10.0, "enable_merging": false, "connection_precision": "low"}}}, "transfer_summary": {"transfer_stats": {"total_transfers": 18, "successful_transfers": 6, "failed_transfers": 12, "data_integrity_errors": 0, "type_information_losses": 0}, "total_records": 18, "registered_containers": 18, "recent_transfers": [{"timestamp": 1753713759.9892764, "source_stage": "line_processing", "target_stage": "entity_grouping", "container_id": "fbc4d3b4c087", "validation_passed": false, "entity_count": 2}, {"timestamp": 1753713759.9902737, "source_stage": "line_processing", "target_stage": "entity_grouping", "container_id": "7a8d3ad19c45", "validation_passed": false, "entity_count": 2}, {"timestamp": 1753713759.9902737, "source_stage": "line_processing", "target_stage": "entity_grouping", "container_id": "622dbd3f5802", "validation_passed": false, "entity_count": 2}, {"timestamp": 1753713759.9912806, "source_stage": "line_processing", "target_stage": "entity_grouping", "container_id": "0455367b6d29", "validation_passed": false, "entity_count": 2}, {"timestamp": 1753713759.998268, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "4439e564716a", "validation_passed": true, "entity_count": 6}, {"timestamp": 1753713759.999267, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "33234b97dcd4", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753713760.000267, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "725b8dc49ca7", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753713760.0012681, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "520c34ca35af", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753713760.0022678, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "fe5cefbf1dd0", "validation_passed": true, "entity_count": 3}, {"timestamp": 1753713760.0022678, "source_stage": "entity_grouping", "target_stage": "auto_labeling", "container_id": "aa2e7761f031", "validation_passed": true, "entity_count": 3}]}, "manager_stats": {"total_files_processed": 1, "total_entities_processed": 7, "total_containers_created": 6, "total_processing_time": 0.02598404884338379, "stage_times": {"container_creation": 0.003994941711425781, "line_processing": 0.00800323486328125, "entity_grouping": 0.011033296585083008, "integration_validation": 0.0009946823120117188}}}}, "validation": {"is_valid": true, "errors": [], "warnings": [], "layer_integrity": {"original_layers": 6, "processed_layers": 6, "missing_layers": [], "preservation_rate": 1.0}, "cross_layer_check": {"total_groups": 6, "cross_layer_groups": 0, "cross_layer_rate": 0.0, "is_clean": true}}}, "traditional_time": 0.015024185180664062, "enhanced_time": 0.027016401290893555, "time_ratio": 1.7981941094325251}, "integration_test_result": {"enhanced_result": {"success": true, "entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.013263, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[100, 0], [100, 100]], "start_point": [100, 0], "end_point": [100, 100], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.013263, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.0142603, "layer_preserved": true, "color": 9}, {"type": "LINE", "layer": "A-DOOR", "original_layer": "A-DOOR", "points": [[30, 0], [50, 0]], "start_point": [30, 0], "end_point": [50, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.0162857, "layer_preserved": true, "color": 2}, {"type": "LINE", "layer": "EQUIPMENT", "original_layer": "EQUIPMENT", "points": [[100, 0], [120, 0]], "start_point": [100, 0], "end_point": [120, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.0182714, "layer_preserved": true, "color": 8}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5, "original_layer": "A-ANNO-TEXT", "layer_preserved": true, "processed_by": "LayerAwareLineMerger", "processing_timestamp": 1753713760.0182714, "merge_skipped_reason": "preserve_original"}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6, "original_layer": "A-DIMS", "layer_preserved": true, "processed_by": "LayerAwareLineMerger", "processing_timestamp": 1753713760.0182714, "merge_skipped_reason": "preserve_original"}], "groups": [{"entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.013263, "layer_preserved": true, "color": 1}, {"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[100, 0], [100, 100]], "start_point": [100, 0], "end_point": [100, 100], "merged_from_count": 2, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.013263, "layer_preserved": true, "color": 1}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "CONSTRUCTION", "original_layer": "CONSTRUCTION", "points": [[0, 0], [100, 0]], "start_point": [0, 0], "end_point": [100, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.0142603, "layer_preserved": true, "color": 9}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"type": "LINE", "layer": "A-DOOR", "original_layer": "A-DOOR", "points": [[30, 0], [50, 0]], "start_point": [30, 0], "end_point": [50, 0], "merged_from_count": 1, "merged_by": "LayerAwareLineMerger", "merge_timestamp": 1753713760.0162857, "layer_preserved": true, "color": 2}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}], "labeled_entities": [], "auto_labeled_entities": [], "processing_mode": "traditional_improved", "merge_statistics": {"merge_stats": {"total_entities_input": 7, "total_entities_output": 7, "layers_processed": 6, "layer_details": {"A-WALL": {"input_count": 2, "output_count": 2, "merge_enabled": true, "config_used": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}}, "CONSTRUCTION": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-DOOR": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}}, "EQUIPMENT": {"input_count": 1, "output_count": 1, "merge_enabled": true, "config_used": {"merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": false}}, "A-ANNO-TEXT": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}}, "A-DIMS": {"input_count": 1, "output_count": 1, "merge_enabled": false, "config_used": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}}, "processing_time": 0.008041143417358398, "cross_layer_prevention_count": 0}, "layer_configs": {"wall_layers": {"patterns": ["wall", "墙", "a-wall", "arch-wall"], "merge_enabled": true, "distance_threshold": 3, "angle_threshold": 1, "preserve_endpoints": true}, "door_window_layers": {"patterns": ["door", "window", "门", "窗", "a-door", "a-wind"], "merge_enabled": true, "distance_threshold": 8, "angle_threshold": 3, "preserve_endpoints": false}, "railing_layers": {"patterns": ["rail", "栏杆", "护栏", "a-rail"], "merge_enabled": true, "distance_threshold": 5, "angle_threshold": 2, "preserve_endpoints": true}, "text_layers": {"patterns": ["text", "文字", "标注", "a-anno", "anno"], "merge_enabled": false, "preserve_original": true}, "dimension_layers": {"patterns": ["dim", "dimension", "尺寸", "a-dims"], "merge_enabled": false, "preserve_original": true}}, "cross_layer_prevention": {"enabled": true, "prevention_count": 0, "description": "通过按图层分组处理，完全避免跨图层合并"}}, "total_entities": 7, "total_groups": 3, "total_labeled": 0, "validation": {"is_valid": true, "errors": [], "warnings": [], "layer_integrity": {"original_layers": 6, "processed_layers": 6, "missing_layers": [], "preservation_rate": 1.0}, "cross_layer_check": {"total_groups": 3, "cross_layer_groups": 0, "cross_layer_rate": 0.0, "is_clean": true}}, "integration_info": {"processor_used": "enhanced", "integration_version": "1.0", "processing_timestamp": 1753713760.0202596}}, "fallback_result": {"success": true, "entities": [{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]]}, {"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]]}, {"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]]}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]]}, {"id": "equipment_extension", "type": "LINE", "layer": "EQUIPMENT", "start_point": [100, 0], "end_point": [120, 0], "color": 8, "points": [[100, 0], [120, 0]]}, {"id": "room_text", "type": "TEXT", "layer": "A-ANNO-TEXT", "text": "房间1", "position": [50, 50], "height": 5, "color": 5}, {"id": "wall_dimension", "type": "DIMENSION", "layer": "A-DIMS", "def_points": [[0, 0], [100, 0], [50, -20]], "text": "100", "color": 6}], "groups": [{"entities": [{"id": "wall_main", "type": "LINE", "layer": "A-WALL", "start_point": [0, 0], "end_point": [100, 0], "color": 1, "points": [[0, 0], [100, 0]]}, {"id": "wall_perpendicular", "type": "LINE", "layer": "A-WALL", "start_point": [100, 0], "end_point": [100, 100], "color": 1, "points": [[100, 0], [100, 100]]}], "label": "wall_A-WALL_0", "group_type": "wall", "layer": "A-WALL", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "construction_overlay", "type": "LINE", "layer": "CONSTRUCTION", "start_point": [0, 0], "end_point": [100, 0], "color": 9, "points": [[0, 0], [100, 0]]}], "label": "wall_CONSTRUCTION_0", "group_type": "wall", "layer": "CONSTRUCTION", "status": "pending", "confidence": 0.8}, {"entities": [{"id": "door_opening", "type": "LINE", "layer": "A-DOOR", "start_point": [30, 0], "end_point": [50, 0], "color": 2, "points": [[30, 0], [50, 0]]}], "label": "door_window_A-DOOR_0", "group_type": "door_window", "layer": "A-DOOR", "status": "pending", "confidence": 0.8}], "labeled_entities": [], "auto_labeled_entities": [], "processing_mode": "traditional", "total_entities": 7, "total_groups": 3, "total_labeled": 0, "integration_info": {"processor_used": "traditional", "integration_version": "1.0", "processing_timestamp": 1753713760.0222557}}, "enhanced_time": 0.011030197143554688, "fallback_time": 0.0009968280792236328, "integration_stats": {"enhanced_usage_count": 1, "traditional_usage_count": 1, "fallback_count": 0, "total_processing_time": 0.01202702522277832, "current_mode": "enhanced"}}, "summary": {"total_test_time": 0.07995939254760742, "all_tests_passed": true, "layer_preservation_achieved": true, "cross_layer_prevention_working": true, "integration_successful": true}}
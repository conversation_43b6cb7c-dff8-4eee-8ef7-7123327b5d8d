#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
选择性图层处理器
实现差异化的图层处理策略：
1. 墙体图层：进行迭代合并
2. 其他图层：进行去重处理
"""

import copy
import time
import hashlib
from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict

try:
    from line_merger import SimpleLineMerger
    LINE_MERGER_AVAILABLE = True
except ImportError:
    LINE_MERGER_AVAILABLE = False
    print("⚠️ 原始线条合并器不可用")


class SelectiveLayerProcessor:
    """
    选择性图层处理器
    
    核心策略：
    1. 墙体图层（A-WALL, WALL等）：使用迭代合并算法
    2. 其他图层：使用去重算法，移除重复实体
    """
    
    def __init__(self, distance_threshold=5, angle_threshold=2):
        """
        初始化选择性图层处理器
        
        Args:
            distance_threshold: 距离阈值
            angle_threshold: 角度阈值
        """
        self.distance_threshold = distance_threshold
        self.angle_threshold = angle_threshold
        
        # 墙体图层识别模式
        self.wall_layer_patterns = [
            'wall', '墙', 'a-wall', 'arch-wall', 'a-wall-', 'wall-'
        ]
        
        # 处理统计
        self.processing_stats = {
            'total_entities_input': 0,
            'total_entities_output': 0,
            'wall_layers_processed': 0,
            'other_layers_processed': 0,
            'wall_entities_merged': 0,
            'other_entities_deduplicated': 0,
            'processing_time': 0.0,
            'layer_details': {}
        }
        
        print("🎯 选择性图层处理器初始化完成")
    
    def process_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理实体的主要方法
        
        Args:
            entities: 输入实体列表
            
        Returns:
            处理后的实体列表
        """
        print(f"🔄 开始选择性图层处理: {len(entities)} 个实体")
        start_time = time.time()
        
        # 重置统计
        self.processing_stats = {
            'total_entities_input': len(entities),
            'total_entities_output': 0,
            'wall_layers_processed': 0,
            'other_layers_processed': 0,
            'wall_entities_merged': 0,
            'other_entities_deduplicated': 0,
            'processing_time': 0.0,
            'layer_details': {}
        }
        
        # 按图层分组
        layer_groups = self._group_entities_by_layer(entities)
        
        print(f"   📋 发现 {len(layer_groups)} 个图层")
        
        processed_entities = []
        
        for layer_name, layer_entities in layer_groups.items():
            print(f"   🔧 处理图层: {layer_name} ({len(layer_entities)} 个实体)")
            
            # 判断是否为墙体图层
            is_wall_layer = self._is_wall_layer(layer_name)
            
            if is_wall_layer:
                # 墙体图层：迭代合并
                layer_processed = self._process_wall_layer(layer_entities, layer_name)
                self.processing_stats['wall_layers_processed'] += 1
                self.processing_stats['wall_entities_merged'] += len(layer_entities) - len(layer_processed)
            else:
                # 其他图层：去重处理
                layer_processed = self._process_other_layer(layer_entities, layer_name)
                self.processing_stats['other_layers_processed'] += 1
                self.processing_stats['other_entities_deduplicated'] += len(layer_entities) - len(layer_processed)
            
            processed_entities.extend(layer_processed)
            
            # 更新图层统计
            self.processing_stats['layer_details'][layer_name] = {
                'input_count': len(layer_entities),
                'output_count': len(layer_processed),
                'is_wall_layer': is_wall_layer,
                'processing_type': 'iterative_merge' if is_wall_layer else 'deduplication'
            }
        
        # 完成统计
        self.processing_stats['total_entities_output'] = len(processed_entities)
        self.processing_stats['processing_time'] = time.time() - start_time
        
        print(f"✅ 选择性图层处理完成: {len(entities)} -> {len(processed_entities)} 个实体")
        print(f"   处理时间: {self.processing_stats['processing_time']:.3f} 秒")
        print(f"   墙体图层: {self.processing_stats['wall_layers_processed']} 个")
        print(f"   其他图层: {self.processing_stats['other_layers_processed']} 个")
        
        return processed_entities
    
    def _group_entities_by_layer(self, entities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按图层分组实体"""
        layer_groups = defaultdict(list)
        
        for entity in entities:
            if isinstance(entity, dict):
                layer = entity.get('layer', 'UNKNOWN')
                # 创建实体副本，确保原始数据不被修改
                entity_copy = copy.deepcopy(entity)
                entity_copy['original_layer'] = layer
                layer_groups[layer].append(entity_copy)
        
        return dict(layer_groups)
    
    def _is_wall_layer(self, layer_name: str) -> bool:
        """判断是否为墙体图层"""
        layer_name_lower = layer_name.lower()
        return any(pattern in layer_name_lower for pattern in self.wall_layer_patterns)
    
    def _process_wall_layer(self, entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """处理墙体图层：迭代合并"""
        print(f"     🏗️ 墙体图层迭代合并处理")
        
        # 分离线条和非线条实体
        line_entities = []
        non_line_entities = []
        
        for entity in entities:
            if self._is_line_entity(entity):
                line_entities.append(entity)
            else:
                non_line_entities.append(entity)
        
        print(f"       线条实体: {len(line_entities)}, 非线条实体: {len(non_line_entities)}")
        
        # 对线条实体进行迭代合并
        if line_entities and LINE_MERGER_AVAILABLE:
            merged_lines = self._iterative_merge_lines(line_entities, layer_name)
        else:
            merged_lines = line_entities
            print(f"       ⚠️ 跳过线条合并（合并器不可用或无线条实体）")
        
        # 对非线条实体进行去重
        deduplicated_non_lines = self._deduplicate_entities(non_line_entities, layer_name)
        
        # 合并结果
        result = merged_lines + deduplicated_non_lines
        
        print(f"       合并结果: {len(line_entities)} 线条 -> {len(merged_lines)}, "
              f"{len(non_line_entities)} 其他 -> {len(deduplicated_non_lines)}")
        
        return result
    
    def _process_other_layer(self, entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """处理其他图层：去重处理"""
        print(f"     🔄 其他图层去重处理")
        
        # 对所有实体进行去重
        deduplicated_entities = self._deduplicate_entities(entities, layer_name)
        
        print(f"       去重结果: {len(entities)} -> {len(deduplicated_entities)} 个实体")
        
        return deduplicated_entities
    
    def _iterative_merge_lines(self, line_entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """迭代合并线条实体"""
        if not line_entities:
            return []
        
        print(f"       🔄 开始迭代合并线条...")
        
        # 提取线条坐标
        line_coords = []
        entity_map = {}
        
        for i, entity in enumerate(line_entities):
            coords = self._extract_line_coordinates(entity)
            if coords:
                line_coords.append(coords)
                entity_map[i] = entity
        
        if not line_coords:
            return line_entities
        
        # 使用线条合并器进行迭代合并
        merger = SimpleLineMerger(
            distance_threshold=self.distance_threshold,
            angle_threshold=self.angle_threshold
        )
        
        try:
            merged_coords = merger.merge_lines(line_coords)
            
            # 重建实体，保留图层信息
            merged_entities = []
            for i, coords_obj in enumerate(merged_coords):
                # 处理不同类型的坐标对象
                if hasattr(coords_obj, 'coords'):
                    coords = list(coords_obj.coords)
                elif isinstance(coords_obj, (list, tuple)):
                    coords = coords_obj
                else:
                    try:
                        coords = list(coords_obj)
                    except:
                        print(f"         ⚠️ 无法处理坐标对象类型: {type(coords_obj)}")
                        continue
                
                if len(coords) < 2:
                    continue
                
                # 创建新的合并实体
                merged_entity = {
                    'type': 'LINE',
                    'layer': layer_name,
                    'original_layer': layer_name,
                    'points': coords,
                    'start_point': coords[0],
                    'end_point': coords[-1],
                    'merged_from_count': len(line_entities),
                    'merged_by': 'SelectiveLayerProcessor',
                    'processing_type': 'iterative_merge',
                    'merge_timestamp': time.time()
                }
                
                # 继承第一个实体的其他属性
                if line_entities:
                    first_entity = line_entities[0]
                    for key in ['color', 'linetype', 'lineweight']:
                        if key in first_entity:
                            merged_entity[key] = first_entity[key]
                
                merged_entities.append(merged_entity)
            
            print(f"         ✅ 迭代合并完成: {len(line_entities)} -> {len(merged_entities)} 线条")
            return merged_entities
            
        except Exception as e:
            print(f"         ❌ 迭代合并失败: {e}")
            return line_entities
    
    def _deduplicate_entities(self, entities: List[Dict[str, Any]], layer_name: str) -> List[Dict[str, Any]]:
        """去重实体（简单去重方式）"""
        if not entities:
            return []

        print(f"       🔍 开始简单去重处理...")

        # 使用简单的去重方式
        deduplicated = []
        seen_entities = []

        for entity in entities:
            is_duplicate = False

            # 简单比较：检查是否与已有实体相同
            for seen_entity in seen_entities:
                if self._is_simple_duplicate(entity, seen_entity):
                    is_duplicate = True
                    break

            if not is_duplicate:
                # 添加去重标记
                entity_copy = copy.deepcopy(entity)
                entity_copy['processed_by'] = 'SelectiveLayerProcessor'
                entity_copy['processing_type'] = 'simple_deduplication'
                entity_copy['dedup_timestamp'] = time.time()

                deduplicated.append(entity_copy)
                seen_entities.append(entity)

        removed_count = len(entities) - len(deduplicated)
        if removed_count > 0:
            print(f"         🗑️ 简单去重移除实体: {removed_count} 个")

        return deduplicated

    def _is_simple_duplicate(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> bool:
        """简单的重复判断"""
        # 1. 检查基本属性
        if entity1.get('type') != entity2.get('type'):
            return False

        if entity1.get('layer') != entity2.get('layer'):
            return False

        # 2. 检查坐标信息（简单比较）
        if 'points' in entity1 and 'points' in entity2:
            points1 = entity1['points']
            points2 = entity2['points']
            if len(points1) != len(points2):
                return False

            # 简单的坐标比较（允许小的浮点数误差）
            for p1, p2 in zip(points1, points2):
                if isinstance(p1, (list, tuple)) and isinstance(p2, (list, tuple)):
                    if len(p1) >= 2 and len(p2) >= 2:
                        if abs(p1[0] - p2[0]) > 0.01 or abs(p1[1] - p2[1]) > 0.01:
                            return False

        # 3. 检查起点终点（线条实体）
        if 'start_point' in entity1 and 'start_point' in entity2:
            start1, start2 = entity1['start_point'], entity2['start_point']
            end1, end2 = entity1.get('end_point'), entity2.get('end_point')

            if isinstance(start1, (list, tuple)) and isinstance(start2, (list, tuple)):
                if len(start1) >= 2 and len(start2) >= 2:
                    if abs(start1[0] - start2[0]) > 0.01 or abs(start1[1] - start2[1]) > 0.01:
                        return False

            if end1 and end2 and isinstance(end1, (list, tuple)) and isinstance(end2, (list, tuple)):
                if len(end1) >= 2 and len(end2) >= 2:
                    if abs(end1[0] - end2[0]) > 0.01 or abs(end1[1] - end2[1]) > 0.01:
                        return False

        # 4. 检查位置信息（文字、符号等）
        if 'position' in entity1 and 'position' in entity2:
            pos1, pos2 = entity1['position'], entity2['position']
            if isinstance(pos1, (list, tuple)) and isinstance(pos2, (list, tuple)):
                if len(pos1) >= 2 and len(pos2) >= 2:
                    if abs(pos1[0] - pos2[0]) > 0.01 or abs(pos1[1] - pos2[1]) > 0.01:
                        return False

        # 5. 检查圆心（圆形实体）
        if 'center' in entity1 and 'center' in entity2:
            center1, center2 = entity1['center'], entity2['center']
            if isinstance(center1, (list, tuple)) and isinstance(center2, (list, tuple)):
                if len(center1) >= 2 and len(center2) >= 2:
                    if abs(center1[0] - center2[0]) > 0.01 or abs(center1[1] - center2[1]) > 0.01:
                        return False

            # 检查半径
            radius1, radius2 = entity1.get('radius'), entity2.get('radius')
            if radius1 is not None and radius2 is not None:
                if abs(radius1 - radius2) > 0.01:
                    return False

        # 6. 检查文字内容
        if 'text' in entity1 and 'text' in entity2:
            if entity1['text'] != entity2['text']:
                return False

        # 7. 检查颜色（可选）
        color1, color2 = entity1.get('color'), entity2.get('color')
        if color1 is not None and color2 is not None:
            if color1 != color2:
                return False

        # 如果所有检查都通过，认为是重复实体
        return True

    def _calculate_entity_hash(self, entity: Dict[str, Any]) -> str:
        """计算实体特征哈希（备用方法，当前使用简单去重）"""
        # 注意：当前使用简单去重方式，此方法保留备用
        # 简化的哈希计算
        features = [
            entity.get('type', ''),
            entity.get('layer', ''),
            str(entity.get('points', '')),
            str(entity.get('text', '')),
            str(entity.get('color', ''))
        ]

        feature_string = '|'.join(features)
        return hashlib.md5(feature_string.encode('utf-8')).hexdigest()
    
    def _is_line_entity(self, entity: Dict[str, Any]) -> bool:
        """判断是否为线条实体"""
        entity_type = entity.get('type', '').upper()
        return entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']
    
    def _extract_line_coordinates(self, entity: Dict[str, Any]) -> List[List[float]]:
        """提取线条坐标"""
        if 'points' in entity:
            return entity['points']
        elif 'start_point' in entity and 'end_point' in entity:
            return [entity['start_point'], entity['end_point']]
        return None
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'processing_stats': self.processing_stats,
            'wall_layer_patterns': self.wall_layer_patterns,
            'processing_strategy': {
                'wall_layers': 'iterative_merge',
                'other_layers': 'deduplication'
            }
        }
    
    def validate_processing_result(self, original_entities: List[Dict[str, Any]], 
                                 processed_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证处理结果"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 检查图层信息保留
        original_layers = set()
        processed_layers = set()
        
        for entity in original_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                original_layers.add(entity['layer'])
        
        for entity in processed_entities:
            if isinstance(entity, dict) and 'layer' in entity:
                processed_layers.add(entity['layer'])
        
        # 验证图层完整性
        missing_layers = original_layers - processed_layers
        if missing_layers:
            validation['errors'].append(f"丢失图层: {missing_layers}")
            validation['is_valid'] = False
        
        # 统计信息
        validation['statistics'] = {
            'original_entities': len(original_entities),
            'processed_entities': len(processed_entities),
            'original_layers': len(original_layers),
            'processed_layers': len(processed_layers),
            'reduction_rate': (len(original_entities) - len(processed_entities)) / len(original_entities) if original_entities else 0,
            'layer_preservation_rate': len(processed_layers) / len(original_layers) if original_layers else 1.0
        }
        
        return validation


# 工厂函数
def create_selective_processor(distance_threshold=5, angle_threshold=2) -> SelectiveLayerProcessor:
    """创建选择性图层处理器"""
    return SelectiveLayerProcessor(distance_threshold, angle_threshold)

{"test_time": 1753718875.749307, "processor_test_result": {"input_entities": 13, "output_entities": 9, "processing_time": 0.012992620468139648, "input_layer_stats": {"A-WALL": {"total": 3, "types": "{'TEXT', 'LINE'}"}, "WALL-STRUCTURE": {"total": 2, "types": "{'LINE'}"}, "A-DOOR": {"total": 3, "types": "{'LINE'}"}, "A-TEXT": {"total": 3, "types": "{'TEXT'}"}, "EQUIPMENT": {"total": 2, "types": "{'CIRCLE'}"}}, "processing_report": {"summary": {"total_layers": 5, "wall_layers": 2, "other_layers": 3, "total_input_entities": 13, "total_output_entities": 9, "total_processing_time": 0.012992620468139648, "overall_reduction_rate": 0.3076923076923077}, "processing_order": ["A-WALL", "WALL-STRUCTURE", "A-DOOR", "A-TEXT", "EQUIPMENT"], "layer_details": {"A-WALL": {"layer_type": "wall", "input_count": 3, "output_count": 2, "line_entities": 2, "non_line_entities": 1, "merged_lines": 1, "deduplicated_non_lines": 1, "processing_time": 0.003997087478637695, "reduction_count": 1, "processing_method": "iterative_merge + simple_dedup"}, "WALL-STRUCTURE": {"layer_type": "wall", "input_count": 2, "output_count": 2, "line_entities": 2, "non_line_entities": 0, "merged_lines": 2, "deduplicated_non_lines": 0, "processing_time": 0.00299835205078125, "reduction_count": 0, "processing_method": "iterative_merge + simple_dedup"}, "A-DOOR": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "A-TEXT": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "EQUIPMENT": {"layer_type": "other", "input_count": 2, "output_count": 1, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}}}, "validation": {"is_valid": true, "errors": [], "warnings": [], "layer_integrity": {"original_layers": 5, "processed_layers": 5, "missing_layers": [], "preservation_rate": 1.0}, "processing_integrity": {"processing_stages": ["final_output"], "layer_processing_types": ["simple_deduplication", "wall_iterative_merge"], "all_entities_processed": true, "processing_types_present": true}, "statistics": {"original_entities": 13, "processed_entities": 9, "reduction_count": 4, "reduction_rate": 0.3076923076923077}}}, "manager_test_result": {"success": true, "entities": [{"type": "LINE", "layer": "A-WALL", "original_layer": "A-WALL", "points": [[0.0, 0.0], [20.0, 0.0]], "start_point": [0.0, 0.0], "end_point": [20.0, 0.0], "merged_from_count": 2, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753718875.7822883, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "wall1_text", "type": "TEXT", "layer": "A-WALL", "text": "墙体标注", "position": [10, 5], "color": 1, "original_layer": "A-WALL", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.7832878, "dedup_context": "wall_non_line", "layer_processing_type": "wall_iterative_merge", "processed_layer": "A-WALL", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "WALL-STRUCTURE", "original_layer": "WALL-STRUCTURE", "points": [[0, 20], [15, 20]], "start_point": [0, 20], "end_point": [15, 20], "merged_from_count": 2, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753718875.7862859, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "WALL-STRUCTURE", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"type": "LINE", "layer": "WALL-STRUCTURE", "original_layer": "WALL-STRUCTURE", "points": [[15, 20], [30, 20]], "start_point": [15, 20], "end_point": [30, 20], "merged_from_count": 2, "merged_by": "IndependentLayerProcessor", "processing_type": "iterative_merge", "merge_timestamp": 1753718875.7862859, "processing_stage": "final_output", "color": 1, "layer_processing_type": "wall_iterative_merge", "processed_layer": "WALL-STRUCTURE", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "door1", "type": "LINE", "layer": "A-DOOR", "start_point": [5, 0], "end_point": [8, 0], "color": 2, "points": [[5, 0], [8, 0]], "original_layer": "A-DOOR", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.7872856, "dedup_context": "other_layer", "layer_processing_type": "simple_deduplication", "processed_layer": "A-DOOR", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "door2", "type": "LINE", "layer": "A-DOOR", "start_point": [15, 0], "end_point": [18, 0], "color": 2, "points": [[15, 0], [18, 0]], "original_layer": "A-DOOR", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.7872856, "dedup_context": "other_layer", "layer_processing_type": "simple_deduplication", "processed_layer": "A-DOOR", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "text1", "type": "TEXT", "layer": "A-TEXT", "text": "房间1", "position": [25, 10], "height": 3, "color": 3, "original_layer": "A-TEXT", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.788285, "dedup_context": "other_layer", "layer_processing_type": "simple_deduplication", "processed_layer": "A-TEXT", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "text2", "type": "TEXT", "layer": "A-TEXT", "text": "房间2", "position": [35, 10], "height": 3, "color": 3, "original_layer": "A-TEXT", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.788285, "dedup_context": "other_layer", "layer_processing_type": "simple_deduplication", "processed_layer": "A-TEXT", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}, {"id": "equipment1", "type": "CIRCLE", "layer": "EQUIPMENT", "center": [40, 15], "radius": 3, "color": 4, "original_layer": "EQUIPMENT", "processing_stage": "final_output", "processed_by": "IndependentLayerProcessor", "processing_type": "simple_deduplication", "dedup_timestamp": 1753718875.788285, "dedup_context": "other_layer", "layer_processing_type": "simple_deduplication", "processed_layer": "EQUIPMENT", "output_timestamp": 1753718875.7892845, "processed_by_system": "IndependentLayerProcessor"}], "mode_info": "独立图层处理: 5个图层独立处理", "processing_stats": {"processing_stats": {"total_entities_input": 13, "total_entities_output": 9, "total_layers_processed": 5, "wall_layers_count": 2, "other_layers_count": 3, "processing_time": 0.011992931365966797, "layer_processing_order": ["A-WALL", "WALL-STRUCTURE", "A-DOOR", "A-TEXT", "EQUIPMENT"], "layer_details": {"A-WALL": {"layer_type": "wall", "input_count": 3, "output_count": 2, "line_entities": 2, "non_line_entities": 1, "merged_lines": 1, "deduplicated_non_lines": 1, "processing_time": 0.0049970149993896484, "reduction_count": 1, "processing_method": "iterative_merge + simple_dedup"}, "WALL-STRUCTURE": {"layer_type": "wall", "input_count": 2, "output_count": 2, "line_entities": 2, "non_line_entities": 0, "merged_lines": 2, "deduplicated_non_lines": 0, "processing_time": 0.0029981136322021484, "reduction_count": 0, "processing_method": "iterative_merge + simple_dedup"}, "A-DOOR": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "A-TEXT": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "EQUIPMENT": {"layer_type": "other", "input_count": 2, "output_count": 1, "processing_time": 0.00099945068359375, "reduction_count": 1, "processing_method": "simple_deduplication"}}}, "wall_layer_patterns": ["wall", "墙", "a-wall", "arch-wall", "a-wall-", "wall-"], "processing_strategy": {"wall_layers": "iterative_merge", "other_layers": "simple_deduplication", "layer_independence": true}}, "processing_report": {"summary": {"total_layers": 5, "wall_layers": 2, "other_layers": 3, "total_input_entities": 13, "total_output_entities": 9, "total_processing_time": 0.011992931365966797, "overall_reduction_rate": 0.3076923076923077}, "processing_order": ["A-WALL", "WALL-STRUCTURE", "A-DOOR", "A-TEXT", "EQUIPMENT"], "layer_details": {"A-WALL": {"layer_type": "wall", "input_count": 3, "output_count": 2, "line_entities": 2, "non_line_entities": 1, "merged_lines": 1, "deduplicated_non_lines": 1, "processing_time": 0.0049970149993896484, "reduction_count": 1, "processing_method": "iterative_merge + simple_dedup"}, "WALL-STRUCTURE": {"layer_type": "wall", "input_count": 2, "output_count": 2, "line_entities": 2, "non_line_entities": 0, "merged_lines": 2, "deduplicated_non_lines": 0, "processing_time": 0.0029981136322021484, "reduction_count": 0, "processing_method": "iterative_merge + simple_dedup"}, "A-DOOR": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "A-TEXT": {"layer_type": "other", "input_count": 3, "output_count": 2, "processing_time": 0.0, "reduction_count": 1, "processing_method": "simple_deduplication"}, "EQUIPMENT": {"layer_type": "other", "input_count": 2, "output_count": 1, "processing_time": 0.00099945068359375, "reduction_count": 1, "processing_method": "simple_deduplication"}}}, "validation": {"is_valid": true, "errors": [], "warnings": [], "layer_integrity": {"original_layers": 5, "processed_layers": 5, "missing_layers": [], "preservation_rate": 1.0}, "processing_integrity": {"processing_stages": ["final_output"], "layer_processing_types": ["simple_deduplication", "wall_iterative_merge"], "all_entities_processed": true, "processing_types_present": true}, "statistics": {"original_entities": 13, "processed_entities": 9, "reduction_count": 4, "reduction_rate": 0.3076923076923077}}, "processing_time": 0.013991594314575195, "processing_mode": "independent"}, "summary": {"total_test_time": 0.042975664138793945, "processor_test_success": true, "manager_test_success": true, "all_tests_passed": true, "processor_processing_time": 0.012992620468139648, "manager_processing_time": 0.013991594314575195, "performance_excellent": true}}